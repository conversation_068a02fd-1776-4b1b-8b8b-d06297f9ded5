import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class StorageService {
  Future<List<Map<String, dynamic>>> loadServers() async {
    final prefs = await SharedPreferences.getInstance();
    final serversJson = prefs.getStringList('servers') ?? [];
    return serversJson
        .map((e) => Map<String, dynamic>.from(jsonDecode(e)))
        .toList();
  }

  Future<void> saveServers(List<Map<String, dynamic>> servers) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> serversJson = servers
        .map((server) => jsonEncode(server))
        .toList();
    await prefs.setStringList('servers', serversJson);
  }
}
