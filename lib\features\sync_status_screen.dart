import 'package:flutter/material.dart';
import '../services/supabase_sync_test.dart';

/// شاشة عرض حالة المزامنة مع Supabase
class SyncStatusScreen extends StatefulWidget {
  const SyncStatusScreen({super.key});

  @override
  State<SyncStatusScreen> createState() => _SyncStatusScreenState();
}

class _SyncStatusScreenState extends State<SyncStatusScreen> {
  SyncTestResult? _lastResult;
  bool _isRunningTest = false;

  @override
  void initState() {
    super.initState();
    _loadLastResult();
  }

  Future<void> _loadLastResult() async {
    final result = await SupabaseSyncTest.getLastSyncTestResult();
    setState(() {
      _lastResult = result;
    });
  }

  Future<void> _runSyncTest() async {
    setState(() {
      _isRunningTest = true;
    });

    try {
      final result = await SupabaseSyncTest.performFullSyncTest();
      setState(() {
        _lastResult = result;
        _isRunningTest = false;
      });

      if (result.overallSuccess) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ اختبار المزامنة مكتمل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ فشل اختبار المزامنة: ${result.errorMessage}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isRunningTest = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تشغيل الاختبار: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حالة المزامنة'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _isRunningTest ? null : _runSyncTest,
            icon: _isRunningTest
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_lastResult == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.sync_problem, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'لا توجد نتائج اختبار سابقة',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 16),
            Text(
              'اضغط على زر التحديث لإجراء اختبار',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _runSyncTest,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildOverallStatusCard(),
          const SizedBox(height: 16),
          _buildDetailedResultsCard(),
          const SizedBox(height: 16),
          _buildLastTestInfoCard(),
        ],
      ),
    );
  }

  Widget _buildOverallStatusCard() {
    final isSuccess = _lastResult!.overallSuccess;
    
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.error,
              size: 64,
              color: isSuccess ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 12),
            Text(
              isSuccess ? 'المزامنة تعمل بشكل طبيعي' : 'توجد مشكلة في المزامنة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: isSuccess ? Colors.green : Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            if (!isSuccess && _lastResult!.errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                _lastResult!.errorMessage!,
                style: const TextStyle(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedResultsCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الاختبار',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildTestItem(
              'الاتصال بالإنترنت',
              _lastResult!.internetConnection,
              Icons.wifi,
            ),
            _buildTestItem(
              'جلسة Supabase',
              _lastResult!.supabaseSession,
              Icons.security,
            ),
            _buildTestItem(
              'الوصول لقاعدة البيانات',
              _lastResult!.databaseAccess,
              Icons.storage,
            ),
            _buildTestItem(
              'مزامنة البيانات',
              _lastResult!.dataSyncTest,
              Icons.sync,
            ),
            _buildTestItem(
              'سلامة البيانات',
              _lastResult!.dataIntegrity,
              Icons.verified,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestItem(String title, bool success, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: success ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(fontSize: 16),
            ),
          ),
          Icon(
            success ? Icons.check_circle : Icons.cancel,
            color: success ? Colors.green : Colors.red,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildLastTestInfoCard() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات الاختبار',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_lastResult!.completedAt != null) ...[
              Row(
                children: [
                  const Icon(Icons.access_time, size: 20, color: Colors.grey),
                  const SizedBox(width: 8),
                  Text(
                    'آخر اختبار: ${_formatDateTime(_lastResult!.completedAt!)}',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                const Icon(Icons.info, size: 20, color: Colors.grey),
                const SizedBox(width: 8),
                const Text(
                  'يتم إجراء اختبار تلقائي كل 30 دقيقة',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}
