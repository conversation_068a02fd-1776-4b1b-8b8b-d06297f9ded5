# 🔗 إعداد Deep Links لاستعادة كلمة المرور

## 🎯 **المشكلة:**
روابط استعادة كلمة المرور من Supabase لا تعمل في التطبيق وتظهر "تعذر الاتصال".

## ✅ **الحل المطبق:**

### **1️⃣ إضافة app_links dependency:**
```yaml
dependencies:
  app_links: ^6.3.2
```

### **2️⃣ إعداد Android Manifest:**
```xml
<!-- في android/app/src/main/AndroidManifest.xml -->

<!-- Deep Links for Supabase -->
<intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="https"
          android:host="iwtvsvfqmafsziqnoekm.supabase.co" />
</intent-filter>

<!-- Custom scheme for app -->
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="itower" />
</intent-filter>
```

### **3️⃣ إنشاء خدمة Deep Links:**
- **الملف:** `lib/services/deep_link_service.dart`
- **الوظيفة:** معالجة الروابط الواردة وتوجيهها للشاشة المناسبة

### **4️⃣ تحديث AuthService:**
```dart
await _supabase.auth.resetPasswordForEmail(
  email,
  redirectTo: 'itower://reset-password', // Custom scheme
);
```

### **5️⃣ تهيئة Deep Links في التطبيق:**
```dart
// في SimpleRootScreen
WidgetsBinding.instance.addPostFrameCallback((_) {
  DeepLinkService().initialize(context);
});
```

## 🔧 **إعدادات Supabase المطلوبة:**

### **في لوحة تحكم Supabase:**
1. اذهب إلى **Authentication > URL Configuration**
2. أضف في **Redirect URLs:**
   ```
   itower://reset-password
   https://iwtvsvfqmafsziqnoekm.supabase.co/auth/v1/callback
   ```

## 🚀 **كيفية العمل:**

### **التدفق الجديد:**
```
1. المستخدم يطلب استعادة كلمة المرور
2. Supabase يرسل بريد إلكتروني مع رابط
3. الرابط يحتوي على: itower://reset-password
4. عند الضغط: يفتح التطبيق
5. DeepLinkService يعالج الرابط
6. يستخرج access_token و refresh_token
7. يعين الجلسة في Supabase
8. ينتقل لشاشة تحديث كلمة المرور
```

## 🧪 **اختبار الحل:**

### **خطوات الاختبار:**
1. `flutter clean && flutter pub get`
2. `flutter run`
3. اذهب لشاشة تسجيل الدخول
4. اضغط "نسيت كلمة المرور؟"
5. أدخل بريدك الإلكتروني
6. افحص البريد الإلكتروني
7. اضغط على الرابط
8. يجب أن يفتح التطبيق ويذهب لشاشة تحديث كلمة المرور

## 🛠️ **استكشاف الأخطاء:**

### **إذا لم يعمل الرابط:**
1. تأكد من إضافة Redirect URLs في Supabase
2. تحقق من Android Manifest
3. أعد تشغيل التطبيق بعد التغييرات
4. تحقق من logs في Debug Console

### **رسائل Debug مفيدة:**
```
🔗 [DEEP_LINK] تم استلام رابط: ...
🔍 [SUPABASE_LINK] معالجة رابط Supabase
✅ [PASSWORD_RECOVERY] تم تعيين الجلسة بنجاح
```

## 📱 **ملاحظات مهمة:**

### **للـ iOS (إضافي):**
```xml
<!-- في ios/Runner/Info.plist -->
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>itower.deeplink</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>itower</string>
        </array>
    </dict>
</array>
```

### **للإنتاج:**
- تأكد من تحديث Redirect URLs في Supabase Production
- اختبر على أجهزة حقيقية
- تأكد من App Store/Play Store approval للـ Deep Links

## 🎉 **النتيجة:**
الآن روابط استعادة كلمة المرور ستعمل بشكل صحيح وتفتح التطبيق مباشرة!
