-- تحديث نظام المزامنة ليصبح ديناميكي مع حذف النسخ القديمة تلقائياً
-- يجب تشغيل هذا السكريبت في Supabase SQL Editor

-- 1. إنشاء دالة فحص الحدود الديناميكية الجديدة
CREATE OR REPLACE FUNCTION check_sync_limits_dynamic(
  target_user_id UUID,
  backup_size_mb INTEGER
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  limits_info RECORD;
  current_backups INTEGER;
  can_sync BOOLEAN := true;
  reasons TEXT[] := '{}';
  cleanup_performed BOOLEAN := false;
  deleted_count INTEGER := 0;
BEGIN
  -- الحصول على حدود المستخدم
  SELECT * INTO limits_info
  FROM user_sync_limits
  WHERE user_id = target_user_id;

  -- إنشاء حدود افتراضية إذا لم توجد
  IF NOT FOUND THEN
    INSERT INTO user_sync_limits (user_id) VALUES (target_user_id);
    SELECT * INTO limits_info FROM user_sync_limits WHERE user_id = target_user_id;
  END IF;

  -- فحص عدد النسخ الحالية
  SELECT COUNT(*) INTO current_backups
  FROM compressed_backups
  WHERE user_id = target_user_id;

  -- 🔄 المزامنة الديناميكية: حذف النسخ القديمة تلقائياً عند الوصول للحد الأقصى
  IF current_backups >= limits_info.max_backups THEN
    -- تنفيذ تنظيف تلقائي للنسخ القديمة
    DECLARE
      cleanup_result RECORD;
    BEGIN
      -- استدعاء دالة التنظيف
      SELECT * INTO cleanup_result FROM cleanup_old_backups(target_user_id);
      
      deleted_count := cleanup_result.deleted_count;
      cleanup_performed := true;
      
      -- إضافة رسالة توضيحية
      reasons := array_append(reasons, 'تم حذف ' || deleted_count || ' نسخة قديمة تلقائياً لإفساح المجال للنسخة الجديدة');
      
      -- إعادة فحص عدد النسخ بعد التنظيف
      SELECT COUNT(*) INTO current_backups
      FROM compressed_backups
      WHERE user_id = target_user_id;
      
      -- السماح بالمزامنة بعد التنظيف
      can_sync := true;
      
    EXCEPTION WHEN OTHERS THEN
      -- في حالة فشل التنظيف، منع المزامنة
      can_sync := false;
      reasons := array_append(reasons, 'فشل في تنظيف النسخ القديمة: ' || SQLERRM);
    END;
  END IF;

  -- فحص حجم النسخة الاحتياطية
  IF backup_size_mb > limits_info.max_backup_size_mb THEN
    can_sync := false;
    reasons := array_append(reasons, 'حجم النسخة الاحتياطية يتجاوز الحد المسموح (' || limits_info.max_backup_size_mb || ' MB)');
  END IF;

  -- فحص مساحة التخزين المتاحة
  IF (limits_info.current_storage_used_mb + backup_size_mb) > limits_info.total_storage_limit_mb THEN
    can_sync := false;
    reasons := array_append(reasons, 'لا توجد مساحة تخزين كافية');
  END IF;

  -- فحص المزامنات اليومية
  IF limits_info.last_sync_date = CURRENT_DATE AND limits_info.syncs_today >= limits_info.max_daily_syncs THEN
    can_sync := false;
    reasons := array_append(reasons, 'تم الوصول للحد الأقصى من المزامنات اليومية (' || limits_info.max_daily_syncs || ')');
  END IF;

  RETURN jsonb_build_object(
    'can_sync', can_sync,
    'reasons', reasons,
    'current_backups', current_backups,
    'max_backups', limits_info.max_backups,
    'storage_used_mb', limits_info.current_storage_used_mb,
    'storage_limit_mb', limits_info.total_storage_limit_mb,
    'syncs_today', CASE WHEN limits_info.last_sync_date = CURRENT_DATE THEN limits_info.syncs_today ELSE 0 END,
    'max_daily_syncs', limits_info.max_daily_syncs,
    'cleanup_performed', cleanup_performed,
    'deleted_old_backups', deleted_count
  );
END;
$$;

-- 2. تحديث دالة cleanup_old_backups لتعيد معلومات أكثر تفصيلاً
CREATE OR REPLACE FUNCTION cleanup_old_backups(target_user_id UUID)
RETURNS TABLE(
  deleted_count INTEGER,
  freed_space_mb DECIMAL,
  deleted_files TEXT[]
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_limit INTEGER;
  backup_record RECORD;
  total_deleted INTEGER := 0;
  total_freed BIGINT := 0;
  deleted_file_paths TEXT[] := '{}';
BEGIN
  -- الحصول على حد المستخدم
  SELECT max_backups INTO user_limit
  FROM user_sync_limits
  WHERE user_id = target_user_id;
  
  -- استخدام الحد الافتراضي إذا لم يوجد
  user_limit := COALESCE(user_limit, 5);
  
  -- حذف النسخ الزائدة (الاحتفاظ بـ max_backups - 1 لإفساح المجال للنسخة الجديدة)
  FOR backup_record IN (
    SELECT id, file_path, compressed_size
    FROM compressed_backups 
    WHERE user_id = target_user_id 
    ORDER BY created_at DESC 
    OFFSET (user_limit - 1)
  ) LOOP
    -- إضافة معلومات الملف المحذوف
    deleted_file_paths := array_append(deleted_file_paths, backup_record.file_path);
    total_freed := total_freed + backup_record.compressed_size;
    
    -- حذف السجل من قاعدة البيانات
    DELETE FROM compressed_backups WHERE id = backup_record.id;
    total_deleted := total_deleted + 1;
  END LOOP;
  
  -- تحديث استخدام التخزين
  UPDATE user_sync_limits 
  SET 
    current_storage_used_mb = GREATEST(0, current_storage_used_mb - (total_freed / 1024 / 1024)),
    updated_at = NOW()
  WHERE user_id = target_user_id;
  
  -- إرجاع النتائج
  RETURN QUERY SELECT total_deleted, (total_freed::DECIMAL / 1024 / 1024), deleted_file_paths;
END;
$$;

-- 3. منح الصلاحيات للدالة الجديدة
GRANT EXECUTE ON FUNCTION check_sync_limits_dynamic TO authenticated;

-- 4. رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE '✅ تم تحديث نظام المزامنة ليصبح ديناميكي!';
    RAISE NOTICE '🔄 الآن سيتم حذف النسخ القديمة تلقائياً عند الوصول للحد الأقصى';
    RAISE NOTICE '📊 الدالة الجديدة: check_sync_limits_dynamic';
    RAISE NOTICE '🧹 دالة التنظيف المحدثة: cleanup_old_backups';
END $$;
