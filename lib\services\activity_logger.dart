// خدمة تسجيل نشاط الموظفين في العمليات

import '../db_helper.dart';
import '../services/employee_service.dart';
import '../models/employee_models.dart';

class ActivityLogger {
  /// تسجيل عملية إضافة مشترك
  static Future<void> logAddSubscriber({
    required String subscriberName,
    required String subscriberId,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'إضافة مشترك',
        details: 'تم إضافة المشترك: $subscriberName (ID: $subscriberId)',
      );
    }
  }

  /// تسجيل عملية تعديل مشترك
  static Future<void> logEditSubscriber({
    required String subscriberName,
    required String subscriberId,
    required String changes,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'تعديل مشترك',
        details: 'تم تعديل المشترك: $subscriberName (ID: $subscriberId)\nالتغييرات: $changes',
      );
    }
  }

  /// تسجيل عملية تجديد اشتراك
  static Future<void> logRenewSubscription({
    required String subscriberName,
    required String packageName,
    required double amount,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'تجديد اشتراك',
        details: 'تم تجديد اشتراك المشترك: $subscriberName\nالباقة: $packageName\nالمبلغ: $amount',
      );
    }
  }

  /// تسجيل عملية تسديد دين
  static Future<void> logPayDebt({
    required String subscriberName,
    required double amount,
    required double remainingDebt,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'تسديد دين',
        details: 'تم تسديد دين المشترك: $subscriberName\nالمبلغ المسدد: $amount\nالدين المتبقي: $remainingDebt',
      );
    }
  }

  /// تسجيل عملية إضافة معاملة
  static Future<void> logAddTransaction({
    required String transactionType,
    required double amount,
    required String description,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'إضافة معاملة',
        details: 'نوع المعاملة: $transactionType\nالمبلغ: $amount\nالوصف: $description',
      );
    }
  }

  /// تسجيل عملية حذف سجل
  static Future<void> logDeleteRecord({
    required String recordType,
    required String recordId,
    required String recordName,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'حذف سجل',
        details: 'تم حذف $recordType: $recordName (ID: $recordId)',
      );
    }
  }

  /// تسجيل عملية تصدير بيانات
  static Future<void> logExportData({
    required String exportType,
    required String fileName,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'تصدير بيانات',
        details: 'تم تصدير $exportType إلى الملف: $fileName',
      );
    }
  }

  /// تسجيل عملية عرض تقرير
  static Future<void> logViewReport({
    required String reportType,
    required String dateRange,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'عرض تقرير',
        details: 'تم عرض تقرير: $reportType\nالفترة: $dateRange',
      );
    }
  }

  /// تسجيل عملية إدارة جهاز
  static Future<void> logDeviceManagement({
    required String action,
    required String deviceName,
    required String details,
  }) async {
    final session = await EmployeeService.getCurrentSession();
    if (session != null) {
      await _logActivity(
        employeeId: session.employeeId,
        action: 'إدارة جهاز',
        details: 'العملية: $action\nالجهاز: $deviceName\nالتفاصيل: $details',
      );
    }
  }

  /// الحصول على سجل نشاط موظف معين
  static Future<List<EmployeeActivityLog>> getEmployeeActivityLogs({
    required int employeeId,
    int? limit,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await DBHelper.instance.database;
    
    String whereClause = 'employee_id = ?';
    List<dynamic> whereArgs = [employeeId];
    
    if (fromDate != null) {
      whereClause += ' AND timestamp >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }
    
    if (toDate != null) {
      whereClause += ' AND timestamp <= ?';
      whereArgs.add(toDate.toIso8601String());
    }
    
    final result = await db.query(
      'employee_activity_logs',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: 'timestamp DESC',
      limit: limit,
    );
    
    return result.map((data) => EmployeeActivityLog.fromMap(data)).toList();
  }

  /// الحصول على سجل نشاط جميع الموظفين (للمدير فقط)
  static Future<List<Map<String, dynamic>>> getAllEmployeesActivityLogs({
    int? limit,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await DBHelper.instance.database;
    
    String whereClause = '1=1';
    List<dynamic> whereArgs = [];
    
    if (fromDate != null) {
      whereClause += ' AND logs.timestamp >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }
    
    if (toDate != null) {
      whereClause += ' AND logs.timestamp <= ?';
      whereArgs.add(toDate.toIso8601String());
    }
    
    final result = await db.rawQuery('''
      SELECT 
        logs.*,
        employees.name as employee_name,
        employees.email as employee_email
      FROM employee_activity_logs logs
      LEFT JOIN employees ON logs.employee_id = employees.id
      WHERE $whereClause
      ORDER BY logs.timestamp DESC
      ${limit != null ? 'LIMIT $limit' : ''}
    ''', whereArgs);
    
    return result;
  }

  /// إحصائيات نشاط الموظفين
  static Future<Map<String, dynamic>> getActivityStatistics({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await DBHelper.instance.database;
    
    String whereClause = '1=1';
    List<dynamic> whereArgs = [];
    
    if (fromDate != null) {
      whereClause += ' AND timestamp >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }
    
    if (toDate != null) {
      whereClause += ' AND timestamp <= ?';
      whereArgs.add(toDate.toIso8601String());
    }
    
    // إجمالي العمليات
    final totalResult = await db.rawQuery('''
      SELECT COUNT(*) as total_operations
      FROM employee_activity_logs
      WHERE $whereClause
    ''', whereArgs);
    
    // العمليات حسب النوع
    final actionResult = await db.rawQuery('''
      SELECT action, COUNT(*) as count
      FROM employee_activity_logs
      WHERE $whereClause
      GROUP BY action
      ORDER BY count DESC
    ''', whereArgs);
    
    // العمليات حسب الموظف
    final employeeResult = await db.rawQuery('''
      SELECT 
        logs.employee_id,
        employees.name as employee_name,
        COUNT(*) as operations_count
      FROM employee_activity_logs logs
      LEFT JOIN employees ON logs.employee_id = employees.id
      WHERE $whereClause
      GROUP BY logs.employee_id, employees.name
      ORDER BY operations_count DESC
    ''', whereArgs);
    
    return {
      'total_operations': totalResult.first['total_operations'] ?? 0,
      'operations_by_action': actionResult,
      'operations_by_employee': employeeResult,
    };
  }

  // دالة مساعدة خاصة لتسجيل النشاط
  static Future<void> _logActivity({
    required int employeeId,
    required String action,
    required String details,
  }) async {
    try {
      final db = await DBHelper.instance.database;
      
      final log = EmployeeActivityLog(
        employeeId: employeeId,
        action: action,
        details: details,
        timestamp: DateTime.now(),
      );

      await db.insert('employee_activity_logs', log.toMap());
    } catch (e) {
      // تسجيل الخطأ ولكن لا نوقف العملية
      print('خطأ في تسجيل النشاط: $e');
    }
  }
}
