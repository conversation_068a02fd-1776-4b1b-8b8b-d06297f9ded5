import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import '../../../db_helper.dart';

typedef Subscription =
    Map<String, dynamic>; // {'name': String, 'price': double}

class SubscriptionPricesScreen extends StatefulWidget {
  const SubscriptionPricesScreen({super.key});
  @override
  State<SubscriptionPricesScreen> createState() =>
      _SubscriptionPricesScreenState();
}

class _SubscriptionPricesScreenState extends State<SubscriptionPricesScreen> {
  List<Subscription> subscriptions = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSubscriptions();
  }

  Future<void> _loadSubscriptions() async {
    setState(() => isLoading = true);
    final list = await DBHelper.instance.getAllSubscriptions();
    setState(() {
      subscriptions = list;
      isLoading = false;
    });
  }

  Future<void> _saveSubscription(Map<String, dynamic> sub, {int? index}) async {
    if (index != null) {
      await DBHelper.instance.updateSubscription(sub);
    } else {
      await DBHelper.instance.insertSubscription(sub);
    }
    await _loadSubscriptions();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الباقة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _addOrEditSubscription({Subscription? sub, int? index}) async {
    final nameController = TextEditingController(text: sub?['name'] ?? '');
    final buyPriceController = TextEditingController(
      text: sub?['buyPrice']?.toString() ?? '',
    );
    final sellPriceController = TextEditingController(
      text: sub?['sellPrice']?.toString() ?? '',
    );
    final customBuyPriceController = TextEditingController(
      text: sub?['custom_sell_price']?.toString() ?? '',
    );

    final result = await showDialog<Subscription>(
      context: context,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Theme.of(ctx).colorScheme.surface,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // رأس الحوار
              Row(
                children: [
                  Icon(
                    sub == null ? Icons.add : Icons.edit,
                    color: Theme.of(ctx).colorScheme.primary,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      sub == null ? 'إضافة باقة جديدة' : 'تعديل الباقة',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(ctx).colorScheme.onSurface,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // حقل اسم الباقة
              _buildDialogField(
                controller: nameController,
                label: 'اسم الباقة',
                icon: Icons.local_offer,
                context: ctx,
              ),

              const SizedBox(height: 16),

              // حقل سعر الشراء
              _buildDialogField(
                controller: buyPriceController,
                label: 'سعر الشراء (د.ع)',
                icon: Icons.shopping_cart,
                keyboardType: TextInputType.number,
                context: ctx,
              ),

              const SizedBox(height: 16),

              // حقل سعر البيع
              _buildDialogField(
                controller: sellPriceController,
                label: 'سعر البيع (د.ع)',
                icon: Icons.sell,
                keyboardType: TextInputType.number,
                context: ctx,
              ),

              const SizedBox(height: 16),

              // حقل السعر المخصص (اختياري)
              _buildDialogField(
                controller: customBuyPriceController,
                label: 'سعر بيع مخصص (اختياري)',
                icon: Icons.sell_rounded,
                keyboardType: TextInputType.number,
                context: ctx,
                hintText: 'اتركه فارغاً لاستخدام سعر البيع الافتراضي',
              ),

              const SizedBox(height: 32),

              // أزرار الحوار
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(ctx).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        final name = nameController.text.trim();
                        final buyPrice =
                            double.tryParse(buyPriceController.text.trim()) ??
                            0;
                        final sellPrice =
                            double.tryParse(sellPriceController.text.trim()) ??
                            0;
                        final customSellPrice =
                            customBuyPriceController.text.trim().isNotEmpty
                            ? double.tryParse(
                                customBuyPriceController.text.trim(),
                              )
                            : null;
                        if (name.isEmpty || buyPrice <= 0 || sellPrice <= 0) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text(
                                'يرجى إدخال جميع البيانات بشكل صحيح',
                              ),
                              backgroundColor: Colors.red,
                            ),
                          );
                          return;
                        }
                        // تأكد أن id نصي دائماً
                        final id =
                            (sub?['id']?.toString() ?? const Uuid().v4());
                        Navigator.of(ctx).pop({
                          'id': id,
                          'name': name,
                          'buyPrice': buyPrice,
                          'sellPrice': sellPrice,
                          'custom_sell_price': customSellPrice,
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(ctx).colorScheme.primary,
                        foregroundColor: Theme.of(ctx).colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'حفظ',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
    if (result != null) {
      await _saveSubscription(result, index: index);
    }
  }

  // الحصول على سعر البيع الفعال (مخصص أو افتراضي)
  double _getEffectiveSellPrice(Map<String, dynamic> package) {
    final customPrice = package['custom_sell_price'];
    if (customPrice != null && customPrice > 0) {
      return customPrice.toDouble();
    }
    return (package['sellPrice'] ?? 0).toDouble();
  }

  // الحصول على سعر الشراء الفعال (مخصص أو افتراضي) - للتوافق
  double _getEffectiveBuyPrice(Map<String, dynamic> package) {
    final customPrice = package['custom_buy_price'];
    if (customPrice != null && customPrice > 0) {
      return customPrice.toDouble();
    }
    return (package['buyPrice'] ?? 0).toDouble();
  }

  // تحديد ما إذا كان يمكن حذف الباقة (الباقات المضافة يدوياً فقط)
  bool _canDeletePackage(Map<String, dynamic> package) {
    // يمكن حذف الباقة إذا كانت مضافة يدوياً (ليست من المزامنة)
    // الباقات من المزامنة لها معرفات رقمية، الباقات اليدوية لها معرفات UUID
    final id = package['id']?.toString() ?? '';

    // إذا كان المعرف فارغ أو يبدأ بـ manual_ أو هو UUID (يحتوي على شرطات)
    return id.isEmpty ||
        id.startsWith('manual_') ||
        id.contains('-'); // UUID format
  }

  // إزالة السعر المخصص والعودة للسعر الافتراضي
  Future<void> _removeCustomPrice(int index) async {
    try {
      final package = subscriptions[index];
      final packageId = package['id'];

      // تحديث قاعدة البيانات لإزالة السعر المخصص
      await DBHelper.instance.updateSubscription({
        'id': packageId,
        'custom_sell_price': null,
      });

      // تحديث القائمة المحلية
      setState(() {
        subscriptions[index]['custom_sell_price'] = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'تم إزالة السعر المخصص، سيتم استخدام السعر الافتراضي',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إزالة السعر المخصص: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // بناء حقل في الحوار
  Widget _buildDialogField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required BuildContext context,
    TextInputType? keyboardType,
    String? hintText,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return TextField(
      controller: controller,
      keyboardType: keyboardType,
      style: TextStyle(color: colorScheme.onSurface, fontSize: 16),
      decoration: InputDecoration(
        labelText: label,
        hintText: hintText,
        prefixIcon: Icon(icon, color: colorScheme.primary),
        filled: true,
        fillColor: colorScheme.primary.withValues(alpha: 0.05),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary.withValues(alpha: 0.2),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary.withValues(alpha: 0.2),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  void _deleteSubscription(int index) async {
    final packageName = subscriptions[index]['name'] ?? 'الباقة';
    final confirm = await showDialog<bool>(
      context: context,
      builder: (ctx) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Theme.of(ctx).colorScheme.surface,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة التحذير
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.warning_rounded,
                  color: Colors.red,
                  size: 32,
                ),
              ),

              const SizedBox(height: 24),

              // عنوان الحوار
              Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(ctx).colorScheme.onSurface,
                ),
              ),

              const SizedBox(height: 16),

              // نص التأكيد
              Text(
                'هل أنت متأكد أنك تريد حذف باقة "$packageName"؟\nلا يمكن التراجع عن هذا الإجراء.',
                style: TextStyle(
                  fontSize: 16,
                  color: Theme.of(
                    ctx,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // أزرار الحوار
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(ctx).pop(false),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(ctx).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'حذف',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
    if (confirm == true) {
      final id = subscriptions[index]['id'];
      await DBHelper.instance.deleteSubscription(id);
      await _loadSubscriptions();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الباقة بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // محتوى الشاشة
                  isLoading
                      ? _buildLoadingState(colorScheme)
                      : subscriptions.isEmpty
                      ? _buildEmptyState(colorScheme, isDark)
                      : _buildPackagesList(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
      child: Column(
        children: [
          // أزرار الإجراءات في العمود الأيسر
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // العمود الأيسر للأزرار
              Column(
                children: [
                  // زر الإضافة (الوحيد)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.add, color: colorScheme.onPrimary),
                      tooltip: 'إضافة باقة',
                      onPressed: () => _addOrEditSubscription(),
                    ),
                  ),
                ],
              ),

              // مساحة فارغة للتوازن
              const SizedBox(width: 48),
            ],
          ),
          const SizedBox(height: 0),

          // الأيقونة الرئيسية في الوسط
          Container(
            margin: const EdgeInsets.only(bottom: 18),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.18),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withValues(
                alpha: isDark ? 0.08 : 0.18,
              ),
              child: Icon(
                Icons.local_offer,
                color: colorScheme.primary,
                size: 54,
              ),
            ),
          ),

          // العنوان والوصف
          Text(
            'الباقات',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
              letterSpacing: 1,
              shadows: [
                Shadow(
                  color: colorScheme.shadow.withValues(alpha: 0.13),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إدارة باقات الاشتراك وأسعارها',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onPrimary.withValues(alpha: 0.92),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء حالة التحميل
  Widget _buildLoadingState(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: 0.7),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: const Padding(
        padding: EdgeInsets.all(64),
        child: Center(child: CircularProgressIndicator()),
      ),
    );
  }

  // بناء حالة عدم وجود باقات
  Widget _buildEmptyState(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.local_offer_outlined,
              size: 64,
              color: colorScheme.primary.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد باقات بعد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'أضف باقة جديدة لبدء إدارة أسعار الاشتراكات',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _addOrEditSubscription(),
              icon: const Icon(Icons.add),
              label: const Text('إضافة باقة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قائمة الباقات
  Widget _buildPackagesList(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        for (int i = 0; i < subscriptions.length; i++)
          _buildPackageCard(subscriptions[i], i, colorScheme, isDark),
      ],
    );
  }

  // بناء بطاقة باقة واحدة
  Widget _buildPackageCard(
    Map<String, dynamic> package,
    int index,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 16),
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            _buildPackageHeader(package, colorScheme),
            const SizedBox(height: 16),

            // معلومات الأسعار
            _buildPriceInfo(package, colorScheme),
            const SizedBox(height: 20),

            // أزرار التحكم
            _buildPackageActions(package, index, colorScheme),
          ],
        ),
      ),
    );
  }

  // بناء رأس بطاقة الباقة
  Widget _buildPackageHeader(
    Map<String, dynamic> package,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        // أيقونة الباقة
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(Icons.local_offer, color: colorScheme.primary, size: 24),
        ),

        const SizedBox(width: 16),

        // اسم الباقة
        Expanded(
          child: Text(
            package['name'] ?? '',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        // مؤشر الباقة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Text(
            'نشطة',
            style: TextStyle(
              color: Colors.green,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  // بناء معلومات الأسعار
  Widget _buildPriceInfo(
    Map<String, dynamic> package,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          // سعر الشراء (افتراضي من السيرفر)
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'سعر الشراء',
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                // عرض سعر الشراء الافتراضي من السيرفر
                Text(
                  '${(package['buyPrice'] ?? 0).toDouble()} د.ع',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ),

          // خط فاصل
          Container(
            width: 1,
            height: 40,
            color: colorScheme.primary.withValues(alpha: 0.2),
          ),

          // سعر البيع
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'سعر البيع',
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                // عرض سعر البيع المخصص إذا كان موجوداً، وإلا السعر الافتراضي
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${_getEffectiveSellPrice(package)} د.ع',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 2),
                    // مؤشر نوع السعر
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 4,
                        vertical: 1,
                      ),
                      decoration: BoxDecoration(
                        color:
                            (package['custom_sell_price'] != null &&
                                package['custom_sell_price'] > 0)
                            ? Colors.blue.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(3),
                        border: Border.all(
                          color:
                              (package['custom_sell_price'] != null &&
                                  package['custom_sell_price'] > 0)
                              ? Colors.blue.withValues(alpha: 0.3)
                              : Colors.grey.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        (package['custom_sell_price'] != null &&
                                package['custom_sell_price'] > 0)
                            ? 'مخصص'
                            : 'افتراضي',
                        style: TextStyle(
                          fontSize: 7,
                          color:
                              (package['custom_sell_price'] != null &&
                                  package['custom_sell_price'] > 0)
                              ? Colors.blue
                              : Colors.grey,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء أزرار التحكم في الباقة
  Widget _buildPackageActions(
    Map<String, dynamic> package,
    int index,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        // زر التعديل
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _addOrEditSubscription(sub: package, index: index),
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('تعديل'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              foregroundColor: Colors.blue,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),

        const SizedBox(width: 12),

        // زر الحذف (يظهر فقط للباقات المضافة يدوياً أو التي لها سعر مخصص)
        if (_canDeletePackage(package))
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _deleteSubscription(index),
              icon: const Icon(Icons.delete, size: 18),
              label: const Text('حذف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.withValues(alpha: 0.1),
                foregroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          )
        else
        // زر إزالة السعر المخصص (للباقات من المزامنة التي لها سعر مخصص)
        if (package['custom_sell_price'] != null &&
            package['custom_sell_price'] > 0)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _removeCustomPrice(index),
              icon: const Icon(Icons.restore, size: 18),
              label: const Text('إزالة السعر المخصص'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.withValues(alpha: 0.1),
                foregroundColor: Colors.orange,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
      ],
    );
  }
}
