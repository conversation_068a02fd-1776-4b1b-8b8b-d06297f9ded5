-- إصلاح قيود subscription_type في جدول user_accounts

-- 1. حذف القيد القديم
ALTER TABLE user_accounts 
DROP CONSTRAINT IF EXISTS user_accounts_subscription_type_check;

-- 2. إنشاء قيد جديد يدعم القيم المطلوبة (متوافق مع قاعدة البيانات الحالية)
ALTER TABLE user_accounts
ADD CONSTRAINT user_accounts_subscription_type_check
CHECK (subscription_type IN ('trial', 'monthly', 'quarterly', 'semi_annual', 'annual'));

-- 3. تحديث أي قيم موجودة بالعربية إلى الإنجليزية
UPDATE user_accounts
SET subscription_type = CASE
  WHEN subscription_type = 'باقة شهرية' OR subscription_type = 'شهرية' THEN 'monthly'
  WHEN subscription_type = 'باقة ربع سنوية' OR subscription_type = 'ربع سنوية' THEN 'quarterly'
  WHEN subscription_type = 'باقة نصف سنوية' OR subscription_type = 'نصف سنوية' THEN 'semi_annual'
  WHEN subscription_type = 'باقة سنوية' OR subscription_type = 'سنوية' OR subscription_type = 'yearly' THEN 'annual'
  WHEN subscription_type = 'تجريبي' THEN 'trial'
  ELSE 'trial'
END
WHERE subscription_type NOT IN ('trial', 'monthly', 'quarterly', 'semi_annual', 'annual');

-- 4. عرض النتائج
SELECT 
  subscription_type,
  COUNT(*) as count
FROM user_accounts 
GROUP BY subscription_type;

-- تأكيد إنجاز الإصلاح
SELECT 'تم إصلاح قيود subscription_type بنجاح!' as status;
