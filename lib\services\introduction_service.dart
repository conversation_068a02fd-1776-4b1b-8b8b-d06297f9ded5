import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class IntroductionService {
  static const String _hasSeenIntroKey = 'has_seen_introduction';
  static const String _appVersionKey = 'app_version_seen_intro';

  /// التحقق من أن المستخدم شاهد الاستعراض من قبل
  static Future<bool> hasSeenIntroduction() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_hasSeenIntroKey) ?? false;
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة الاستعراض: $e');
      return false;
    }
  }

  /// تسجيل أن المستخدم شاهد الاستعراض
  static Future<void> markIntroductionAsSeen() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_hasSeenIntroKey, true);
      await prefs.setString(
        _appVersionKey,
        '1.0.0',
      ); // يمكن تحديثه حسب إصدار التطبيق
      debugPrint('تم تسجيل مشاهدة الاستعراض');
    } catch (e) {
      debugPrint('خطأ في تسجيل مشاهدة الاستعراض: $e');
    }
  }

  /// إعادة تعيين حالة الاستعراض (للاختبار أو التحديثات الجديدة)
  static Future<void> resetIntroduction() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_hasSeenIntroKey);
      await prefs.remove(_appVersionKey);
      debugPrint('تم إعادة تعيين حالة الاستعراض');
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين الاستعراض: $e');
    }
  }

  /// التحقق من الحاجة لعرض الاستعراض بناءً على إصدار التطبيق
  static Future<bool> shouldShowIntroductionForVersion(
    String currentVersion,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasSeenIntro = prefs.getBool(_hasSeenIntroKey) ?? false;
      final lastVersionSeen = prefs.getString(_appVersionKey) ?? '';

      // إذا لم يشاهد الاستعراض من قبل أو كان الإصدار مختلف
      return !hasSeenIntro || lastVersionSeen != currentVersion;
    } catch (e) {
      debugPrint('خطأ في التحقق من إصدار الاستعراض: $e');
      return true; // في حالة الخطأ، اعرض الاستعراض للأمان
    }
  }

  /// حفظ إصدار التطبيق الحالي
  static Future<void> saveCurrentVersion(String version) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_appVersionKey, version);
    } catch (e) {
      debugPrint('خطأ في حفظ إصدار التطبيق: $e');
    }
  }
}
