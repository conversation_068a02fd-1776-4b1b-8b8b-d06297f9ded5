import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'user_presence_service.dart';
import '../core/managers/unified_account_status_manager.dart';
import '../core/models/unified_account_model.dart';

/// خدمة كشف الحسابات المحذوفة وتسجيل الخروج الفوري
class DeletedAccountDetector {
  static Timer? _checkTimer;
  static bool _isChecking = false;
  static const Duration _checkInterval = Duration(
    minutes: 2,
  ); // ✅ فحص كل دقيقتين

  /// بدء مراقبة الحساب المحذوف
  static void startMonitoring() {
    if (_checkTimer != null) return;

    debugPrint('🔍 [DELETED_ACCOUNT] بدء مراقبة الحساب المحذوف...');

    // فحص فوري
    _checkAccountStatus();

    // فحص دوري كل دقيقتين
    _checkTimer = Timer.periodic(_checkInterval, (timer) {
      _checkAccountStatus();
    });
  }

  /// إيقاف المراقبة
  static void stopMonitoring() {
    _checkTimer?.cancel();
    _checkTimer = null;
    _isChecking = false;
    debugPrint('⏹️ [DELETED_ACCOUNT] تم إيقاف مراقبة الحساب');
  }

  /// فحص حالة الحساب (محدث للنظام الجديد - يستخدم AccountStatusManager)
  static Future<void> _checkAccountStatus() async {
    if (_isChecking) return;
    _isChecking = true;

    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('ℹ️ [ACCOUNT_STATUS] لا يوجد مستخدم مسجل دخول');
        _isChecking = false;
        return;
      }

      debugPrint(
        '🔍 [ACCOUNT_STATUS] فحص حالة الحساب باستخدام النظام الموحد: ${user.id}',
      );

      // تحديث معرف المستخدم في النظام الموحد
      await UnifiedAccountStatusManager.updateUserId(user.id);

      // جلب الحالة من النظام الموحد
      final accountStatus = UnifiedAccountStatusManager.currentStatus;

      if (accountStatus == null) {
        debugPrint('🚨 [ACCOUNT_STATUS] لا توجد بيانات حساب - قد يكون محذوف!');
        await _immediateLogout();
        return;
      }

      debugPrint('📊 [ACCOUNT_STATUS] حالة الحساب: ${accountStatus.status}');

      // التعامل مع حالات الحساب المختلفة باستخدام النظام الجديد
      await _handleAccountStatusNew(accountStatus);
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في فحص حالة الحساب: $e');

      // فحص إذا كان الخطأ يشير لحساب محذوف
      if (_isAccountDeletedError(e)) {
        debugPrint(
          '🚨 [ACCOUNT_STATUS] خطأ يشير لحساب محذوف - تسجيل خروج فوري',
        );
        await _immediateLogout();
      }
    } finally {
      _isChecking = false;
    }
  }

  /// التعامل مع حالات الحساب المختلفة (النظام الجديد)
  static Future<void> _handleAccountStatusNew(
    UnifiedAccountModel accountStatus,
  ) async {
    switch (accountStatus.status) {
      case AccountStatus.banned:
        debugPrint('🚨 [ACCOUNT_STATUS] الحساب محظور! تسجيل خروج فوري...');
        await _immediateLogout();
        break;

      case AccountStatus.expired:
        debugPrint(
          '⚠️ [ACCOUNT_STATUS] الحساب منتهي الصلاحية - إيقاف الخدمات...',
        );
        await _stopServicesForExpiredAccount();
        break;

      case AccountStatus.trial:
        if (accountStatus.trialDaysRemaining <= 0) {
          debugPrint(
            '⚠️ [ACCOUNT_STATUS] انتهت الفترة التجريبية - إيقاف الخدمات...',
          );
          await _stopServicesForExpiredAccount();
        } else {
          debugPrint(
            '✅ [ACCOUNT_STATUS] حساب تجريبي نشط - ${accountStatus.trialDaysRemaining} أيام متبقية',
          );
        }
        break;

      case AccountStatus.active:
        debugPrint('✅ [ACCOUNT_STATUS] حساب مفعل ونشط');
        break;

      case AccountStatus.suspended:
      case AccountStatus.locked:
        debugPrint('⚠️ [ACCOUNT_STATUS] الحساب موقوف/مقفل - إيقاف الخدمات...');
        await _stopServicesForExpiredAccount();
        break;

      case AccountStatus.unknown:
        debugPrint('⚠️ [ACCOUNT_STATUS] حالة حساب غير معروفة');
        break;
    }
  }

  /// إيقاف الخدمات للحسابات المنتهية (بدون تسجيل خروج)
  static Future<void> _stopServicesForExpiredAccount() async {
    try {
      debugPrint('🛑 [ACCOUNT_STATUS] إيقاف الخدمات للحساب المنتهي...');

      // إيقاف خدمة تتبع الحالة
      await _stopUserPresenceService();

      // إيقاف المزامنة اليومية
      await _stopDailySyncService();

      // إيقاف جلسات heartbeat
      await _stopSessionHeartbeat();

      // تعيين علامة للتطبيق أن الحساب منتهي
      await _setAccountExpiredFlag();

      debugPrint('✅ [ACCOUNT_STATUS] تم إيقاف الخدمات للحساب المنتهي');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في إيقاف الخدمات: $e');
    }
  }

  /// فحص إذا كان الخطأ يشير لحساب محذوف
  static bool _isAccountDeletedError(dynamic error) {
    final errorStr = error.toString().toLowerCase();

    return errorStr.contains('404') ||
        errorStr.contains('not found') ||
        errorStr.contains('pgrst116') ||
        errorStr.contains('no rows found') ||
        errorStr.contains('user not found') ||
        errorStr.contains('account not found') ||
        errorStr.contains('forbidden') ||
        errorStr.contains('403') ||
        errorStr.contains('unauthorized') ||
        errorStr.contains('401');
  }

  /// تسجيل خروج فوري وتنظيف البيانات
  static Future<void> _immediateLogout() async {
    try {
      debugPrint('🚨 [DELETED_ACCOUNT] بدء تسجيل الخروج الفوري...');

      // إيقاف المراقبة
      stopMonitoring();

      // تنظيف البيانات المحلية
      await _cleanupLocalData();

      // تسجيل الخروج من Supabase
      await Supabase.instance.client.auth.signOut();

      debugPrint('✅ [DELETED_ACCOUNT] تم تسجيل الخروج الفوري بنجاح');

      // إشعار التطبيق بالحاجة لإعادة التوجيه
      _notifyAppForLogout();
    } catch (e) {
      debugPrint('❌ [DELETED_ACCOUNT] خطأ في تسجيل الخروج الفوري: $e');
    }
  }

  /// تنظيف البيانات المحلية
  static Future<void> _cleanupLocalData() async {
    try {
      debugPrint('🧹 [DELETED_ACCOUNT] تنظيف البيانات المحلية...');

      final prefs = await SharedPreferences.getInstance();

      // قائمة المفاتيح المراد حذفها
      final keysToRemove = prefs
          .getKeys()
          .where(
            (key) =>
                key.startsWith('user_') ||
                key.startsWith('cached_account_data_') ||
                key.startsWith('account_') ||
                key.contains('device_id') ||
                key.contains('session') ||
                key.contains('sync_') ||
                key.contains('presence_') ||
                key.contains('auth_'),
          )
          .toList();

      // حذف البيانات
      for (final key in keysToRemove) {
        await prefs.remove(key);
        debugPrint('🗑️ [DELETED_ACCOUNT] تم حذف: $key');
      }

      debugPrint('✅ [DELETED_ACCOUNT] تم تنظيف البيانات المحلية');
    } catch (e) {
      debugPrint('❌ [DELETED_ACCOUNT] خطأ في تنظيف البيانات: $e');
    }
  }

  /// إشعار التطبيق بالحاجة لإعادة التوجيه
  static void _notifyAppForLogout() {
    // يمكن استخدام Stream أو Callback هنا
    // للتبسيط، سنستخدم SharedPreferences كإشارة
    _setLogoutFlag();

    // إشعار فوري للتطبيق
    debugPrint('🚨 [DELETED_ACCOUNT] إرسال إشعار فوري للتطبيق');
  }

  /// تعيين علامة تسجيل الخروج
  static Future<void> _setLogoutFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('force_logout_deleted_account', true);
      await prefs.setInt(
        'logout_timestamp',
        DateTime.now().millisecondsSinceEpoch,
      );
      debugPrint('🚩 [DELETED_ACCOUNT] تم تعيين علامة تسجيل الخروج');
    } catch (e) {
      debugPrint('❌ [DELETED_ACCOUNT] خطأ في تعيين علامة الخروج: $e');
    }
  }

  /// فحص إذا كان هناك طلب تسجيل خروج معلق
  static Future<bool> checkForPendingLogout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final forceLogout =
          prefs.getBool('force_logout_deleted_account') ?? false;

      if (forceLogout) {
        // مسح العلامة
        await prefs.remove('force_logout_deleted_account');
        await prefs.remove('logout_timestamp');
        debugPrint('🚩 [DELETED_ACCOUNT] تم العثور على طلب تسجيل خروج معلق');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ [DELETED_ACCOUNT] خطأ في فحص طلب الخروج المعلق: $e');
      return false;
    }
  }

  /// فحص فوري للحساب (للاستخدام عند الحاجة)
  static Future<bool> quickAccountCheck() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return false;

      debugPrint('⚡ [DELETED_ACCOUNT] فحص سريع للحساب...');

      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('user_id')
          .eq('user_id', user.id)
          .maybeSingle()
          .timeout(const Duration(seconds: 5));

      final exists = response != null;
      debugPrint(
        '⚡ [DELETED_ACCOUNT] نتيجة الفحص السريع: ${exists ? 'موجود' : 'محذوف'}',
      );

      if (!exists) {
        await _immediateLogout();
      }

      return exists;
    } catch (e) {
      debugPrint('❌ [DELETED_ACCOUNT] خطأ في الفحص السريع: $e');

      if (_isAccountDeletedError(e)) {
        await _immediateLogout();
        return false;
      }

      // في حالة خطأ الشبكة، نفترض أن الحساب موجود
      return true;
    }
  }

  /// إيقاف خدمة تتبع الحالة
  static Future<void> _stopUserPresenceService() async {
    try {
      UserPresenceService.dispose();
      debugPrint('🛑 [ACCOUNT_STATUS] تم إيقاف خدمة تتبع الحالة');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في إيقاف خدمة تتبع الحالة: $e');
    }
  }

  /// إيقاف المزامنة اليومية
  static Future<void> _stopDailySyncService() async {
    try {
      // إيقاف المزامنة عبر تعطيل العلامة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('account_expired_stop_sync', true);
      debugPrint('🛑 [ACCOUNT_STATUS] تم إيقاف المزامنة اليومية');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في إيقاف المزامنة اليومية: $e');
    }
  }

  /// إيقاف جلسات heartbeat
  static Future<void> _stopSessionHeartbeat() async {
    try {
      // إيقاف heartbeat عبر تعطيل العلامة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('account_expired_stop_heartbeat', true);
      debugPrint('🛑 [ACCOUNT_STATUS] تم إيقاف جلسات heartbeat');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في إيقاف جلسات heartbeat: $e');
    }
  }

  /// تعيين علامة أن الحساب منتهي
  static Future<void> _setAccountExpiredFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('account_expired', true);
      await prefs.setString(
        'account_expired_time',
        DateTime.now().toIso8601String(),
      );
      debugPrint('🏷️ [ACCOUNT_STATUS] تم تعيين علامة انتهاء الحساب');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في تعيين علامة انتهاء الحساب: $e');
    }
  }

  /// فحص إذا كان الحساب منتهي محلياً (محدث للنظام الموحد)
  static Future<bool> isAccountExpiredLocally() async {
    try {
      // استخدام النظام الموحد كمصدر أساسي
      final accountStatus = UnifiedAccountStatusManager.currentStatus;
      if (accountStatus != null) {
        return accountStatus.isRestricted;
      }

      // التراجع للطريقة القديمة إذا لم تكن البيانات متاحة
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('account_expired') ?? false;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في فحص علامة انتهاء الحساب: $e');
      return false;
    }
  }

  /// إزالة علامة انتهاء الحساب (عند التفعيل)
  static Future<void> clearAccountExpiredFlag() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('account_expired');
      await prefs.remove('account_expired_time');
      await prefs.remove('account_expired_stop_sync');
      await prefs.remove('account_expired_stop_heartbeat');
      debugPrint('🧹 [ACCOUNT_STATUS] تم إزالة علامات انتهاء الحساب');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_STATUS] خطأ في إزالة علامات انتهاء الحساب: $e');
    }
  }

  /// تنظيف الخدمة
  static void dispose() {
    stopMonitoring();
    debugPrint('🧹 [DELETED_ACCOUNT] تم تنظيف خدمة كشف الحساب المحذوف');
  }
}
