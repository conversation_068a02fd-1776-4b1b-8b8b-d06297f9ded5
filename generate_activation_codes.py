# سكريبت Python لتوليد أكواد تفعيل عشوائية معقدة وإضافتها إلى Supabase
# تحتاج إلى تثبيت supabase: pip install supabase

import random
import string
import os
from supabase import create_client, Client
from datetime import datetime

# إعداد الاتصال بـ Supabase
url = "https://iwtvsvfqmafsziqnoekm.supabase.co"
key = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3dHZzdmZxbWFmc3ppcW5vZWttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMjM2NzUsImV4cCI6MjA2ODc5OTY3NX0.9ho9O3Bk-iziVQBSiQ5X5V9E45KqEzJ2tuMoSu5kNKg"
supabase: Client = create_client(url, key)

# إعدادات الباقات
packages = [
    {'type': 'month', 'package_name': 'باقة شهرية', 'days': 30, 'amount': 5000},
    {'type': '3months', 'package_name': 'باقة 3 أشهر', 'days': 90, 'amount': 14000},
    {'type': '6months', 'package_name': 'باقة 6 أشهر', 'days': 180, 'amount': 27000},
    {'type': 'year', 'package_name': 'باقة سنوية', 'days': 365, 'amount': 50000},
]

# دالة توليد كود معقد
def generate_code(length=12):
    chars = string.ascii_uppercase + string.digits + string.ascii_lowercase
    return ''.join(random.choices(chars, k=length))

# توليد وإضافة الأكواد
try:
    for pkg in packages:
        for _ in range(10):
            code = generate_code(12)

            # إضافة الكود إلى Supabase
            result = supabase.table('activation_codes').insert({
                'code': code,
                'days': pkg['days'],
                'type': pkg['type'],
                'package_name': pkg['package_name'],
                'amount': pkg['amount'],
                'used': False,
                'used_by': None,
                'used_at': None,
                'created_at': datetime.now().isoformat()
            }).execute()

            print(f"تم إنشاء الكود: {code} لباقة {pkg['package_name']}")

    print('✅ تم إنشاء جميع الأكواد بنجاح في Supabase!')

except Exception as e:
    print(f"❌ خطأ في إنشاء الأكواد: {e}")
    print("تأكد من:")
    print("1. إنشاء جدول activation_codes في Supabase")
    print("2. تطبيق SQL schema من ملف supabase_tables.sql")
    print("3. تحديث مفاتيح Supabase في السكريبت")
