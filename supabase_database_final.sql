-- ملف إنشاء الجداول المطلوبة في Supabase - النسخة المحدثة
-- يجب تنفيذ هذا الكود في SQL Editor في Supabase

-- 1. جدول حسابات المستخدمين
CREATE TABLE IF NOT EXISTS user_accounts (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  is_trial BOOLEAN DEFAULT true,
  expiry_millis BIGINT DEFAULT 0,
  creation_millis BIGINT DEFAULT 0,
  activation_millis BIGINT DEFAULT 0,
  active_package TEXT DEFAULT '',
  display_name TEXT DEFAULT '',
  trial_days INTEGER DEFAULT 15,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. جدول ربط المستخدمين بالأجهزة (محدث)
CREATE TABLE IF NOT EXISTS user_devices (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  device_name TEXT DEFAULT '',
  device_type TEXT DEFAULT '',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, device_id)  -- نفس المستخدم + نفس الجهاز = سجل واحد
);

-- 3. دالة الحصول على الوقت الحقيقي من الخادم
CREATE OR REPLACE FUNCTION get_server_time()
RETURNS TABLE(server_time timestamptz)
LANGUAGE sql
AS $$
  SELECT now() as server_time;
$$;

-- 4. دالة للتحقق من انتهاء الفترة التجريبية
CREATE OR REPLACE FUNCTION is_trial_expired(user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    account_data RECORD;
BEGIN
    SELECT * INTO account_data 
    FROM user_accounts 
    WHERE user_id = user_uuid;
    
    IF NOT FOUND THEN
        RETURN false;
    END IF;
    
    IF NOT account_data.is_trial THEN
        RETURN false;
    END IF;
    
    IF account_data.expiry_millis = 0 THEN
        RETURN false;
    END IF;
    
    RETURN (EXTRACT(EPOCH FROM NOW()) * 1000) > account_data.expiry_millis;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON user_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_accounts_is_trial ON user_accounts(is_trial);
CREATE INDEX IF NOT EXISTS idx_user_accounts_expiry ON user_accounts(expiry_millis);
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON user_devices(device_id);

-- 6. تفعيل Row Level Security (RLS)
ALTER TABLE user_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_devices ENABLE ROW LEVEL SECURITY;

-- 7. حذف السياسات الموجودة (إذا كانت موجودة)
DROP POLICY IF EXISTS "Users can view own account data" ON user_accounts;
DROP POLICY IF EXISTS "Users can insert own account data" ON user_accounts;
DROP POLICY IF EXISTS "Users can update own account data" ON user_accounts;
DROP POLICY IF EXISTS "Users can delete own account data" ON user_accounts;

DROP POLICY IF EXISTS "Users can view own devices" ON user_devices;
DROP POLICY IF EXISTS "Users can insert own devices" ON user_devices;
DROP POLICY IF EXISTS "Users can update own devices" ON user_devices;
DROP POLICY IF EXISTS "Users can delete own devices" ON user_devices;

-- 8. سياسات الأمان للجدول user_accounts
CREATE POLICY "Users can view own account data" ON user_accounts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own account data" ON user_accounts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own account data" ON user_accounts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own account data" ON user_accounts
  FOR DELETE USING (auth.uid() = user_id);

-- 9. سياسات الأمان للجدول user_devices
CREATE POLICY "Users can view own devices" ON user_devices
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own devices" ON user_devices
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own devices" ON user_devices
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own devices" ON user_devices
  FOR DELETE USING (auth.uid() = user_id);

-- 10. دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 11. تطبيق الدالة على الجداول
DROP TRIGGER IF EXISTS update_user_accounts_updated_at ON user_accounts;
CREATE TRIGGER update_user_accounts_updated_at 
  BEFORE UPDATE ON user_accounts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_devices_updated_at ON user_devices;
CREATE TRIGGER update_user_devices_updated_at 
  BEFORE UPDATE ON user_devices 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 12. منح الصلاحيات للمستخدمين المصادق عليهم
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON user_accounts TO authenticated;
GRANT ALL ON user_devices TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- 13. دالة مساعدة لحساب الأيام المتبقية
CREATE OR REPLACE FUNCTION get_days_left(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
    account_data RECORD;
    current_time_millis BIGINT;
    days_left INTEGER;
BEGIN
    SELECT * INTO account_data 
    FROM user_accounts 
    WHERE user_id = user_uuid;
    
    IF NOT FOUND OR NOT account_data.is_trial THEN
        RETURN -1; -- حساب غير تجريبي أو غير موجود
    END IF;
    
    IF account_data.expiry_millis = 0 THEN
        RETURN account_data.trial_days; -- لم يتم تحديد تاريخ انتهاء
    END IF;
    
    current_time_millis := EXTRACT(EPOCH FROM NOW()) * 1000;
    
    IF current_time_millis >= account_data.expiry_millis THEN
        RETURN 0; -- انتهت الفترة
    END IF;
    
    days_left := CEIL((account_data.expiry_millis - current_time_millis) / (24 * 60 * 60 * 1000.0));
    RETURN GREATEST(days_left, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تم إنشاء الجداول والدوال بنجاح!
-- الآن يمكن للتطبيق استخدام Supabase لحفظ وجلب بيانات الحسابات

-- ملاحظات مهمة:
-- 1. تأكد من تنفيذ هذا الكود في Supabase Dashboard > SQL Editor
-- 2. Row Level Security مفعل لضمان الأمان
-- 3. كل مستخدم يمكنه الوصول لبياناته فقط
-- 4. الدوال المساعدة متاحة للاستخدام من التطبيق
