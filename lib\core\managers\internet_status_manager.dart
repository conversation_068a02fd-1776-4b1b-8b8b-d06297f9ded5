import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// مدير حالة الإنترنت الموحد - مصدر واحد للحقيقة
class InternetStatusManager {
  static final InternetStatusManager _instance = InternetStatusManager._internal();
  factory InternetStatusManager() => _instance;
  InternetStatusManager._internal();

  // Stream للاستماع لتغييرات حالة الإنترنت
  static final StreamController<bool> _statusController = StreamController<bool>.broadcast();
  static Stream<bool> get statusStream => _statusController.stream;

  // الحالة الحالية
  static bool _isConnected = false;
  static bool get isConnected => _isConnected;

  // مستمع تغييرات الاتصال
  static StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // حالة التهيئة
  static bool _isInitialized = false;
  static bool get isInitialized => _isInitialized;

  /// تهيئة مدير حالة الإنترنت
  static Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🌐 [INTERNET_MANAGER] تهيئة مدير حالة الإنترنت...');

    try {
      // فحص الحالة الأولية
      _isConnected = await _performDetailedConnectionCheck();
      debugPrint('🌐 [INTERNET_MANAGER] الحالة الأولية: ${_isConnected ? 'متصل' : 'غير متصل'}');

      // إرسال الحالة الأولية
      _statusController.add(_isConnected);

      // بدء مراقبة التغييرات
      _startConnectivityListener();

      _isInitialized = true;
      debugPrint('✅ [INTERNET_MANAGER] تم تهيئة مدير حالة الإنترنت بنجاح');
    } catch (e) {
      debugPrint('❌ [INTERNET_MANAGER] خطأ في تهيئة مدير حالة الإنترنت: $e');
      _isConnected = false;
      _statusController.add(false);
    }
  }

  /// فحص تفصيلي للاتصال بالإنترنت
  static Future<bool> _performDetailedConnectionCheck() async {
    try {
      debugPrint('🔍 [INTERNET_MANAGER] بدء فحص تفصيلي للاتصال...');

      // الخطوة 1: فحص حالة الشبكة
      final connectivityResult = await Connectivity().checkConnectivity();
      final hasNetworkConnection = connectivityResult.contains(ConnectivityResult.mobile) ||
                                  connectivityResult.contains(ConnectivityResult.wifi);

      if (!hasNetworkConnection) {
        debugPrint('❌ [INTERNET_MANAGER] لا يوجد اتصال شبكة');
        return false;
      }

      debugPrint('✅ [INTERNET_MANAGER] يوجد اتصال شبكة');

      // الخطوة 2: اختبار DNS lookup
      try {
        final result = await InternetAddress.lookup('google.com').timeout(
          const Duration(seconds: 5),
        );
        
        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          debugPrint('✅ [INTERNET_MANAGER] اختبار DNS نجح');
          return true;
        } else {
          debugPrint('❌ [INTERNET_MANAGER] اختبار DNS فشل - لا توجد عناوين');
          return false;
        }
      } on SocketException catch (e) {
        debugPrint('❌ [INTERNET_MANAGER] اختبار DNS فشل - SocketException: $e');
        return false;
      } on TimeoutException catch (e) {
        debugPrint('❌ [INTERNET_MANAGER] اختبار DNS فشل - Timeout: $e');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [INTERNET_MANAGER] خطأ في الفحص التفصيلي: $e');
      return false;
    }
  }

  /// بدء مراقبة تغييرات الاتصال
  static void _startConnectivityListener() {
    _connectivitySubscription?.cancel();
    
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> results) async {
        debugPrint('🔄 [INTERNET_MANAGER] تغيير في حالة الاتصال: $results');
        
        // فحص تفصيلي عند تغيير الحالة
        final newStatus = await _performDetailedConnectionCheck();
        
        if (newStatus != _isConnected) {
          _isConnected = newStatus;
          debugPrint('📡 [INTERNET_MANAGER] تغيير حالة الإنترنت: ${_isConnected ? 'متصل' : 'منقطع'}');
          _statusController.add(_isConnected);
        }
      },
    );
  }

  /// فحص فوري لحالة الإنترنت
  static Future<bool> checkConnection() async {
    debugPrint('🔍 [INTERNET_MANAGER] فحص فوري للاتصال...');
    
    final isConnected = await _performDetailedConnectionCheck();
    
    if (isConnected != _isConnected) {
      _isConnected = isConnected;
      _statusController.add(_isConnected);
    }
    
    return isConnected;
  }

  /// عرض حالة الاتصال للمستخدم
  static void showConnectionStatus(BuildContext context, {bool forceShow = false}) {
    if (!forceShow && _isConnected) return; // لا نعرض شيء إذا كان متصل

    final message = _isConnected 
        ? '✅ تم استعادة الاتصال بالإنترنت'
        : '❌ لا يوجد اتصال بالإنترنت';
    
    final backgroundColor = _isConnected ? Colors.green : Colors.red;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              _isConnected ? Icons.wifi : Icons.wifi_off,
              color: Colors.white,
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: backgroundColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        duration: Duration(seconds: _isConnected ? 2 : 4),
      ),
    );
  }

  /// إنشاء widget لعرض حالة الإنترنت
  static Widget buildConnectionStatusWidget() {
    return StreamBuilder<bool>(
      stream: statusStream,
      initialData: _isConnected,
      builder: (context, snapshot) {
        final isConnected = snapshot.data ?? false;
        
        if (isConnected) return const SizedBox.shrink();
        
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          color: Colors.red,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.wifi_off, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              const Text(
                'لا يوجد اتصال بالإنترنت',
                style: TextStyle(color: Colors.white, fontSize: 14),
              ),
            ],
          ),
        );
      },
    );
  }

  /// تنظيف الموارد
  static void dispose() {
    _connectivitySubscription?.cancel();
    _statusController.close();
    _isInitialized = false;
    debugPrint('🧹 [INTERNET_MANAGER] تم تنظيف مدير حالة الإنترنت');
  }
}
