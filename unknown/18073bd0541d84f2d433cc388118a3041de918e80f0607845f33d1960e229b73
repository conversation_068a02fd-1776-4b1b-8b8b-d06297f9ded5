import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:async/async.dart';
import '../utils/api_utils.dart';

class MikrotikService {
  Future<String> tryConnect({
    required String ip,
    required String user,
    required String pass,
  }) async {
    Socket socket;
    StreamQueue<List<int>> queue;
    try {
      socket = await Socket.connect(
        ip,
        8728,
        timeout: const Duration(seconds: 5),
      );
      queue = StreamQueue(socket);
    } catch (e) {
      return 'تعذر فتح اتصال بالمنفذ 8728: $e';
    }
    try {
      await _writeSentence(socket, ['/login']);
      final resp = await _readSentenceQ(queue);
      final challengeHex = resp
          .firstWhere((e) => e.startsWith('=ret='), orElse: () => '')
          .replaceFirst('=ret=', '');
      if (challengeHex.isEmpty) {
        socket.destroy();
        await queue.cancel();
        return 'فشل challenge: لم يتم استقبال challenge من السيرفر';
      }
      final challenge = hexToBytes(challengeHex);
      final passBytes = Uint8List.fromList(pass.codeUnits);
      final data = Uint8List(1 + passBytes.length + challenge.length);
      data[0] = 0;
      data.setRange(1, 1 + passBytes.length, passBytes);
      data.setRange(1 + passBytes.length, data.length, challenge);
      final md5 = md5convert(data);
      final response = '00${md5ToHex(md5)}';
      await _writeSentence(socket, [
        '/login',
        '=name=$user',
        '=response=$response',
      ]);
      final loginResp = await _readSentenceQ(queue);
      socket.destroy();
      await queue.cancel();
      if (loginResp.isNotEmpty && loginResp[0].startsWith('!done')) {
        return 'success';
      } else {
        return 'فشل تسجيل الدخول: تحقق من اسم المستخدم أو كلمة المرور';
      }
    } catch (e) {
      try {
        socket.destroy();
        await queue.cancel();
      } catch (_) {}
      return 'خطأ أثناء الاتصال: $e';
    }
  }

  Future<List<Map<String, String>>> fetchPppActive({
    required String ip,
    required String user,
    required String pass,
  }) async {
    final socket = await Socket.connect(
      ip,
      8728,
      timeout: const Duration(seconds: 5),
    );
    final queue = StreamQueue(socket);
    await _writeSentence(socket, ['/login']);
    final resp = await _readSentenceQ(queue);
    final challengeHex = resp
        .firstWhere((e) => e.startsWith('=ret='), orElse: () => '')
        .replaceFirst('=ret=', '');
    if (challengeHex.isEmpty) {
      socket.destroy();
      await queue.cancel();
      throw Exception('فشل challenge');
    }
    final challenge = hexToBytes(challengeHex);
    final passBytes = Uint8List.fromList(pass.codeUnits);
    final data = Uint8List(1 + passBytes.length + challenge.length);
    data[0] = 0;
    data.setRange(1, 1 + passBytes.length, passBytes);
    data.setRange(1 + passBytes.length, data.length, challenge);
    final md5 = md5convert(data);
    final response = '00${md5ToHex(md5)}';
    await _writeSentence(socket, [
      '/login',
      '=name=$user',
      '=response=$response',
    ]);
    final loginResp = await _readSentenceQ(queue);
    if (loginResp.isEmpty || !loginResp[0].startsWith('!done')) {
      socket.destroy();
      await queue.cancel();
      throw Exception('فشل تسجيل الدخول');
    }
    await _writeSentence(socket, ['/ppp/active/print']);
    List<Map<String, String>> users = [];
    while (true) {
      final resp = await _readSentenceQ(queue);
      if (resp.isEmpty) break;
      if (resp[0].startsWith('!done')) break;
      if (resp[0].startsWith('!re')) {
        Map<String, String> user = {};
        for (final w in resp.skip(1)) {
          if (w.startsWith('=name=')) {
            user['name'] = w.replaceFirst('=name=', '');
          }
          if (w.startsWith('=address=')) {
            user['address'] = w.replaceFirst('=address=', '');
          }
          if (w.startsWith('=uptime=')) {
            user['uptime'] = w.replaceFirst('=uptime=', '');
          }
        }
        if (user.isNotEmpty) users.add(user);
      }
    }
    socket.destroy();
    await queue.cancel();
    return users;
  }

  /// إزالة جلسة PPP Active لمستخدم محدد
  Future<void> removeActiveSession(
    String ip,
    String user,
    String pass,
    String username,
  ) async {
    final socket = await Socket.connect(
      ip,
      8728,
      timeout: const Duration(seconds: 5),
    );
    final queue = StreamQueue(socket);
    // تسجيل الدخول
    await _writeSentence(socket, ['/login']);
    final resp = await _readSentenceQ(queue);
    final challengeHex = resp
        .firstWhere((e) => e.startsWith('=ret='), orElse: () => '')
        .replaceFirst('=ret=', '');
    if (challengeHex.isEmpty) {
      socket.destroy();
      await queue.cancel();
      throw Exception('فشل challenge');
    }
    final challenge = hexToBytes(challengeHex);
    final passBytes = Uint8List.fromList(pass.codeUnits);
    final data = Uint8List(1 + passBytes.length + challenge.length);
    data[0] = 0;
    data.setRange(1, 1 + passBytes.length, passBytes);
    data.setRange(1 + passBytes.length, data.length, challenge);
    final md5 = md5convert(data);
    final response = '00${md5ToHex(md5)}';
    await _writeSentence(socket, [
      '/login',
      '=name=$user',
      '=response=$response',
    ]);
    final loginResp = await _readSentenceQ(queue);
    if (loginResp.isEmpty || !loginResp[0].startsWith('!done')) {
      socket.destroy();
      await queue.cancel();
      throw Exception('فشل تسجيل الدخول');
    }
    // تنفيذ أمر إزالة الجلسة
    await _writeSentence(socket, [
      '/ppp/active/remove',
      '=numbers=[find name=$username]',
    ]);
    await _readSentenceQ(queue); // تجاهل الرد
    socket.destroy();
    await queue.cancel();
  }

  Future<void> _writeSentence(Socket socket, List<String> words) async {
    for (final word in words) {
      final bytes = Uint8List.fromList(word.codeUnits);
      socket.add(_encodeLength(bytes.length));
      socket.add(bytes);
    }
    socket.add([0]);
    await socket.flush();
  }

  Future<List<String>> _readSentenceQ(StreamQueue queue) async {
    List<String> words = [];
    while (true) {
      final len = await _readLengthQ(queue);
      if (len == 0) break;
      final data = await _readBytesQ(queue, len);
      words.add(String.fromCharCodes(data));
    }
    return words;
  }

  Future<int> _readLengthQ(StreamQueue queue) async {
    int len = 0;
    int b = (await _readBytesQ(queue, 1))[0];
    if (b < 0x80) {
      len = b;
    } else if ((b & 0xC0) == 0x80) {
      len = ((b & ~0xC0) << 8) + (await _readBytesQ(queue, 1))[0];
    } else if ((b & 0xE0) == 0xC0) {
      len =
          ((b & ~0xE0) << 16) +
          ((await _readBytesQ(queue, 1))[0] << 8) +
          (await _readBytesQ(queue, 1))[0];
    } else if ((b & 0xF0) == 0xE0) {
      len =
          ((b & ~0xF0) << 24) +
          ((await _readBytesQ(queue, 1))[0] << 16) +
          ((await _readBytesQ(queue, 1))[0] << 8) +
          (await _readBytesQ(queue, 1))[0];
    }
    return len;
  }

  Future<Uint8List> _readBytesQ(StreamQueue queue, int len) async {
    List<int> buffer = [];
    while (buffer.length < len) {
      final chunk = await queue.next;
      buffer.addAll(chunk);
    }
    return Uint8List.fromList(buffer.sublist(0, len));
  }

  List<int> _encodeLength(int len) {
    if (len < 0x80) {
      return [len];
    } else if (len < 0x4000) {
      return [((len >> 8) | 0x80), (len & 0xFF)];
    } else if (len < 0x200000) {
      return [((len >> 16) | 0xC0), ((len >> 8) & 0xFF), (len & 0xFF)];
    } else if (len < 0x10000000) {
      return [
        ((len >> 24) | 0xE0),
        ((len >> 16) & 0xFF),
        ((len >> 8) & 0xFF),
        (len & 0xFF),
      ];
    } else {
      throw Exception('Word too long');
    }
  }
}
