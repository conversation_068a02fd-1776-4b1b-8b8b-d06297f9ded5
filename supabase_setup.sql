-- إعد<PERSON> قاعدة البيانات في Supabase للنسخ الاحتياطي

-- 1. إن<PERSON>اء جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    timestamp BIGINT NOT NULL,
    public_url TEXT NOT NULL,
    checksum TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_backups_user_id ON backups(user_id);
CREATE INDEX IF NOT EXISTS idx_backups_created_at ON backups(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_backups_timestamp ON backups(timestamp DESC);

-- 3. إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 4. إنشاء trigger لتحديث updated_at
CREATE TRIGGER update_backups_updated_at 
    BEFORE UPDATE ON backups 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 5. إعداد Row Level Security (RLS)
ALTER TABLE backups ENABLE ROW LEVEL SECURITY;

-- 6. سياسة الأمان: المستخدمون يمكنهم الوصول لنسخهم الاحتياطية فقط
CREATE POLICY "Users can view their own backups" ON backups
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own backups" ON backups
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own backups" ON backups
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own backups" ON backups
    FOR DELETE USING (auth.uid() = user_id);

-- 7. إنشاء bucket للتخزين (يجب تنفيذه من لوحة التحكم)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('backups', 'backups', true);

-- 8. سياسات أمان Storage
-- CREATE POLICY "Users can upload their own backups" ON storage.objects
--     FOR INSERT WITH CHECK (bucket_id = 'backups' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can view their own backups" ON storage.objects
--     FOR SELECT USING (bucket_id = 'backups' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can delete their own backups" ON storage.objects
--     FOR DELETE USING (bucket_id = 'backups' AND auth.uid()::text = (storage.foldername(name))[1]);

-- 9. دالة للحصول على إحصائيات النسخ الاحتياطية
CREATE OR REPLACE FUNCTION get_backup_stats(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_backups', COUNT(*),
        'total_size', COALESCE(SUM(file_size), 0),
        'latest_backup', MAX(created_at),
        'oldest_backup', MIN(created_at)
    ) INTO result
    FROM backups
    WHERE user_id = user_uuid;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. دالة لتنظيف النسخ القديمة (الاحتفاظ بـ 5 نسخ فقط)
CREATE OR REPLACE FUNCTION cleanup_old_backups(user_uuid UUID, keep_count INTEGER DEFAULT 5)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    WITH old_backups AS (
        SELECT id
        FROM backups
        WHERE user_id = user_uuid
        ORDER BY created_at DESC
        OFFSET keep_count
    )
    DELETE FROM backups
    WHERE id IN (SELECT id FROM old_backups);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 11. جدولة تنظيف النسخ القديمة (اختياري - يتطلب pg_cron extension)
-- SELECT cron.schedule('cleanup-old-backups', '0 2 * * *', 'SELECT cleanup_old_backups(user_id) FROM (SELECT DISTINCT user_id FROM backups) AS users;');

-- ملاحظات للتطبيق:
-- 1. يجب إنشاء bucket 'backups' من لوحة تحكم Supabase
-- 2. يجب تفعيل RLS على جدول storage.objects
-- 3. يجب إضافة سياسات الأمان للـ Storage من لوحة التحكم
-- 4. يمكن تخصيص عدد النسخ المحتفظ بها في دالة cleanup_old_backups
