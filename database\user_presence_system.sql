-- نظام تتبع حالة اتصال المستخدمين
-- تشغيل هذا الملف في Supabase SQL Editor

-- 1. جدول حالة اتصال المستخدمين
CREATE TABLE IF NOT EXISTS user_presence (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  device_name TEXT,
  device_type TEXT CHECK (device_type IN ('android', 'ios', 'web')),
  is_online BOOLEAN DEFAULT true,
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_heartbeat TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT,
  app_version TEXT,
  location_info JSONB,
  session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  total_session_time INTERVAL DEFAULT '0 seconds',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- فهرس فريد لكل جهاز
  UNIQUE(user_id, device_id)
);

-- 2. جدول إحصائيات الاتصال اليومية
CREATE TABLE IF NOT EXISTS daily_presence_stats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  total_online_time INTERVAL DEFAULT '0 seconds',
  session_count INTEGER DEFAULT 0,
  devices_used TEXT[] DEFAULT '{}',
  first_login TIMESTAMP WITH TIME ZONE,
  last_logout TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- فهرس فريد لكل مستخدم في اليوم
  UNIQUE(user_id, date)
);

-- 3. إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_presence_user_id ON user_presence(user_id);
CREATE INDEX IF NOT EXISTS idx_user_presence_is_online ON user_presence(is_online);
CREATE INDEX IF NOT EXISTS idx_user_presence_last_heartbeat ON user_presence(last_heartbeat);
CREATE INDEX IF NOT EXISTS idx_user_presence_device_id ON user_presence(device_id);
CREATE INDEX IF NOT EXISTS idx_daily_presence_stats_user_id ON daily_presence_stats(user_id);
CREATE INDEX IF NOT EXISTS idx_daily_presence_stats_date ON daily_presence_stats(date);

-- 4. دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق الـ trigger على الجداول
DROP TRIGGER IF EXISTS update_user_presence_updated_at ON user_presence;
CREATE TRIGGER update_user_presence_updated_at 
    BEFORE UPDATE ON user_presence 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5. دالة تسجيل اتصال المستخدم
CREATE OR REPLACE FUNCTION user_connect(
  user_id_param UUID,
  device_id_param TEXT,
  device_name_param TEXT DEFAULT NULL,
  device_type_param TEXT DEFAULT 'unknown',
  ip_address_param INET DEFAULT NULL,
  user_agent_param TEXT DEFAULT NULL,
  app_version_param TEXT DEFAULT NULL,
  location_info_param JSONB DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  existing_session RECORD;
BEGIN
  -- البحث عن جلسة موجودة
  SELECT * INTO existing_session 
  FROM user_presence 
  WHERE user_id = user_id_param AND device_id = device_id_param;
  
  IF existing_session.id IS NOT NULL THEN
    -- تحديث الجلسة الموجودة
    UPDATE user_presence 
    SET 
      is_online = true,
      last_heartbeat = NOW(),
      last_seen = NOW(),
      ip_address = COALESCE(ip_address_param, ip_address),
      user_agent = COALESCE(user_agent_param, user_agent),
      app_version = COALESCE(app_version_param, app_version),
      location_info = COALESCE(location_info_param, location_info),
      device_name = COALESCE(device_name_param, device_name),
      device_type = COALESCE(device_type_param, device_type)
    WHERE user_id = user_id_param AND device_id = device_id_param;
  ELSE
    -- إنشاء جلسة جديدة
    INSERT INTO user_presence (
      user_id, device_id, device_name, device_type,
      is_online, last_seen, last_heartbeat,
      ip_address, user_agent, app_version, location_info,
      session_start
    ) VALUES (
      user_id_param, device_id_param, device_name_param, device_type_param,
      true, NOW(), NOW(),
      ip_address_param, user_agent_param, app_version_param, location_info_param,
      NOW()
    );
  END IF;
  
  -- تحديث الإحصائيات اليومية
  INSERT INTO daily_presence_stats (user_id, date, session_count, first_login, devices_used)
  VALUES (user_id_param, CURRENT_DATE, 1, NOW(), ARRAY[device_id_param])
  ON CONFLICT (user_id, date) 
  DO UPDATE SET 
    session_count = daily_presence_stats.session_count + 1,
    devices_used = array_append(
      CASE 
        WHEN device_id_param = ANY(daily_presence_stats.devices_used) 
        THEN daily_presence_stats.devices_used
        ELSE daily_presence_stats.devices_used
      END, 
      CASE 
        WHEN device_id_param = ANY(daily_presence_stats.devices_used) 
        THEN NULL
        ELSE device_id_param
      END
    );
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. دالة تحديث heartbeat
CREATE OR REPLACE FUNCTION user_heartbeat(
  user_id_param UUID,
  device_id_param TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE user_presence 
  SET 
    last_heartbeat = NOW(),
    last_seen = NOW(),
    is_online = true
  WHERE user_id = user_id_param AND device_id = device_id_param;
  
  RETURN FOUND;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. دالة قطع اتصال المستخدم
CREATE OR REPLACE FUNCTION user_disconnect(
  user_id_param UUID,
  device_id_param TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  session_duration INTERVAL;
BEGIN
  -- حساب مدة الجلسة وتحديث الحالة
  UPDATE user_presence 
  SET 
    is_online = false,
    last_seen = NOW(),
    total_session_time = total_session_time + (NOW() - session_start)
  WHERE user_id = user_id_param AND device_id = device_id_param
  RETURNING (NOW() - session_start) INTO session_duration;
  
  -- تحديث الإحصائيات اليومية
  UPDATE daily_presence_stats 
  SET 
    total_online_time = total_online_time + COALESCE(session_duration, '0 seconds'),
    last_logout = NOW()
  WHERE user_id = user_id_param AND date = CURRENT_DATE;
  
  RETURN FOUND;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. دالة جلب المستخدمين المتصلين
CREATE OR REPLACE FUNCTION get_online_users()
RETURNS TABLE (
  user_id UUID,
  email TEXT,
  display_name TEXT,
  device_count BIGINT,
  last_seen TIMESTAMP WITH TIME ZONE,
  session_start TIMESTAMP WITH TIME ZONE,
  devices JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.user_id,
    ua.email,
    ua.display_name,
    COUNT(up.device_id) as device_count,
    MAX(up.last_seen) as last_seen,
    MIN(up.session_start) as session_start,
    jsonb_agg(
      jsonb_build_object(
        'device_id', up.device_id,
        'device_name', up.device_name,
        'device_type', up.device_type,
        'last_heartbeat', up.last_heartbeat,
        'ip_address', up.ip_address
      )
    ) as devices
  FROM user_presence up
  JOIN user_accounts ua ON up.user_id = ua.user_id
  WHERE up.is_online = true
  GROUP BY up.user_id, ua.email, ua.display_name
  ORDER BY MAX(up.last_seen) DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. دالة تنظيف الجلسات المنتهية
CREATE OR REPLACE FUNCTION cleanup_offline_users()
RETURNS INTEGER AS $$
DECLARE
  offline_count INTEGER;
BEGIN
  -- تحديد المستخدمين غير المتصلين (لم يرسلوا heartbeat لأكثر من 5 دقائق)
  UPDATE user_presence 
  SET is_online = false, last_seen = NOW()
  WHERE is_online = true 
    AND last_heartbeat < NOW() - INTERVAL '5 minutes';
  
  GET DIAGNOSTICS offline_count = ROW_COUNT;
  
  -- حذف الجلسات القديمة (أكثر من 30 يوم)
  DELETE FROM user_presence 
  WHERE last_seen < NOW() - INTERVAL '30 days';
  
  -- حذف الإحصائيات القديمة (أكثر من 90 يوم)
  DELETE FROM daily_presence_stats 
  WHERE date < CURRENT_DATE - INTERVAL '90 days';
  
  RETURN offline_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. إعداد Row Level Security (RLS)
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_presence_stats ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للمستخدمين
CREATE POLICY "Users can view their own presence" ON user_presence
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own presence" ON user_presence
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own presence" ON user_presence
  FOR UPDATE USING (auth.uid() = user_id);

-- سياسات للإدارة (يمكن للمدراء رؤية جميع البيانات)
CREATE POLICY "Admins can view all presence" ON user_presence
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_accounts 
      WHERE user_id = auth.uid() 
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

-- سياسات للإحصائيات
CREATE POLICY "Users can view their own stats" ON daily_presence_stats
  FOR SELECT USING (auth.uid() = user_id);

-- 11. إنشاء مهمة تنظيف تلقائية
SELECT cron.schedule(
  'cleanup-offline-users',
  '*/5 * * * *', -- كل 5 دقائق
  'SELECT cleanup_offline_users();'
);

-- 12. إنشاء view للإحصائيات السريعة
CREATE OR REPLACE VIEW user_presence_summary AS
SELECT 
  COUNT(*) FILTER (WHERE is_online = true) as online_users,
  COUNT(*) FILTER (WHERE is_online = false) as offline_users,
  COUNT(DISTINCT user_id) as total_unique_users,
  COUNT(DISTINCT user_id) FILTER (WHERE is_online = true) as unique_online_users,
  AVG(EXTRACT(EPOCH FROM (last_seen - session_start))) FILTER (WHERE is_online = true) as avg_session_duration_seconds
FROM user_presence
WHERE last_seen > NOW() - INTERVAL '24 hours';

COMMIT;
