-- جداول نظام المزامنة الكامل لقاعدة البيانات مع Supabase
-- يجب تنفيذ هذا الكود في SQL Editor في Supabase

-- 1. جدول النسخ الاحتياطية المضغوطة
CREATE TABLE IF NOT EXISTS compressed_backups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  backup_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  compressed_size BIGINT NOT NULL,
  compression_ratio DECIMAL(5,2),
  checksum TEXT NOT NULL,
  backup_version TEXT DEFAULT '3.0.0',
  backup_type TEXT DEFAULT 'full_database',
  
  -- بيانات المحتوى
  total_subscribers INTEGER DEFAULT 0,
  total_transactions INTEGER DEFAULT 0,
  total_devices INTEGER DEFAULT 0,
  
  -- معلومات الضغط
  compression_algorithm TEXT DEFAULT 'gzip',
  encryption_enabled BOOLEAN DEFAULT false,
  
  -- معلومات النظام
  app_version TEXT,
  platform TEXT,
  device_info JSONB,
  
  -- التوقيتات
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- فهرسة للبحث السريع
  UNIQUE(user_id, backup_name)
);

-- 2. جدول إحصائيات المزامنة
CREATE TABLE IF NOT EXISTS sync_statistics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  
  -- إحصائيات عامة
  total_syncs INTEGER DEFAULT 0,
  successful_syncs INTEGER DEFAULT 0,
  failed_syncs INTEGER DEFAULT 0,
  last_sync_at TIMESTAMPTZ,
  last_successful_sync_at TIMESTAMPTZ,
  last_failed_sync_at TIMESTAMPTZ,
  
  -- أحجام البيانات
  total_data_uploaded BIGINT DEFAULT 0,
  total_data_downloaded BIGINT DEFAULT 0,
  average_backup_size BIGINT DEFAULT 0,
  
  -- معدلات الأداء
  average_upload_time INTERVAL,
  average_compression_ratio DECIMAL(5,2),
  
  -- معلومات الأخطاء
  last_error_message TEXT,
  error_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, device_id)
);

-- 3. جدول سجل المزامنة (للتتبع المفصل)
CREATE TABLE IF NOT EXISTS sync_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  backup_id UUID REFERENCES compressed_backups(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  
  -- نوع العملية
  operation_type TEXT CHECK (operation_type IN ('upload', 'download', 'delete', 'cleanup')),
  status TEXT CHECK (status IN ('started', 'in_progress', 'completed', 'failed', 'cancelled')),
  
  -- تفاصيل العملية
  operation_details JSONB,
  progress_percentage INTEGER DEFAULT 0,
  
  -- الأوقات
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  duration INTERVAL,
  
  -- معلومات الخطأ
  error_message TEXT,
  error_code TEXT,
  
  -- معلومات إضافية
  metadata JSONB,
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_compressed_backups_user_id ON compressed_backups(user_id);
CREATE INDEX IF NOT EXISTS idx_compressed_backups_created_at ON compressed_backups(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_compressed_backups_device_id ON compressed_backups(device_id);

CREATE INDEX IF NOT EXISTS idx_sync_statistics_user_id ON sync_statistics(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_statistics_device_id ON sync_statistics(device_id);

CREATE INDEX IF NOT EXISTS idx_sync_logs_user_id ON sync_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_operation_type ON sync_logs(operation_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_status ON sync_logs(status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_started_at ON sync_logs(started_at DESC);

-- 5. دالة تنظيف النسخ القديمة (الاحتفاظ بـ 5 نسخ لكل مستخدم)
CREATE OR REPLACE FUNCTION cleanup_old_backups(target_user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
  deleted_count INTEGER := 0;
  backup_record RECORD;
BEGIN
  -- حذف النسخ الزائدة عن 5 لكل مستخدم
  FOR backup_record IN (
    SELECT id, file_path
    FROM compressed_backups 
    WHERE user_id = target_user_id 
    ORDER BY created_at DESC 
    OFFSET 5
  ) LOOP
    -- حذف الملف من Storage (يجب تنفيذه من التطبيق)
    -- حذف السجل من قاعدة البيانات
    DELETE FROM compressed_backups WHERE id = backup_record.id;
    deleted_count := deleted_count + 1;
  END LOOP;
  
  RETURN deleted_count;
END;
$$;

-- 6. دالة تحديث إحصائيات المزامنة
CREATE OR REPLACE FUNCTION update_sync_statistics(
  target_user_id UUID,
  target_device_id TEXT,
  sync_success BOOLEAN,
  backup_size BIGINT DEFAULT 0,
  sync_duration INTERVAL DEFAULT NULL,
  compression_ratio DECIMAL DEFAULT NULL,
  error_msg TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO sync_statistics (
    user_id, device_id, total_syncs, successful_syncs, failed_syncs,
    last_sync_at, last_successful_sync_at, last_failed_sync_at,
    total_data_uploaded, average_backup_size, average_compression_ratio,
    last_error_message, error_count
  ) VALUES (
    target_user_id, target_device_id, 1, 
    CASE WHEN sync_success THEN 1 ELSE 0 END,
    CASE WHEN sync_success THEN 0 ELSE 1 END,
    NOW(),
    CASE WHEN sync_success THEN NOW() ELSE NULL END,
    CASE WHEN sync_success THEN NULL ELSE NOW() END,
    CASE WHEN sync_success THEN backup_size ELSE 0 END,
    backup_size, compression_ratio, error_msg,
    CASE WHEN sync_success THEN 0 ELSE 1 END
  )
  ON CONFLICT (user_id, device_id) DO UPDATE SET
    total_syncs = sync_statistics.total_syncs + 1,
    successful_syncs = sync_statistics.successful_syncs + CASE WHEN sync_success THEN 1 ELSE 0 END,
    failed_syncs = sync_statistics.failed_syncs + CASE WHEN sync_success THEN 0 ELSE 1 END,
    last_sync_at = NOW(),
    last_successful_sync_at = CASE WHEN sync_success THEN NOW() ELSE sync_statistics.last_successful_sync_at END,
    last_failed_sync_at = CASE WHEN sync_success THEN sync_statistics.last_failed_sync_at ELSE NOW() END,
    total_data_uploaded = sync_statistics.total_data_uploaded + CASE WHEN sync_success THEN backup_size ELSE 0 END,
    average_backup_size = (sync_statistics.average_backup_size * sync_statistics.successful_syncs + 
                          CASE WHEN sync_success THEN backup_size ELSE 0 END) / 
                          GREATEST(sync_statistics.successful_syncs + CASE WHEN sync_success THEN 1 ELSE 0 END, 1),
    average_compression_ratio = CASE 
      WHEN sync_success AND compression_ratio IS NOT NULL THEN 
        (COALESCE(sync_statistics.average_compression_ratio, 0) * sync_statistics.successful_syncs + compression_ratio) / 
        (sync_statistics.successful_syncs + 1)
      ELSE sync_statistics.average_compression_ratio 
    END,
    last_error_message = CASE WHEN sync_success THEN NULL ELSE error_msg END,
    error_count = CASE WHEN sync_success THEN 0 ELSE sync_statistics.error_count + 1 END,
    updated_at = NOW();
END;
$$;

-- 7. دالة الحصول على إحصائيات المستخدم
CREATE OR REPLACE FUNCTION get_user_sync_stats(target_user_id UUID)
RETURNS TABLE(
  total_backups INTEGER,
  total_size_mb DECIMAL,
  last_backup_date TIMESTAMPTZ,
  success_rate DECIMAL,
  average_compression DECIMAL
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(cb.id)::INTEGER as total_backups,
    ROUND(SUM(cb.file_size)::DECIMAL / 1024 / 1024, 2) as total_size_mb,
    MAX(cb.created_at) as last_backup_date,
    CASE 
      WHEN ss.total_syncs > 0 THEN ROUND((ss.successful_syncs::DECIMAL / ss.total_syncs) * 100, 2)
      ELSE 0
    END as success_rate,
    ROUND(AVG(cb.compression_ratio), 2) as average_compression
  FROM compressed_backups cb
  LEFT JOIN sync_statistics ss ON ss.user_id = cb.user_id
  WHERE cb.user_id = target_user_id
  GROUP BY ss.total_syncs, ss.successful_syncs;
END;
$$;

-- 8. تفعيل Row Level Security
ALTER TABLE compressed_backups ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;

-- 9. سياسات الأمان
CREATE POLICY "Users can only access their own backups" ON compressed_backups
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own sync stats" ON sync_statistics
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own sync logs" ON sync_logs
  FOR ALL USING (auth.uid() = user_id);
