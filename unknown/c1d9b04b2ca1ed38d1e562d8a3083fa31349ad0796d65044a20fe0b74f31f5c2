import 'package:flutter/material.dart';

class PppActiveScreen extends StatelessWidget {
  final List<Map<String, String>> users;
  const PppActiveScreen({super.key, required this.users});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المشتركين النشطين PPP')),
      body: users.isEmpty
          ? const Center(child: Text('لا يوجد مشتركين نشطين'))
          : ListView.separated(
              padding: const EdgeInsets.all(16),
              itemCount: users.length,
              separatorBuilder: (_, __) => const SizedBox(height: 12),
              itemBuilder: (context, i) {
                final u = users[i];
                return Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(14),
                  ),
                  elevation: 2,
                  child: ListTile(
                    leading: const Icon(Icons.person, color: Colors.blue),
                    title: Text(
                      u['name'] ?? '',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('IP: ${u['address'] ?? ''}'),
                        Text('وقت الاتصال: ${u['uptime'] ?? ''}'),
                      ],
                    ),
                  ),
                );
              },
            ),
    );
  }
}
