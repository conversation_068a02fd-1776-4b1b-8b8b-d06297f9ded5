-- ===================================================================
-- نظام المزامنة السحابية الكامل لقاعدة البيانات مع Supabase
-- تاريخ الإنشاء: 2025-01-27
-- الوصف: نظام شامل للنسخ الاحتياطي المضغوط مع إحصائيات مفصلة
-- ===================================================================

-- 1. جدول النسخ الاحتياطية المضغوطة
CREATE TABLE IF NOT EXISTS compressed_backups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  
  -- معلومات الملف
  backup_name TEXT NOT NULL,
  file_path TEXT NOT NULL,
  storage_bucket TEXT DEFAULT 'userbackups',
  
  -- أحجام البيانات
  original_size BIGINT NOT NULL,
  compressed_size BIGINT NOT NULL,
  compression_ratio DECIMAL(5,2) GENERATED ALWAYS AS (
    CASE WHEN original_size > 0 
    THEN ROUND((1.0 - compressed_size::DECIMAL / original_size) * 100, 2)
    ELSE 0 END
  ) STORED,
  
  -- معلومات الأمان
  checksum TEXT NOT NULL,
  encryption_key TEXT,
  is_encrypted BOOLEAN DEFAULT false,
  
  -- معلومات المحتوى
  backup_version TEXT DEFAULT '3.0.0',
  backup_type TEXT DEFAULT 'full_database',
  total_subscribers INTEGER DEFAULT 0,
  total_transactions INTEGER DEFAULT 0,
  total_devices INTEGER DEFAULT 0,
  
  -- معلومات النظام
  app_version TEXT,
  platform TEXT,
  device_info JSONB,
  compression_algorithm TEXT DEFAULT 'gzip',
  
  -- التوقيتات
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '90 days'),
  
  -- قيود فريدة
  UNIQUE(user_id, backup_name),
  
  -- فحص صحة البيانات
  CONSTRAINT valid_sizes CHECK (original_size > 0 AND compressed_size > 0),
  CONSTRAINT valid_compression CHECK (compressed_size <= original_size)
);

-- 2. جدول إحصائيات المزامنة
CREATE TABLE IF NOT EXISTS sync_statistics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  
  -- إحصائيات عامة
  total_syncs INTEGER DEFAULT 0,
  successful_syncs INTEGER DEFAULT 0,
  failed_syncs INTEGER DEFAULT 0,
  
  -- معدل النجاح
  success_rate DECIMAL(5,2) GENERATED ALWAYS AS (
    CASE WHEN total_syncs > 0 
    THEN ROUND((successful_syncs::DECIMAL / total_syncs) * 100, 2)
    ELSE 0 END
  ) STORED,
  
  -- أحجام البيانات
  total_data_uploaded BIGINT DEFAULT 0,
  total_data_downloaded BIGINT DEFAULT 0,
  average_backup_size BIGINT DEFAULT 0,
  largest_backup_size BIGINT DEFAULT 0,
  smallest_backup_size BIGINT DEFAULT 0,
  
  -- معدلات الأداء
  average_upload_time INTERVAL DEFAULT '0 seconds',
  fastest_upload_time INTERVAL DEFAULT '0 seconds',
  slowest_upload_time INTERVAL DEFAULT '0 seconds',
  average_compression_ratio DECIMAL(5,2) DEFAULT 0,
  
  -- التوقيتات
  first_sync_at TIMESTAMPTZ,
  last_sync_at TIMESTAMPTZ,
  last_successful_sync_at TIMESTAMPTZ,
  last_failed_sync_at TIMESTAMPTZ,
  
  -- معلومات الأخطاء
  last_error_message TEXT,
  consecutive_failures INTEGER DEFAULT 0,
  total_error_count INTEGER DEFAULT 0,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, device_id)
);

-- 3. جدول سجل المزامنة المفصل
CREATE TABLE IF NOT EXISTS sync_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  backup_id UUID REFERENCES compressed_backups(id) ON DELETE SET NULL,
  device_id TEXT NOT NULL,
  
  -- نوع العملية
  operation_type TEXT CHECK (operation_type IN (
    'upload', 'download', 'delete', 'cleanup', 'verify'
  )) NOT NULL,
  
  -- حالة العملية
  status TEXT CHECK (status IN (
    'started', 'compressing', 'uploading', 'verifying', 
    'completed', 'failed', 'cancelled', 'timeout'
  )) NOT NULL DEFAULT 'started',
  
  -- تفاصيل العملية
  operation_details JSONB DEFAULT '{}',
  progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
  
  -- معلومات الأداء
  data_size BIGINT DEFAULT 0,
  transfer_speed_kbps DECIMAL(10,2),
  compression_ratio DECIMAL(5,2),
  
  -- الأوقات
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  duration INTERVAL GENERATED ALWAYS AS (completed_at - started_at) STORED,
  
  -- معلومات الخطأ
  error_message TEXT,
  error_code TEXT,
  retry_count INTEGER DEFAULT 0,
  
  -- معلومات إضافية
  metadata JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. جدول حدود المستخدمين
CREATE TABLE IF NOT EXISTS user_sync_limits (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- حدود النسخ
  max_backups INTEGER DEFAULT 5,
  max_backup_size_mb INTEGER DEFAULT 100,
  max_daily_syncs INTEGER DEFAULT 10,
  
  -- حدود التخزين
  total_storage_limit_mb INTEGER DEFAULT 500,
  current_storage_used_mb BIGINT DEFAULT 0,
  
  -- حدود الشبكة
  max_upload_speed_kbps INTEGER DEFAULT 1024,
  max_download_speed_kbps INTEGER DEFAULT 2048,
  
  -- إعدادات المستخدم
  auto_cleanup_enabled BOOLEAN DEFAULT true,
  compression_enabled BOOLEAN DEFAULT true,
  encryption_enabled BOOLEAN DEFAULT false,
  
  -- إحصائيات الاستخدام
  syncs_today INTEGER DEFAULT 0,
  last_sync_date DATE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_compressed_backups_user_id ON compressed_backups(user_id);
CREATE INDEX IF NOT EXISTS idx_compressed_backups_created_at ON compressed_backups(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_compressed_backups_device_id ON compressed_backups(device_id);
CREATE INDEX IF NOT EXISTS idx_compressed_backups_expires_at ON compressed_backups(expires_at);
CREATE INDEX IF NOT EXISTS idx_compressed_backups_size ON compressed_backups(compressed_size);

CREATE INDEX IF NOT EXISTS idx_sync_statistics_user_id ON sync_statistics(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_statistics_device_id ON sync_statistics(device_id);
CREATE INDEX IF NOT EXISTS idx_sync_statistics_last_sync ON sync_statistics(last_sync_at DESC);
CREATE INDEX IF NOT EXISTS idx_sync_statistics_success_rate ON sync_statistics(success_rate);

CREATE INDEX IF NOT EXISTS idx_sync_logs_user_id ON sync_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_operation_type ON sync_logs(operation_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_status ON sync_logs(status);
CREATE INDEX IF NOT EXISTS idx_sync_logs_started_at ON sync_logs(started_at DESC);
CREATE INDEX IF NOT EXISTS idx_sync_logs_backup_id ON sync_logs(backup_id);

-- 6. دالة تنظيف النسخ القديمة (الاحتفاظ بـ 5 نسخ لكل مستخدم)
CREATE OR REPLACE FUNCTION cleanup_old_backups(target_user_id UUID)
RETURNS TABLE(
  deleted_count INTEGER,
  freed_space_mb DECIMAL,
  deleted_files TEXT[]
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_limit INTEGER;
  backup_record RECORD;
  total_deleted INTEGER := 0;
  total_freed BIGINT := 0;
  deleted_file_paths TEXT[] := '{}';
BEGIN
  -- الحصول على حد المستخدم
  SELECT max_backups INTO user_limit
  FROM user_sync_limits
  WHERE user_id = target_user_id;
  
  -- استخدام الحد الافتراضي إذا لم يوجد
  user_limit := COALESCE(user_limit, 5);
  
  -- حذف النسخ الزائدة
  FOR backup_record IN (
    SELECT id, file_path, compressed_size
    FROM compressed_backups 
    WHERE user_id = target_user_id 
    ORDER BY created_at DESC 
    OFFSET user_limit
  ) LOOP
    -- إضافة معلومات الملف المحذوف
    deleted_file_paths := array_append(deleted_file_paths, backup_record.file_path);
    total_freed := total_freed + backup_record.compressed_size;
    
    -- حذف السجل من قاعدة البيانات
    DELETE FROM compressed_backups WHERE id = backup_record.id;
    total_deleted := total_deleted + 1;
  END LOOP;
  
  -- تحديث استخدام التخزين
  UPDATE user_sync_limits 
  SET 
    current_storage_used_mb = GREATEST(0, current_storage_used_mb - (total_freed / 1024 / 1024)),
    updated_at = NOW()
  WHERE user_id = target_user_id;
  
  RETURN QUERY SELECT 
    total_deleted,
    ROUND((total_freed::DECIMAL / 1024 / 1024), 2),
    deleted_file_paths;
END;
$$;

-- 7. دالة تحديث إحصائيات المزامنة
CREATE OR REPLACE FUNCTION update_sync_statistics(
  target_user_id UUID,
  target_device_id TEXT,
  sync_success BOOLEAN,
  backup_size BIGINT DEFAULT 0,
  sync_duration INTERVAL DEFAULT NULL,
  compression_ratio DECIMAL DEFAULT NULL,
  error_msg TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_stats RECORD;
BEGIN
  -- الحصول على الإحصائيات الحالية
  SELECT * INTO current_stats
  FROM sync_statistics
  WHERE user_id = target_user_id AND device_id = target_device_id;

  IF FOUND THEN
    -- تحديث الإحصائيات الموجودة
    UPDATE sync_statistics SET
      total_syncs = total_syncs + 1,
      successful_syncs = successful_syncs + CASE WHEN sync_success THEN 1 ELSE 0 END,
      failed_syncs = failed_syncs + CASE WHEN sync_success THEN 0 ELSE 1 END,

      -- تحديث أحجام البيانات
      total_data_uploaded = total_data_uploaded + CASE WHEN sync_success THEN backup_size ELSE 0 END,
      average_backup_size = CASE
        WHEN sync_success AND successful_syncs > 0 THEN
          (average_backup_size * successful_syncs + backup_size) / (successful_syncs + 1)
        ELSE average_backup_size
      END,
      largest_backup_size = GREATEST(largest_backup_size, CASE WHEN sync_success THEN backup_size ELSE 0 END),
      smallest_backup_size = CASE
        WHEN smallest_backup_size = 0 AND sync_success THEN backup_size
        WHEN sync_success THEN LEAST(smallest_backup_size, backup_size)
        ELSE smallest_backup_size
      END,

      -- تحديث أوقات الرفع
      average_upload_time = CASE
        WHEN sync_success AND sync_duration IS NOT NULL AND successful_syncs > 0 THEN
          ((average_upload_time * successful_syncs) + sync_duration) / (successful_syncs + 1)
        ELSE average_upload_time
      END,
      fastest_upload_time = CASE
        WHEN sync_success AND sync_duration IS NOT NULL THEN
          CASE WHEN fastest_upload_time = '0 seconds'::INTERVAL THEN sync_duration
               ELSE LEAST(fastest_upload_time, sync_duration) END
        ELSE fastest_upload_time
      END,
      slowest_upload_time = CASE
        WHEN sync_success AND sync_duration IS NOT NULL THEN
          GREATEST(slowest_upload_time, sync_duration)
        ELSE slowest_upload_time
      END,

      -- تحديث معدل الضغط
      average_compression_ratio = CASE
        WHEN sync_success AND compression_ratio IS NOT NULL AND successful_syncs > 0 THEN
          (average_compression_ratio * successful_syncs + compression_ratio) / (successful_syncs + 1)
        ELSE average_compression_ratio
      END,

      -- تحديث التوقيتات
      last_sync_at = NOW(),
      last_successful_sync_at = CASE WHEN sync_success THEN NOW() ELSE last_successful_sync_at END,
      last_failed_sync_at = CASE WHEN sync_success THEN last_failed_sync_at ELSE NOW() END,

      -- تحديث معلومات الأخطاء
      last_error_message = CASE WHEN sync_success THEN NULL ELSE error_msg END,
      consecutive_failures = CASE WHEN sync_success THEN 0 ELSE consecutive_failures + 1 END,
      total_error_count = total_error_count + CASE WHEN sync_success THEN 0 ELSE 1 END,

      updated_at = NOW()
    WHERE user_id = target_user_id AND device_id = target_device_id;
  ELSE
    -- إنشاء سجل جديد
    INSERT INTO sync_statistics (
      user_id, device_id, total_syncs, successful_syncs, failed_syncs,
      first_sync_at, last_sync_at, last_successful_sync_at, last_failed_sync_at,
      total_data_uploaded, average_backup_size, largest_backup_size, smallest_backup_size,
      average_upload_time, fastest_upload_time, slowest_upload_time,
      average_compression_ratio, last_error_message, consecutive_failures, total_error_count
    ) VALUES (
      target_user_id, target_device_id, 1,
      CASE WHEN sync_success THEN 1 ELSE 0 END,
      CASE WHEN sync_success THEN 0 ELSE 1 END,
      NOW(), NOW(),
      CASE WHEN sync_success THEN NOW() ELSE NULL END,
      CASE WHEN sync_success THEN NULL ELSE NOW() END,
      CASE WHEN sync_success THEN backup_size ELSE 0 END,
      CASE WHEN sync_success THEN backup_size ELSE 0 END,
      CASE WHEN sync_success THEN backup_size ELSE 0 END,
      CASE WHEN sync_success THEN backup_size ELSE 0 END,
      COALESCE(sync_duration, '0 seconds'::INTERVAL),
      COALESCE(sync_duration, '0 seconds'::INTERVAL),
      COALESCE(sync_duration, '0 seconds'::INTERVAL),
      COALESCE(compression_ratio, 0),
      CASE WHEN sync_success THEN NULL ELSE error_msg END,
      CASE WHEN sync_success THEN 0 ELSE 1 END,
      CASE WHEN sync_success THEN 0 ELSE 1 END
    );
  END IF;
END;
$$;

-- 8. دالة تسجيل عملية مزامنة
CREATE OR REPLACE FUNCTION log_sync_operation(
  p_user_id UUID,
  p_device_id TEXT,
  p_operation_type TEXT,
  p_backup_id UUID DEFAULT NULL,
  p_status TEXT DEFAULT 'started',
  p_details JSONB DEFAULT '{}',
  p_data_size BIGINT DEFAULT 0,
  p_error_message TEXT DEFAULT NULL,
  p_error_code TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO sync_logs (
    user_id, device_id, backup_id, operation_type, status,
    operation_details, data_size, error_message, error_code
  ) VALUES (
    p_user_id, p_device_id, p_backup_id, p_operation_type, p_status,
    p_details, p_data_size, p_error_message, p_error_code
  ) RETURNING id INTO log_id;

  RETURN log_id;
END;
$$;

-- 9. دالة تحديث حالة عملية المزامنة
CREATE OR REPLACE FUNCTION update_sync_operation(
  p_log_id UUID,
  p_status TEXT,
  p_progress INTEGER DEFAULT NULL,
  p_error_message TEXT DEFAULT NULL,
  p_error_code TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE sync_logs SET
    status = p_status,
    progress_percentage = COALESCE(p_progress, progress_percentage),
    error_message = COALESCE(p_error_message, error_message),
    error_code = COALESCE(p_error_code, error_code),
    metadata = COALESCE(p_metadata, metadata),
    completed_at = CASE WHEN p_status IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE completed_at END
  WHERE id = p_log_id;
END;
$$;

-- 10. دالة الحصول على إحصائيات المستخدم الشاملة
CREATE OR REPLACE FUNCTION get_user_sync_dashboard(target_user_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSONB;
  backup_stats RECORD;
  sync_stats RECORD;
  limits_info RECORD;
BEGIN
  -- إحصائيات النسخ الاحتياطية
  SELECT
    COUNT(*) as total_backups,
    COALESCE(SUM(compressed_size), 0) as total_size_bytes,
    COALESCE(AVG(compression_ratio), 0) as avg_compression,
    MAX(created_at) as last_backup_date,
    MIN(created_at) as first_backup_date
  INTO backup_stats
  FROM compressed_backups
  WHERE user_id = target_user_id;

  -- إحصائيات المزامنة
  SELECT
    COALESCE(SUM(total_syncs), 0) as total_syncs,
    COALESCE(SUM(successful_syncs), 0) as successful_syncs,
    COALESCE(SUM(failed_syncs), 0) as failed_syncs,
    COALESCE(AVG(success_rate), 0) as avg_success_rate,
    MAX(last_sync_at) as last_sync_date
  INTO sync_stats
  FROM sync_statistics
  WHERE user_id = target_user_id;

  -- معلومات الحدود
  SELECT * INTO limits_info
  FROM user_sync_limits
  WHERE user_id = target_user_id;

  -- بناء النتيجة
  result := jsonb_build_object(
    'user_id', target_user_id,
    'backups', jsonb_build_object(
      'total_count', backup_stats.total_backups,
      'total_size_mb', ROUND((backup_stats.total_size_bytes::DECIMAL / 1024 / 1024), 2),
      'average_compression_ratio', ROUND(backup_stats.avg_compression, 2),
      'first_backup_date', backup_stats.first_backup_date,
      'last_backup_date', backup_stats.last_backup_date
    ),
    'sync_stats', jsonb_build_object(
      'total_syncs', sync_stats.total_syncs,
      'successful_syncs', sync_stats.successful_syncs,
      'failed_syncs', sync_stats.failed_syncs,
      'success_rate', ROUND(sync_stats.avg_success_rate, 2),
      'last_sync_date', sync_stats.last_sync_date
    ),
    'limits', jsonb_build_object(
      'max_backups', COALESCE(limits_info.max_backups, 5),
      'max_backup_size_mb', COALESCE(limits_info.max_backup_size_mb, 100),
      'storage_limit_mb', COALESCE(limits_info.total_storage_limit_mb, 500),
      'storage_used_mb', COALESCE(limits_info.current_storage_used_mb, 0),
      'storage_available_mb', COALESCE(limits_info.total_storage_limit_mb, 500) - COALESCE(limits_info.current_storage_used_mb, 0),
      'syncs_today', COALESCE(limits_info.syncs_today, 0),
      'max_daily_syncs', COALESCE(limits_info.max_daily_syncs, 10)
    ),
    'generated_at', NOW()
  );

  RETURN result;
END;
$$;

-- 11. دالة فحص حدود المستخدم قبل المزامنة
CREATE OR REPLACE FUNCTION check_sync_limits(
  target_user_id UUID,
  backup_size_mb INTEGER
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  limits_info RECORD;
  current_backups INTEGER;
  can_sync BOOLEAN := true;
  reasons TEXT[] := '{}';
BEGIN
  -- الحصول على حدود المستخدم
  SELECT * INTO limits_info
  FROM user_sync_limits
  WHERE user_id = target_user_id;

  -- إنشاء حدود افتراضية إذا لم توجد
  IF NOT FOUND THEN
    INSERT INTO user_sync_limits (user_id) VALUES (target_user_id);
    SELECT * INTO limits_info FROM user_sync_limits WHERE user_id = target_user_id;
  END IF;

  -- فحص عدد النسخ الحالية
  SELECT COUNT(*) INTO current_backups
  FROM compressed_backups
  WHERE user_id = target_user_id;

  -- فحص الحدود
  IF current_backups >= limits_info.max_backups THEN
    can_sync := false;
    reasons := array_append(reasons, 'تم الوصول للحد الأقصى من النسخ الاحتياطية (' || limits_info.max_backups || ')');
  END IF;

  IF backup_size_mb > limits_info.max_backup_size_mb THEN
    can_sync := false;
    reasons := array_append(reasons, 'حجم النسخة الاحتياطية يتجاوز الحد المسموح (' || limits_info.max_backup_size_mb || ' MB)');
  END IF;

  IF (limits_info.current_storage_used_mb + backup_size_mb) > limits_info.total_storage_limit_mb THEN
    can_sync := false;
    reasons := array_append(reasons, 'لا توجد مساحة تخزين كافية');
  END IF;

  -- فحص المزامنات اليومية
  IF limits_info.last_sync_date = CURRENT_DATE AND limits_info.syncs_today >= limits_info.max_daily_syncs THEN
    can_sync := false;
    reasons := array_append(reasons, 'تم الوصول للحد الأقصى من المزامنات اليومية (' || limits_info.max_daily_syncs || ')');
  END IF;

  RETURN jsonb_build_object(
    'can_sync', can_sync,
    'reasons', reasons,
    'current_backups', current_backups,
    'max_backups', limits_info.max_backups,
    'storage_used_mb', limits_info.current_storage_used_mb,
    'storage_limit_mb', limits_info.total_storage_limit_mb,
    'syncs_today', CASE WHEN limits_info.last_sync_date = CURRENT_DATE THEN limits_info.syncs_today ELSE 0 END,
    'max_daily_syncs', limits_info.max_daily_syncs
  );
END;
$$;

-- 12. دالة تحديث استخدام التخزين
CREATE OR REPLACE FUNCTION update_storage_usage(
  target_user_id UUID,
  size_change_mb BIGINT
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO user_sync_limits (user_id, current_storage_used_mb)
  VALUES (target_user_id, GREATEST(0, size_change_mb))
  ON CONFLICT (user_id) DO UPDATE SET
    current_storage_used_mb = GREATEST(0, user_sync_limits.current_storage_used_mb + size_change_mb),
    syncs_today = CASE
      WHEN user_sync_limits.last_sync_date = CURRENT_DATE THEN user_sync_limits.syncs_today + 1
      ELSE 1
    END,
    last_sync_date = CURRENT_DATE,
    updated_at = NOW();
END;
$$;

-- 13. تفعيل Row Level Security
ALTER TABLE compressed_backups ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_statistics ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sync_limits ENABLE ROW LEVEL SECURITY;

-- 14. سياسات الأمان
-- سياسات compressed_backups
CREATE POLICY "Users can manage their own backups" ON compressed_backups
  FOR ALL USING (auth.uid() = user_id);

-- سياسات sync_statistics
CREATE POLICY "Users can view their own sync stats" ON sync_statistics
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own sync stats" ON sync_statistics
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can modify their own sync stats" ON sync_statistics
  FOR UPDATE USING (auth.uid() = user_id);

-- سياسات sync_logs
CREATE POLICY "Users can view their own sync logs" ON sync_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own sync logs" ON sync_logs
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sync logs" ON sync_logs
  FOR UPDATE USING (auth.uid() = user_id);

-- سياسات user_sync_limits
CREATE POLICY "Users can manage their own limits" ON user_sync_limits
  FOR ALL USING (auth.uid() = user_id);

-- سياسات للمدراء
CREATE POLICY "Admins can manage all sync data" ON compressed_backups
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

CREATE POLICY "Admins can view all sync stats" ON sync_statistics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

CREATE POLICY "Admins can view all sync logs" ON sync_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM user_accounts
      WHERE user_id = auth.uid()
      AND (display_name ILIKE '%admin%' OR display_name ILIKE '%مدير%')
    )
  );

-- 15. منح الصلاحيات
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON compressed_backups TO authenticated;
GRANT ALL ON sync_statistics TO authenticated;
GRANT ALL ON sync_logs TO authenticated;
GRANT ALL ON user_sync_limits TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- منح صلاحيات تنفيذ الدوال
GRANT EXECUTE ON FUNCTION cleanup_old_backups TO authenticated;
GRANT EXECUTE ON FUNCTION update_sync_statistics TO authenticated;
GRANT EXECUTE ON FUNCTION log_sync_operation TO authenticated;
GRANT EXECUTE ON FUNCTION update_sync_operation TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_sync_dashboard TO authenticated;
GRANT EXECUTE ON FUNCTION check_sync_limits TO authenticated;
GRANT EXECUTE ON FUNCTION update_storage_usage TO authenticated;

-- 16. إنشاء Storage Bucket (يجب تنفيذه من Dashboard أو API)
-- INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
-- VALUES ('userbackups', 'userbackups', false, 104857600, ARRAY['application/gzip', 'application/json']);

-- 17. سياسات Storage
-- CREATE POLICY "Users can upload their own backups" ON storage.objects
--   FOR INSERT WITH CHECK (
--     bucket_id = 'userbackups'
--     AND auth.uid()::text = (storage.foldername(name))[1]
--   );

-- CREATE POLICY "Users can download their own backups" ON storage.objects
--   FOR SELECT USING (
--     bucket_id = 'userbackups'
--     AND auth.uid()::text = (storage.foldername(name))[1]
--   );

-- CREATE POLICY "Users can delete their own backups" ON storage.objects
--   FOR DELETE USING (
--     bucket_id = 'userbackups'
--     AND auth.uid()::text = (storage.foldername(name))[1]
--   );

-- 18. Triggers لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sync_statistics_updated_at
  BEFORE UPDATE ON sync_statistics
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sync_limits_updated_at
  BEFORE UPDATE ON user_sync_limits
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 19. رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE '✅ تم إنشاء نظام المزامنة السحابية بنجاح!';
    RAISE NOTICE '📊 الجداول المنشأة: compressed_backups, sync_statistics, sync_logs, user_sync_limits';
    RAISE NOTICE '🔧 الدوال المنشأة: 7 دوال لإدارة المزامنة والإحصائيات';
    RAISE NOTICE '🔒 سياسات الأمان: تم تطبيق RLS على جميع الجداول';
    RAISE NOTICE '📁 Storage Bucket: يجب إنشاء bucket "userbackups" من Dashboard';
END $$;

COMMIT;
