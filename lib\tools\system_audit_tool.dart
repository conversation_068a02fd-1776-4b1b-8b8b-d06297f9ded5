import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/app_migration_service.dart';
import '../services/legacy_cleanup_service.dart';
import '../services/system_audit_service.dart';
import 'update_system_checker.dart';

/// أداة سطر أوامر لمراجعة وتنظيف النظام
class SystemAuditTool {
  /// تشغيل المراجعة الشاملة
  static Future<void> runFullAudit() async {
    try {
      debugPrint('🚀 [AUDIT_TOOL] بدء المراجعة الشاملة للنظام...');
      debugPrint('=' * 60);

      // 1. المراجعة الشاملة
      final auditResults = await AppMigrationService.performSystemAudit();

      // 2. عرض النتائج
      _displayAuditResults(auditResults);

      // 3. اقتراح الإجراءات
      await _suggestActions(auditResults);

      debugPrint('=' * 60);
      debugPrint('✅ [AUDIT_TOOL] تم الانتهاء من المراجعة الشاملة');
    } catch (e) {
      debugPrint('❌ [AUDIT_TOOL] خطأ في المراجعة الشاملة: $e');
    }
  }

  /// تشغيل تنظيف البقايا القديمة
  static Future<void> runLegacyCleanup() async {
    try {
      debugPrint('🧹 [AUDIT_TOOL] بدء تنظيف البقايا القديمة...');
      debugPrint('-' * 40);

      // فحص البقايا أولاً
      final scanResults = await LegacyCleanupService.scanForLegacyData();

      if (scanResults['needs_cleanup'] == true) {
        debugPrint(
          '🔍 [AUDIT_TOOL] تم العثور على ${scanResults['total_found']} مفتاح قديم',
        );

        // تنظيف البقايا
        final cleanupResults = await LegacyCleanupService.performFullCleanup();

        debugPrint(
          '✅ [AUDIT_TOOL] تم تنظيف ${cleanupResults['total_cleaned']} مفتاح',
        );

        if (cleanupResults['errors'].isNotEmpty) {
          debugPrint(
            '⚠️ [AUDIT_TOOL] حدثت ${cleanupResults['errors'].length} أخطاء:',
          );
          for (final error in cleanupResults['errors']) {
            debugPrint('  - $error');
          }
        }
      } else {
        debugPrint('✅ [AUDIT_TOOL] لا توجد بقايا قديمة للتنظيف');
      }

      debugPrint('-' * 40);
      debugPrint('✅ [AUDIT_TOOL] تم الانتهاء من التنظيف');
    } catch (e) {
      debugPrint('❌ [AUDIT_TOOL] خطأ في التنظيف: $e');
    }
  }

  /// إنشاء تقرير مفصل
  static Future<String> generateReport() async {
    try {
      debugPrint('📋 [AUDIT_TOOL] إنشاء تقرير مفصل...');

      final auditResults = await SystemAuditService.performFullAudit();
      final report = SystemAuditService.generateDetailedReport(auditResults);

      // إضافة معلومات إضافية للتقرير
      final buffer = StringBuffer();
      buffer.writeln(report);
      buffer.writeln();
      buffer.writeln('🔧 معلومات إضافية:');
      buffer.writeln('-' * 30);

      // معلومات التطبيق
      buffer.writeln('📱 معلومات التطبيق:');
      buffer.writeln('  - إصدار النظام: 2.0 (محدث)');
      buffer.writeln('  - تاريخ التحديث: ${DateTime.now().toIso8601String()}');
      buffer.writeln(
        '  - حالة الترحيل: ${AppMigrationService.isMigrated ? 'مكتمل' : 'غير مكتمل'}',
      );
      buffer.writeln();

      // إحصائيات سريعة
      try {
        final legacyScan = await LegacyCleanupService.scanForLegacyData();
        buffer.writeln('📊 إحصائيات سريعة:');
        buffer.writeln(
          '  - البقايا القديمة: ${legacyScan['total_found']} مفتاح',
        );
        buffer.writeln(
          '  - يحتاج تنظيف: ${legacyScan['needs_cleanup'] ? 'نعم' : 'لا'}',
        );
      } catch (e) {
        buffer.writeln('⚠️ خطأ في جلب الإحصائيات: $e');
      }

      debugPrint('✅ [AUDIT_TOOL] تم إنشاء التقرير بنجاح');
      return buffer.toString();
    } catch (e) {
      debugPrint('❌ [AUDIT_TOOL] خطأ في إنشاء التقرير: $e');
      return 'خطأ في إنشاء التقرير: $e';
    }
  }

  /// عرض نتائج المراجعة
  static void _displayAuditResults(Map<String, dynamic> auditResults) {
    debugPrint('📊 [AUDIT_TOOL] نتائج المراجعة:');
    debugPrint('-' * 30);

    final overallStatus = auditResults['overall_status'];
    final statusIcon = _getStatusIcon(overallStatus);
    debugPrint('$statusIcon الحالة العامة: $overallStatus');
    debugPrint('');

    // عرض تفاصيل كل قسم
    final sections = [
      {'key': 'legacy_cleanup', 'title': '🧹 البقايا القديمة'},
      {'key': 'database_compatibility', 'title': '🗄️ توافق قاعدة البيانات'},
      {'key': 'services_status', 'title': '⚙️ حالة الخدمات'},
    ];

    for (final section in sections) {
      final sectionData = auditResults[section['key']];
      final sectionStatus = sectionData['status'];
      final sectionIcon = _getStatusIcon(sectionStatus);

      debugPrint('${section['title']}: $sectionIcon $sectionStatus');
    }
    debugPrint('');

    // عرض التوصيات
    final recommendations = auditResults['recommendations'] as List;
    if (recommendations.isNotEmpty) {
      debugPrint('💡 التوصيات:');
      for (final recommendation in recommendations) {
        debugPrint('  $recommendation');
      }
      debugPrint('');
    }

    // عرض الأخطاء
    final errors = auditResults['errors'] as List;
    if (errors.isNotEmpty) {
      debugPrint('❌ الأخطاء:');
      for (final error in errors) {
        debugPrint('  - $error');
      }
      debugPrint('');
    }
  }

  /// اقتراح الإجراءات
  static Future<void> _suggestActions(Map<String, dynamic> auditResults) async {
    debugPrint('🎯 [AUDIT_TOOL] الإجراءات المقترحة:');
    debugPrint('-' * 30);

    final overallStatus = auditResults['overall_status'];
    final legacyCleanup = auditResults['legacy_cleanup'];

    switch (overallStatus) {
      case 'excellent':
        debugPrint('✅ النظام يعمل بشكل ممتاز - لا حاجة لإجراءات إضافية');
        break;

      case 'good':
        debugPrint('👍 النظام يعمل بشكل جيد - مراقبة دورية مطلوبة');
        break;

      case 'needs_cleanup':
        debugPrint('🧹 يُنصح بتشغيل تنظيف البقايا القديمة:');
        debugPrint('   await SystemAuditTool.runLegacyCleanup();');

        // تشغيل التنظيف تلقائياً إذا كان آمناً
        if (legacyCleanup['cleanup_needed'] == true) {
          debugPrint('🔄 تشغيل التنظيف التلقائي...');
          await runLegacyCleanup();
        }
        break;

      case 'issues':
        debugPrint('⚠️ توجد مشاكل تحتاج انتباه:');
        debugPrint('   1. راجع إعدادات قاعدة البيانات');
        debugPrint('   2. تحقق من الخدمات المعطلة');
        debugPrint('   3. راجع سجل الأخطاء');
        break;

      case 'error':
        debugPrint('❌ توجد أخطاء خطيرة:');
        debugPrint('   1. راجع الاتصال بقاعدة البيانات');
        debugPrint('   2. تحقق من صحة الإعدادات');
        debugPrint('   3. راجع سجل الأخطاء المفصل');
        break;

      default:
        debugPrint('❓ حالة غير معروفة - يُنصح بمراجعة يدوية');
    }
    debugPrint('');
  }

  /// الحصول على أيقونة الحالة
  static String _getStatusIcon(String status) {
    switch (status) {
      case 'excellent':
      case 'clean':
      case 'compatible':
      case 'working':
        return '✅';
      case 'good':
        return '👍';
      case 'needs_cleanup':
        return '🧹';
      case 'issues':
      case 'incompatible':
        return '⚠️';
      case 'error':
        return '❌';
      default:
        return '❓';
    }
  }

  /// تشغيل فحص سريع
  static Future<void> runQuickCheck() async {
    try {
      debugPrint('⚡ [AUDIT_TOOL] فحص سريع للنظام...');

      // فحص البقايا القديمة
      final hasLegacy = await LegacyCleanupService.hasLegacyData();
      debugPrint('🧹 البقايا القديمة: ${hasLegacy ? '❌ موجودة' : '✅ نظيف'}');

      // فحص حالة الترحيل
      final migrationStatus = AppMigrationService.isMigrated;
      debugPrint(
        '🔄 حالة الترحيل: ${migrationStatus ? '✅ مكتمل' : '❌ غير مكتمل'}',
      );

      // فحص الاتصال بقاعدة البيانات
      try {
        await Supabase.instance.client
            .from('user_accounts')
            .select('*')
            .count(CountOption.exact);
        debugPrint('🗄️ قاعدة البيانات: ✅ متصلة');
      } catch (e) {
        debugPrint('🗄️ قاعدة البيانات: ❌ مشكلة في الاتصال');
      }

      // فحص نظام التحديثات
      try {
        final updateSystemWorking = await UpdateSystemChecker.runQuickCheck();
        debugPrint(
          '🔄 نظام التحديثات: ${updateSystemWorking ? "✅ يعمل" : "❌ مشاكل"}',
        );
      } catch (e) {
        debugPrint('🔄 نظام التحديثات: ❌ خطأ في الفحص');
      }

      debugPrint('⚡ انتهى الفحص السريع');
    } catch (e) {
      debugPrint('❌ [AUDIT_TOOL] خطأ في الفحص السريع: $e');
    }
  }
}
