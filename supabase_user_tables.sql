-- ملف إعداد جداول المستخدمين في Supabase لتطبيق iTower
-- يجب تنفيذ هذا الملف في Supabase Dashboard > SQL Editor

-- 1. إن<PERSON><PERSON><PERSON> جدول user_accounts
CREATE TABLE IF NOT EXISTS public.user_accounts (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    is_trial BOOLEAN DEFAULT true,
    expiry_millis BIGINT DEFAULT 0,
    creation_millis BIGINT DEFAULT 0,
    activation_millis BIGINT DEFAULT 0,
    active_package TEXT DEFAULT '',
    display_name TEXT DEFAULT '',
    trial_days INTEGER DEFAULT 15,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 2. إن<PERSON><PERSON>ء جدول user_devices
CREATE TABLE IF NOT EXISTS public.user_devices (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    device_name TEXT DEFAULT '',
    device_type TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, device_id)
);

-- 3. إعداد Row Level Security (RLS) لجدول user_accounts
ALTER TABLE public.user_accounts ENABLE ROW LEVEL SECURITY;

-- حذف السياسات الموجودة إذا كانت موجودة
DROP POLICY IF EXISTS "Users can read their own account data" ON public.user_accounts;
DROP POLICY IF EXISTS "Users can insert their own account data" ON public.user_accounts;
DROP POLICY IF EXISTS "Users can update their own account data" ON public.user_accounts;
DROP POLICY IF EXISTS "Users can delete their own account data" ON public.user_accounts;

-- سياسة القراءة: المستخدم يمكنه قراءة بياناته فقط
CREATE POLICY "Users can read their own account data" ON public.user_accounts
    FOR SELECT USING (auth.uid() = user_id);

-- سياسة الإدراج: المستخدم يمكنه إنشاء حسابه فقط
CREATE POLICY "Users can insert their own account data" ON public.user_accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- سياسة التحديث: المستخدم يمكنه تحديث بياناته فقط
CREATE POLICY "Users can update their own account data" ON public.user_accounts
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسة الحذف: المستخدم يمكنه حذف حسابه فقط
CREATE POLICY "Users can delete their own account data" ON public.user_accounts
    FOR DELETE USING (auth.uid() = user_id);

-- 4. إعداد Row Level Security (RLS) لجدول user_devices
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;

-- حذف السياسات الموجودة إذا كانت موجودة
DROP POLICY IF EXISTS "Users can read their own devices" ON public.user_devices;
DROP POLICY IF EXISTS "Users can insert their own devices" ON public.user_devices;
DROP POLICY IF EXISTS "Users can update their own devices" ON public.user_devices;
DROP POLICY IF EXISTS "Users can delete their own devices" ON public.user_devices;

-- سياسة القراءة: المستخدم يمكنه قراءة أجهزته فقط
CREATE POLICY "Users can read their own devices" ON public.user_devices
    FOR SELECT USING (auth.uid() = user_id);

-- سياسة الإدراج: المستخدم يمكنه إضافة أجهزته فقط
CREATE POLICY "Users can insert their own devices" ON public.user_devices
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- سياسة التحديث: المستخدم يمكنه تحديث أجهزته فقط
CREATE POLICY "Users can update their own devices" ON public.user_devices
    FOR UPDATE USING (auth.uid() = user_id);

-- سياسة الحذف: المستخدم يمكنه حذف أجهزته فقط
CREATE POLICY "Users can delete their own devices" ON public.user_devices
    FOR DELETE USING (auth.uid() = user_id);

-- 5. إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON public.user_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_accounts_is_trial ON public.user_accounts(is_trial);
CREATE INDEX IF NOT EXISTS idx_user_accounts_expiry ON public.user_accounts(expiry_millis);

CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON public.user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON public.user_devices(device_id);

-- 6. إنشاء دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 7. إنشاء triggers لتحديث updated_at
DROP TRIGGER IF EXISTS update_user_accounts_updated_at ON public.user_accounts;
CREATE TRIGGER update_user_accounts_updated_at 
    BEFORE UPDATE ON public.user_accounts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_devices_updated_at ON public.user_devices;
CREATE TRIGGER update_user_devices_updated_at 
    BEFORE UPDATE ON public.user_devices 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. منح الصلاحيات للمستخدمين المصادق عليهم
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.user_accounts TO authenticated;
GRANT ALL ON public.user_devices TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- 9. إنشاء دالة للتحقق من انتهاء الفترة التجريبية (موحدة)
-- تم حذف الدالة المكررة - استخدم الدالة في supabase_database_final.sql

-- تم الانتهاء من إعداد جداول المستخدمين
-- يمكن الآن استخدام التطبيق مع Supabase بشكل آمن

-- ملاحظات مهمة:
-- 1. يجب تنفيذ هذا الملف في Supabase Dashboard > SQL Editor
-- 2. تأكد من أن Row Level Security مفعل
-- 3. تأكد من أن المستخدمين لديهم صلاحيات authenticated
