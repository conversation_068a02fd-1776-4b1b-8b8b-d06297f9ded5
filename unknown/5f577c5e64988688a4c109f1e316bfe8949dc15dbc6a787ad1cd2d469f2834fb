import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:sqflite/sqflite.dart' as sqflite;
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة النسخ الاحتياطي المحلي (منفصلة تماماً عن النسخ السحابي)
class LocalBackupService {
  static final LocalBackupService _instance = LocalBackupService._internal();
  factory LocalBackupService() => _instance;
  LocalBackupService._internal();

  // مفاتيح SharedPreferences للنسخ المحلي
  static const String _lastLocalBackupKey = 'last_local_backup_date';
  static const String _localBackupCountKey = 'local_backup_count';

  /// الحصول على مسار قاعدة البيانات
  Future<String> getDatabasePath() async {
    final dbPath = await sqflite.getDatabasesPath();
    return '$dbPath/itower.db';
  }

  /// إنشاء نسخة احتياطية محلية (نسخ ملف قاعدة البيانات)
  Future<LocalBackupResult> createLocalBackup() async {
    try {
      print('🔄 بدء إنشاء النسخة الاحتياطية المحلية...');

      // الحصول على مسار قاعدة البيانات
      final dbPath = await getDatabasePath();
      final dbFile = File(dbPath);

      if (!await dbFile.exists()) {
        throw LocalBackupException('ملف قاعدة البيانات غير موجود في: $dbPath');
      }

      print('✅ تم العثور على قاعدة البيانات - الحجم: ${await dbFile.length()} bytes');

      // إنشاء اسم ملف مع التاريخ والوقت
      final now = DateTime.now();
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm-ss').format(now);
      final fileName = 'iTower_local_backup_$timestamp.db';

      // إنشاء مجلد النسخ الاحتياطية المحلية
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDir.path}/local_backups');
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
        print('📁 تم إنشاء مجلد النسخ المحلية: ${backupDir.path}');
      }

      final outputPath = '${backupDir.path}/$fileName';

      print('📋 نسخ ملف قاعدة البيانات...');
      
      // نسخ ملف قاعدة البيانات
      await dbFile.copy(outputPath);

      final backupFile = File(outputPath);
      final fileSize = await backupFile.length();

      print('✅ تم إنشاء النسخة المحلية بنجاح: $fileName');

      // حفظ معلومات النسخة الاحتياطية
      await _saveLocalBackupInfo(fileName, outputPath, now, fileSize);

      return LocalBackupResult(
        success: true,
        fileName: fileName,
        filePath: outputPath,
        fileSize: fileSize,
        createdAt: now,
        message: 'تم إنشاء النسخة الاحتياطية المحلية بنجاح',
      );

    } catch (e) {
      print('❌ خطأ في إنشاء النسخة الاحتياطية المحلية: $e');
      
      return LocalBackupResult(
        success: false,
        fileName: '',
        filePath: '',
        fileSize: 0,
        createdAt: DateTime.now(),
        message: 'فشل في إنشاء النسخة الاحتياطية المحلية: $e',
      );
    }
  }

  /// استعادة من نسخة احتياطية محلية
  Future<LocalRestoreResult> restoreFromLocalBackup(String backupPath) async {
    try {
      print('🔄 بدء استعادة النسخة الاحتياطية المحلية...');

      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        throw LocalBackupException('ملف النسخة الاحتياطية غير موجود: $backupPath');
      }

      print('✅ تم العثور على ملف النسخة الاحتياطية');

      // الحصول على مسار قاعدة البيانات الحالية
      final dbPath = await getDatabasePath();

      print('📋 استبدال قاعدة البيانات الحالية...');
      
      // نسخ ملف النسخة الاحتياطية لاستبدال قاعدة البيانات الحالية
      await backupFile.copy(dbPath);

      print('✅ تم استعادة النسخة الاحتياطية المحلية بنجاح');

      return LocalRestoreResult(
        success: true,
        message: 'تم استعادة النسخة الاحتياطية المحلية بنجاح',
        restoredFrom: backupPath,
      );

    } catch (e) {
      print('❌ خطأ في استعادة النسخة الاحتياطية المحلية: $e');
      
      return LocalRestoreResult(
        success: false,
        message: 'فشل في استعادة النسخة الاحتياطية المحلية: $e',
        restoredFrom: backupPath,
      );
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية المحلية
  Future<List<LocalBackupInfo>> getLocalBackups() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory('${appDir.path}/local_backups');

      if (!await backupDir.exists()) {
        return <LocalBackupInfo>[];
      }

      final files = await backupDir.list().toList();
      final backups = <LocalBackupInfo>[];

      for (final file in files) {
        if (file is File && file.path.endsWith('.db')) {
          final stat = await file.stat();
          final fileName = file.path.split('/').last;
          
          backups.add(LocalBackupInfo(
            name: fileName,
            path: file.path,
            size: stat.size,
            createdAt: stat.modified,
          ));
        }
      }

      // ترتيب حسب التاريخ (الأحدث أولاً)
      backups.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return backups;
    } catch (e) {
      print('خطأ في الحصول على قائمة النسخ المحلية: $e');
      return <LocalBackupInfo>[];
    }
  }

  /// حذف نسخة احتياطية محلية
  Future<bool> deleteLocalBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (await backupFile.exists()) {
        await backupFile.delete();
        print('تم حذف النسخة المحلية: $backupPath');
        return true;
      }
      return false;
    } catch (e) {
      print('خطأ في حذف النسخة المحلية: $e');
      return false;
    }
  }

  /// حفظ معلومات النسخة الاحتياطية المحلية
  Future<void> _saveLocalBackupInfo(String fileName, String filePath, DateTime createdAt, int fileSize) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastLocalBackupKey, createdAt.toIso8601String());
      
      final currentCount = prefs.getInt(_localBackupCountKey) ?? 0;
      await prefs.setInt(_localBackupCountKey, currentCount + 1);
      
      print('تم حفظ معلومات النسخة المحلية: $fileName');
    } catch (e) {
      print('خطأ في حفظ معلومات النسخة المحلية: $e');
    }
  }

  /// الحصول على إحصائيات النسخ المحلي
  Future<LocalBackupStats> getLocalBackupStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastBackupStr = prefs.getString(_lastLocalBackupKey);
      final backupCount = prefs.getInt(_localBackupCountKey) ?? 0;
      
      DateTime? lastBackupDate;
      if (lastBackupStr != null) {
        lastBackupDate = DateTime.parse(lastBackupStr);
      }

      final backups = await getLocalBackups();

      return LocalBackupStats(
        totalBackups: backups.length,
        lastBackupDate: lastBackupDate,
        totalBackupCount: backupCount,
        totalSize: backups.fold(0, (sum, backup) => sum + backup.size),
      );
    } catch (e) {
      print('خطأ في الحصول على إحصائيات النسخ المحلي: $e');
      return LocalBackupStats(
        totalBackups: 0,
        lastBackupDate: null,
        totalBackupCount: 0,
        totalSize: 0,
      );
    }
  }

  /// تنسيق حجم الملف
  String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}

/// نتيجة النسخ الاحتياطي المحلي
class LocalBackupResult {
  final bool success;
  final String fileName;
  final String filePath;
  final int fileSize;
  final DateTime createdAt;
  final String message;

  LocalBackupResult({
    required this.success,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.createdAt,
    required this.message,
  });
}

/// نتيجة استعادة النسخة المحلية
class LocalRestoreResult {
  final bool success;
  final String message;
  final String restoredFrom;

  LocalRestoreResult({
    required this.success,
    required this.message,
    required this.restoredFrom,
  });
}

/// معلومات النسخة الاحتياطية المحلية
class LocalBackupInfo {
  final String name;
  final String path;
  final int size;
  final DateTime createdAt;

  LocalBackupInfo({
    required this.name,
    required this.path,
    required this.size,
    required this.createdAt,
  });

  String get formattedSize => LocalBackupService().formatFileSize(size);
  String get formattedDate => DateFormat('yyyy-MM-dd HH:mm').format(createdAt);
}

/// إحصائيات النسخ المحلي
class LocalBackupStats {
  final int totalBackups;
  final DateTime? lastBackupDate;
  final int totalBackupCount;
  final int totalSize;

  LocalBackupStats({
    required this.totalBackups,
    required this.lastBackupDate,
    required this.totalBackupCount,
    required this.totalSize,
  });

  String get formattedTotalSize => LocalBackupService().formatFileSize(totalSize);
  String? get formattedLastBackupDate => lastBackupDate != null 
      ? DateFormat('yyyy-MM-dd HH:mm').format(lastBackupDate!) 
      : null;
}

/// استثناء النسخ المحلي
class LocalBackupException implements Exception {
  final String message;
  LocalBackupException(this.message);
  
  @override
  String toString() => 'LocalBackupException: $message';
}
