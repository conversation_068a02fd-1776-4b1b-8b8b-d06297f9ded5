import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'deleted_account_detector.dart';
import 'multi_device_service.dart';

class AccountService {
  static const String _cachePrefix = 'cache_';
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // تم حذف _defaultAccountData لأنه غير مستخدم

  /// إنشاء حساب جديد وفقاً للنظام الجديد
  static Future<Map<String, dynamic>> createAccountV2(
    String userId,
    String displayName,
    String email,
  ) async {
    try {
      debugPrint('🔍 [ACCOUNT_V2] إنشاء حساب جديد: $userId, $displayName');

      // ✅ فحص وجود المستخدم في auth.users أولاً
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null || currentUser.id != userId) {
        debugPrint(
          '❌ [ACCOUNT_V2] المستخدم غير موجود في auth.users أو الجلسة غير صالحة',
        );
        debugPrint('🔄 [ACCOUNT_V2] إنهاء الجلسة وإغلاق التطبيق...');

        // إنهاء الجلسة
        await Supabase.instance.client.auth.signOut();

        // إغلاق التطبيق
        throw Exception('FORCE_EXIT: المستخدم غير موجود - يجب إغلاق التطبيق');
      }

      // ✅ فحص إضافي: محاولة إنشاء الحساب مباشرة والتعامل مع خطأ Foreign Key
      // إذا فشل بسبب عدم وجود المستخدم في auth.users، سيتم التعامل معه في catch

      // التحقق من وجود الحساب مسبقاً
      final existingAccount = await getAccountDataV2(userId);
      if (existingAccount != null) {
        debugPrint('ℹ️ [ACCOUNT_V2] الحساب موجود مسبقاً: $userId');
        return existingAccount;
      }

      // حساب تاريخ انتهاء الفترة التجريبية
      final now = DateTime.now();
      final trialEndDate = now.add(const Duration(days: 15));

      // ✅ استخدام دالة إنشاء الحساب التجريبي الجديدة في Supabase
      final result = await Supabase.instance.client.rpc(
        'create_trial_account',
        params: {
          'p_user_id': userId,
          'p_display_name': displayName,
          'p_email': email,
          'p_trial_days': 15,
        },
      );

      debugPrint(
        '✅ [ACCOUNT_V2] تم إنشاء الحساب باستخدام دالة Supabase: $result',
      );

      debugPrint(
        '🔍 [ACCOUNT_V2] تاريخ انتهاء الفترة التجريبية: $trialEndDate',
      );
      debugPrint(
        '🔍 [ACCOUNT_V2] expiry_millis: ${trialEndDate.millisecondsSinceEpoch}',
      );

      // ✅ الحساب تم إنشاؤه بالفعل باستخدام دالة Supabase
      // النتيجة تحتوي على جميع بيانات الحساب المُنشأ
      debugPrint('✅ [ACCOUNT_V2] تم إنشاء الحساب بنجاح: $userId');

      // إرجاع النتيجة من دالة Supabase
      return result;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_V2] خطأ في إنشاء الحساب: $e');

      // ✅ فحص إذا كان الخطأ بسبب عدم وجود المستخدم في auth.users
      if (e.toString().contains('user_accounts_user_id_fkey') ||
          e.toString().contains('is not present in table "users"')) {
        debugPrint('❌ [ACCOUNT_V2] المستخدم غير موجود في جدول auth.users');
        debugPrint('🔄 [ACCOUNT_V2] إنهاء الجلسة وإغلاق التطبيق...');

        // إنهاء الجلسة
        await Supabase.instance.client.auth.signOut();

        // إغلاق التطبيق
        throw Exception(
          'FORCE_EXIT: المستخدم غير موجود في قاعدة البيانات - يجب إغلاق التطبيق',
        );
      }

      rethrow;
    }
  }

  /// إصلاح الحساب المنتهي ليصبح تجريبي صحيح
  static Future<bool> fixExpiredTrialAccount(String userId) async {
    try {
      debugPrint('🔧 [ACCOUNT_V2] إصلاح الحساب المنتهي: $userId');

      // حساب تاريخ انتهاء جديد (15 يوم من الآن)
      final now = DateTime.now();
      final trialEndDate = now.add(const Duration(days: 15));

      // تحديث بيانات الحساب
      await Supabase.instance.client
          .from('user_accounts')
          .update({
            'account_status': 'trial',
            'trial_days_remaining': 15,
            'updated_at': now.toIso8601String(),
          })
          .eq('user_id', userId);

      debugPrint('✅ [ACCOUNT_V2] تم إصلاح الحساب بنجاح');
      debugPrint('🔍 [ACCOUNT_V2] تاريخ الانتهاء الجديد: $trialEndDate');

      return true;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_V2] خطأ في إصلاح الحساب: $e');
      return false;
    }
  }

  /// إصلاح تلقائي للحسابات التجريبية المحولة خطأً إلى expired
  static Future<void> autoFixTrialAccounts() async {
    try {
      debugPrint('🔧 [AUTO_FIX] بدء الإصلاح التلقائي للحسابات التجريبية');

      // البحث عن الحسابات التجريبية المحولة خطأً إلى expired
      // (بما في ذلك التي تم تحويل trial_days_remaining إلى 0)
      final expiredTrialAccounts = await Supabase.instance.client
          .from('user_accounts')
          .select('user_id, created_at, trial_days_remaining')
          .eq('account_status', 'expired')
          .eq('subscription_type', 'trial');

      debugPrint(
        '🔍 [AUTO_FIX] تم العثور على ${expiredTrialAccounts.length} حساب يحتاج إصلاح',
      );

      for (final account in expiredTrialAccounts) {
        final userId = account['user_id'] as String;
        final createdAt = DateTime.parse(account['created_at'] as String);

        // حساب تاريخ الانتهاء من تاريخ الإنشاء (15 يوم)
        final trialEndDate = createdAt.add(const Duration(days: 15));
        final now = DateTime.now();
        final isStillValid = now.isBefore(trialEndDate);

        if (isStillValid) {
          // الحساب ما زال صالحاً - أصلحه
          final remainingDays = trialEndDate.difference(now).inDays + 1;

          await Supabase.instance.client
              .from('user_accounts')
              .update({
                'account_status': 'trial',
                'trial_days_remaining': remainingDays,
                'updated_at': now.toIso8601String(),
              })
              .eq('user_id', userId);

          debugPrint(
            '✅ [AUTO_FIX] تم إصلاح الحساب: $userId ($remainingDays أيام متبقية)',
          );
          debugPrint('📅 [AUTO_FIX] تاريخ الانتهاء: $trialEndDate');
        } else {
          debugPrint(
            '⏰ [AUTO_FIX] الحساب منتهي فعلاً: $userId (انتهى في: $trialEndDate)',
          );
        }
      }

      debugPrint('✅ [AUTO_FIX] انتهى الإصلاح التلقائي');
    } catch (e) {
      debugPrint('❌ [AUTO_FIX] خطأ في الإصلاح التلقائي: $e');
    }
  }

  /// جلب بيانات الحساب وفقاً للنظام الجديد مع فحص حالة الفترة التجريبية
  static Future<Map<String, dynamic>?> getAccountDataV2(String userId) async {
    try {
      debugPrint('🔍 [ACCOUNT_V2] جلب بيانات الحساب: $userId');

      // ✅ فحص وتحديث حالة الفترة التجريبية أولاً
      try {
        final trialStatus = await Supabase.instance.client.rpc(
          'check_trial_status',
          params: {'p_user_id': userId},
        );

        if (trialStatus != null && trialStatus['error'] == null) {
          debugPrint(
            '✅ [ACCOUNT_V2] تم فحص وتحديث حالة الحساب: ${trialStatus['account_status']}',
          );
        }
      } catch (e) {
        debugPrint('⚠️ [ACCOUNT_V2] خطأ في فحص حالة الفترة التجريبية: $e');
        // نتابع بجلب البيانات حتى لو فشل الفحص
      }

      // جلب البيانات المحدثة
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      if (response != null) {
        debugPrint('✅ [ACCOUNT_V2] تم جلب بيانات الحساب بنجاح');
        return response;
      } else {
        debugPrint('ℹ️ [ACCOUNT_V2] لا يوجد حساب للمستخدم: $userId');
        return null;
      }
    } catch (e) {
      debugPrint('❌ [ACCOUNT_V2] خطأ في جلب بيانات الحساب: $e');
      return null;
    }
  }

  /// تسجيل النشاط في سجل الأنشطة
  static Future<void> _logActivity(
    String userId,
    String actionType,
    Map<String, dynamic> details, {
    String? deviceId,
    String? adminUserId,
    String? adminReason,
  }) async {
    try {
      await Supabase.instance.client.rpc(
        'log_user_activity',
        params: {
          'p_user_id': userId,
          'p_action_type': actionType,
          'p_action_details': details,
          'p_device_id': deviceId,
          'p_admin_user_id': adminUserId,
          'p_admin_reason': adminReason,
        },
      );
    } catch (e) {
      debugPrint('⚠️ [ACCOUNT] خطأ في تسجيل النشاط: $e');
    }
  }

  /// تحديث حالة الحساب
  static Future<bool> updateAccountStatus(
    String userId,
    String newStatus, {
    String? reason,
    String? adminUserId,
  }) async {
    try {
      debugPrint('🔄 [ACCOUNT_V2] تحديث حالة الحساب: $userId -> $newStatus');

      await Supabase.instance.client
          .from('user_accounts')
          .update({
            'account_status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
            if (reason != null) 'lock_reason': reason,
          })
          .eq('user_id', userId);

      // تسجيل النشاط
      await _logActivity(
        userId,
        'account_status_changed',
        {'new_status': newStatus, 'reason': reason},
        adminUserId: adminUserId,
        adminReason: reason,
      );

      debugPrint('✅ [ACCOUNT_V2] تم تحديث حالة الحساب بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_V2] خطأ في تحديث حالة الحساب: $e');
      return false;
    }
  }

  /// تفعيل كود التفعيل
  static Future<Map<String, dynamic>?> activateCode(
    String userId,
    String code,
  ) async {
    try {
      debugPrint('🎫 [ACCOUNT_V2] تفعيل الكود: $code للمستخدم: $userId');

      // التحقق من صحة الكود
      final codeData = await Supabase.instance.client
          .from('activation_codes')
          .select('*')
          .eq('code', code)
          .maybeSingle();

      if (codeData == null) {
        throw Exception('كود التفعيل غير صحيح');
      }

      // التحقق من عدم استخدام الكود مسبقاً
      if (codeData['code_status'] != 'active') {
        throw Exception('تم استخدام هذا الكود مسبقاً أو أنه غير نشط');
      }

      // ✅ التحقق من وجود البيانات المطلوبة
      debugPrint('🔍 [ACCOUNT_V2] بيانات الكود: $codeData');

      if (codeData['duration_days'] == null) {
        debugPrint(
          '⚠️ [ACCOUNT_V2] حقل duration_days مفقود، استخدام القيمة الافتراضية 30',
        );
      }

      if (codeData['code_type'] == null) {
        debugPrint(
          '⚠️ [ACCOUNT_V2] حقل code_type مفقود، استخدام القيمة الافتراضية monthly',
        );
      }

      // التحقق من انتهاء صلاحية الكود
      if (codeData['expires_at'] != null) {
        final expiryDate = DateTime.parse(codeData['expires_at']);
        if (expiryDate.isBefore(DateTime.now())) {
          throw Exception('كود التفعيل منتهي الصلاحية');
        }
      }

      // حساب تاريخ انتهاء الاشتراك الجديد
      final durationDays =
          (codeData['duration_days'] as int?) ?? 30; // ✅ الحقل الصحيح
      final subscriptionEnd = DateTime.now().add(Duration(days: durationDays));

      debugPrint('🔍 [ACCOUNT_V2] مدة التفعيل: $durationDays يوم');
      debugPrint('🔍 [ACCOUNT_V2] تاريخ انتهاء الاشتراك: $subscriptionEnd');

      // ✅ معالجة آمنة لنوع الاشتراك
      final subscriptionType =
          (codeData['code_type'] as String?) ?? 'monthly'; // ✅ الحقل الصحيح

      debugPrint('🔍 [ACCOUNT_V2] نوع الاشتراك: $subscriptionType');

      // تحديث بيانات الحساب
      await Supabase.instance.client
          .from('user_accounts')
          .update({
            'account_status': 'active',
            'subscription_type': subscriptionType,
            'subscription_start': DateTime.now().toIso8601String(),
            'subscription_end': subscriptionEnd.toIso8601String(),
            'trial_days_remaining': 0,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId);

      // تحديث حالة الكود
      await Supabase.instance.client
          .from('activation_codes')
          .update({
            'code_status': 'used', // ✅ الحقل الصحيح
            'used_at': DateTime.now().toIso8601String(),
            'used_by': userId,
          })
          .eq('code', code);

      // تسجيل النشاط
      await _logActivity(userId, 'code_activated', {
        'code': code,
        'code_type': subscriptionType, // ✅ استخدام المتغير الآمن
        'duration_days': durationDays,
        'subscription_end': subscriptionEnd.toIso8601String(),
      });

      // إزالة علامة انتهاء الحساب عند التفعيل
      await DeletedAccountDetector.clearAccountExpiredFlag();

      debugPrint('✅ [ACCOUNT_V2] تم تفعيل الكود بنجاح');
      return {
        'success': true,
        'subscription_type': subscriptionType, // ✅ استخدام المتغير الآمن
        'subscription_end': subscriptionEnd.toIso8601String(),
        'duration_days': durationDays,
      };
    } catch (e) {
      debugPrint('❌ [ACCOUNT_V2] خطأ في تفعيل الكود: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // إنشاء حساب جديد في Supabase (مهجور - استخدم createAccountV2)
  @Deprecated('استخدم createAccountV2 بدلاً من ذلك')
  static Future<Map<String, dynamic>> createAccount(
    String userId, {
    String? displayName,
  }) async {
    try {
      debugPrint('🔍 [ACCOUNT] بدء إنشاء الحساب للمستخدم: $userId');
      debugPrint('🔍 [ACCOUNT] اسم المستخدم: $displayName');

      final now = DateTime.now();
      final expiryDate = now.add(const Duration(days: 15));
      debugPrint('🔍 [ACCOUNT] تاريخ الانتهاء: $expiryDate');

      // ⚠️ هذه الدالة مهجورة - استخدم createAccountV2 بدلاً من ذلك
      final accountData = {
        'user_id': userId,
        'display_name': displayName ?? '',
        'account_status': 'trial',
        'subscription_type': 'trial',
        'trial_days_remaining': 15,
        'max_devices': 3,
        'is_locked': false,
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
        'created_by': userId,
      };

      // التحقق من صحة الجلسة وتجديدها إذا لزم الأمر
      debugPrint('🔍 [ACCOUNT] فحص الجلسة الحالية...');
      final currentUser = Supabase.instance.client.auth.currentUser;
      debugPrint('🔍 [ACCOUNT] المستخدم الحالي: ${currentUser?.id}');

      if (currentUser == null) {
        debugPrint('❌ [ACCOUNT] لا يوجد مستخدم مسجل دخول');
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      if (currentUser.id != userId) {
        debugPrint(
          '❌ [ACCOUNT] معرف المستخدم غير متطابق: ${currentUser.id} != $userId',
        );
        throw Exception('معرف المستخدم غير متطابق');
      }

      debugPrint('✅ [ACCOUNT] المستخدم متطابق: ${currentUser.id}');

      // التحقق من حالة الجلسة والاتصال
      try {
        debugPrint('🔍 [ACCOUNT] فحص حالة الجلسة...');
        final session = Supabase.instance.client.auth.currentSession;
        if (session == null) {
          debugPrint('❌ [ACCOUNT] لا توجد جلسة نشطة');
          throw Exception('لا توجد جلسة نشطة');
        }
        debugPrint('✅ [ACCOUNT] الجلسة نشطة ومتاحة للاستخدام');
        debugPrint(
          '🔍 [ACCOUNT] Access Token متاح: ${session.accessToken.isNotEmpty}',
        );
        debugPrint('🔍 [ACCOUNT] انتهاء الجلسة: ${session.expiresAt}');
        debugPrint('🔍 [ACCOUNT] الوقت الحالي: ${DateTime.now()}');

        // اختبار الاتصال بقاعدة البيانات
        try {
          debugPrint('🔍 [ACCOUNT] اختبار الاتصال بقاعدة البيانات...');
          final testResponse = await Supabase.instance.client
              .from('user_accounts')
              .select('count')
              .count(CountOption.exact);
          debugPrint(
            '✅ [ACCOUNT] اختبار الاتصال نجح - عدد السجلات: ${testResponse.count}',
          );
        } catch (testError) {
          debugPrint('❌ [ACCOUNT] فشل اختبار الاتصال: $testError');
          debugPrint('❌ [ACCOUNT] نوع خطأ الاتصال: ${testError.runtimeType}');
        }
      } catch (e) {
        debugPrint('مشكلة في الجلسة: $e');
        // محاولة تجديد الجلسة
        try {
          await Supabase.instance.client.auth.refreshSession();
          debugPrint('تم تجديد جلسة المستخدم بنجاح');
        } catch (refreshError) {
          debugPrint('فشل في تجديد الجلسة: $refreshError');
          throw Exception('فشل في الوصول للجلسة: $refreshError');
        }
      }

      // إدراج البيانات في Supabase مع إعادة المحاولة
      bool insertSuccess = false;
      int retryCount = 0;
      const maxRetries = 3;
      Exception? lastError;

      while (!insertSuccess && retryCount < maxRetries) {
        try {
          retryCount++;
          debugPrint('🔍 [ACCOUNT] محاولة إنشاء الحساب #$retryCount');
          debugPrint('🔍 [ACCOUNT] بيانات الحساب: $accountData');

          // فحص المستخدم الحالي مرة أخرى
          final currentUser = Supabase.instance.client.auth.currentUser;
          debugPrint(
            '🔍 [ACCOUNT] المستخدم الحالي قبل الإدراج: ${currentUser?.id}',
          );
          debugPrint('🔍 [ACCOUNT] المستخدم المطلوب: $userId');

          // فحص الجلسة مرة أخرى
          final sessionBeforeInsert =
              Supabase.instance.client.auth.currentSession;
          debugPrint(
            '🔍 [ACCOUNT] حالة الجلسة قبل الإدراج: ${sessionBeforeInsert != null ? "موجودة" : "غير موجودة"}',
          );

          if (sessionBeforeInsert != null) {
            final timeUntilExpiry =
                sessionBeforeInsert.expiresAt! -
                (DateTime.now().millisecondsSinceEpoch / 1000).round();
            debugPrint(
              '🔍 [ACCOUNT] الوقت المتبقي للجلسة: $timeUntilExpiry ثانية',
            );
          }

          debugPrint('🔍 [ACCOUNT] بدء عملية الإدراج...');
          final response = await Supabase.instance.client
              .from('user_accounts')
              .insert(accountData)
              .select();

          debugPrint('✅ [ACCOUNT] استجابة الإدراج: $response');
          insertSuccess = true;
          debugPrint(
            '✅ [ACCOUNT] تم إنشاء حساب جديد في Supabase للمستخدم: $userId',
          );
        } catch (e) {
          debugPrint('خطأ في محاولة #$retryCount: $e');
          debugPrint('نوع الخطأ: ${e.runtimeType}');
          if (e is PostgrestException) {
            debugPrint(
              'PostgrestException - Code: ${e.code}, Message: ${e.message}',
            );
            debugPrint('Details: ${e.details}');
            debugPrint('Hint: ${e.hint}');
          }
          lastError = Exception(e.toString());

          if (e.toString().contains(
            'relation "user_accounts" does not exist',
          )) {
            debugPrint('❌ جدول user_accounts غير موجود في Supabase');
            debugPrint(
              '📋 يرجى تنفيذ الكود التالي في Supabase Dashboard > SQL Editor:',
            );
            debugPrint('CREATE TABLE user_accounts (...);');
            debugPrint('ALTER TABLE user_accounts ENABLE ROW LEVEL SECURITY;');
            debugPrint('CREATE POLICY ... ON user_accounts;');
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('permission denied') ||
              e.toString().contains('insufficient_privilege')) {
            debugPrint('❌ مشكلة في صلاحيات قاعدة البيانات');
            debugPrint('📋 تحقق من Row Level Security policies في Supabase');
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('violates row-level security')) {
            debugPrint('❌ مشكلة في Row Level Security');
            debugPrint('📋 تحقق من سياسات الأمان في Supabase Dashboard');
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('duplicate key value')) {
            debugPrint('⚠️ المستخدم موجود مسبقاً في user_accounts');
            // جلب البيانات الموجودة بدلاً من إنشاء جديدة
            final existingData = await getAccountData(userId, useCache: false);
            if (existingData != null) {
              return existingData;
            }
            break; // لا فائدة من إعادة المحاولة
          } else if (e.toString().contains('Auth session missing') &&
              retryCount < maxRetries) {
            debugPrint('⚠️ جلسة منتهية، محاولة تجديد الجلسة...');
            try {
              await Supabase.instance.client.auth.refreshSession();
              await Future.delayed(
                Duration(milliseconds: 1000 * retryCount),
              ); // تأخير أطول
              debugPrint('تم تجديد الجلسة، إعادة المحاولة...');
            } catch (refreshError) {
              debugPrint('فشل في تجديد الجلسة: $refreshError');
              break; // إذا فشل التجديد، توقف
            }
          } else if (e.toString().contains('JWT') && retryCount < maxRetries) {
            debugPrint('⚠️ مشكلة في JWT، انتظار وإعادة محاولة...');
            await Future.delayed(Duration(milliseconds: 1000 * retryCount));
          } else {
            // خطأ آخر، توقف
            debugPrint('خطأ غير قابل للإصلاح: ${e.toString()}');
            break;
          }
        }
      }

      // إذا فشلت جميع المحاولات
      if (!insertSuccess) {
        throw lastError ??
            Exception('فشل في إنشاء الحساب بعد $maxRetries محاولات');
      }

      // حفظ نسخة محلية للكاش
      await _cacheAccountData(userId, accountData);

      return accountData;
    } catch (e) {
      debugPrint('خطأ في إنشاء الحساب: $e');
      rethrow;
    }
  }

  /// التحقق من ربط الجهاز بالحساب
  static Future<bool> isDeviceLinked(String userId, String deviceId) async {
    try {
      final response = await Supabase.instance.client
          .from('user_devices')
          .select('id')
          .eq('user_id', userId)
          .eq('device_id', deviceId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      debugPrint('خطأ في التحقق من ربط الجهاز: $e');
      return false; // في حالة الخطأ، نسمح بالمتابعة
    }
  }

  /// التحقق من وجود حساب آخر في نفس الجهاز
  static Future<String?> getExistingAccountForDevice(String deviceId) async {
    try {
      debugPrint('🔍 [DEVICE_CHECK] فحص وجود حساب آخر للجهاز: $deviceId');

      // فحص مع تجاهل RLS للحصول على جميع السجلات
      final response = await Supabase.instance.client.rpc(
        'check_device_exists',
        params: {'device_id_param': deviceId},
      );

      if (response != null && response is List && response.isNotEmpty) {
        final existingUserId = response[0]['user_id'] as String;
        debugPrint('⚠️ [DEVICE_CHECK] وجد حساب موجود للجهاز: $existingUserId');
        return existingUserId;
      }

      // فحص احتياطي بالطريقة العادية
      final fallbackResponse = await Supabase.instance.client
          .from('user_devices')
          .select('user_id, created_at')
          .eq('device_id', deviceId)
          .maybeSingle();

      if (fallbackResponse != null) {
        final existingUserId = fallbackResponse['user_id'] as String;
        debugPrint(
          '⚠️ [DEVICE_CHECK] وجد حساب موجود (فحص احتياطي): $existingUserId',
        );

        // فحص إذا كان الحساب الموجود ما زال نشط
        final isAccountActive = await _isAccountStillActive(existingUserId);
        if (!isAccountActive) {
          debugPrint(
            '🔄 [DEVICE_CHECK] الحساب الموجود غير نشط، سيتم حذف ربط الجهاز',
          );
          await _unlinkDevice(deviceId);
          return null; // السماح بإنشاء حساب جديد
        }

        return existingUserId;
      }

      debugPrint('✅ [DEVICE_CHECK] لا يوجد حساب آخر للجهاز');
      return null;
    } catch (e) {
      debugPrint('❌ [DEVICE_CHECK] خطأ في فحص الجهاز: $e');

      // في حالة الخطأ، نمنع إنشاء الحساب للأمان
      throw Exception('خطأ في فحص الجهاز. يرجى المحاولة لاحقاً.');
    }
  }

  /// فحص إذا كان الحساب ما زال نشط
  static Future<bool> _isAccountStillActive(String userId) async {
    try {
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_CHECK] خطأ في فحص نشاط الحساب: $e');
      return true; // في حالة الخطأ، نفترض أن الحساب نشط
    }
  }

  /// إلغاء ربط الجهاز
  static Future<void> _unlinkDevice(String deviceId) async {
    try {
      await Supabase.instance.client
          .from('user_devices')
          .delete()
          .eq('device_id', deviceId);

      debugPrint('✅ [UNLINK] تم إلغاء ربط الجهاز: $deviceId');
    } catch (e) {
      debugPrint('❌ [UNLINK] خطأ في إلغاء ربط الجهاز: $e');
    }
  }

  /// ربط الجهاز بالحساب (استخدام MultiDeviceService للمنطق الموحد)
  static Future<void> linkDevice(
    String userId,
    String deviceId, {
    String? deviceName,
  }) async {
    try {
      debugPrint(
        '🔍 [LINK_DEVICE] محاولة ربط الجهاز: $deviceId بالمستخدم: $userId',
      );

      // استخدام MultiDeviceService المحدث
      final success = await MultiDeviceService.linkDeviceV2(
        userId,
        deviceId,
        deviceName: deviceName,
      );

      if (!success) {
        throw Exception('فشل في ربط الجهاز بالحساب');
      }

      debugPrint('✅ [LINK_DEVICE] تم ربط الجهاز بنجاح: $deviceId');
    } catch (e) {
      debugPrint('❌ [LINK_DEVICE] خطأ في ربط الجهاز: $e');
      // لا نرمي الخطأ لتجنب توقف التطبيق
    }
  }

  // جلب بيانات الحساب من Supabase
  static Future<Map<String, dynamic>?> getAccountData(
    String userId, {
    bool useCache = true,
  }) async {
    try {
      // محاولة استخدام الكاش أولاً
      if (useCache) {
        final cachedData = await _getCachedAccountData(userId);
        if (cachedData != null) {
          debugPrint('تم جلب بيانات الحساب من الكاش');
          return cachedData;
        }
      }

      // فحص وتجديد الجلسة قبل الوصول لقاعدة البيانات
      await _ensureValidSession();

      // جلب البيانات من Supabase
      try {
        final response = await Supabase.instance.client
            .from('user_accounts')
            .select('*')
            .eq('user_id', userId)
            .maybeSingle();

        if (response != null) {
          debugPrint('تم جلب بيانات الحساب من Supabase');

          // حفظ في الكاش
          await _cacheAccountData(userId, response);

          return response;
        } else {
          debugPrint(
            '🚨 لا توجد بيانات حساب في Supabase للمستخدم: $userId - قد يكون الحساب محذوف',
          );

          // تشغيل فحص سريع للحساب المحذوف
          _triggerDeletedAccountCheck();

          return null;
        }
      } catch (e) {
        debugPrint('خطأ في جلب البيانات من Supabase: $e');

        // فحص إذا كان الخطأ يشير لحساب محذوف
        if (_isAccountDeletedError(e)) {
          debugPrint('🚨 خطأ يشير لحساب محذوف - تشغيل فحص فوري');
          _triggerDeletedAccountCheck();
        }

        if (e.toString().contains('relation "user_accounts" does not exist')) {
          debugPrint('جدول user_accounts غير موجود في Supabase');
        }
        rethrow;
      }
    } catch (e) {
      debugPrint('خطأ في جلب بيانات الحساب: $e');

      // في حالة الخطأ، محاولة استخدام الكاش
      final cachedData = await _getCachedAccountData(userId);
      if (cachedData != null) {
        debugPrint('تم استخدام بيانات الكاش بسبب خطأ في الشبكة');
        return cachedData;
      }

      return null;
    }
  }

  // تحديث بيانات الحساب في Supabase
  static Future<bool> updateAccountData(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      final updateData = {
        ...updates,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await Supabase.instance.client
          .from('user_accounts')
          .update(updateData)
          .eq('user_id', userId);

      debugPrint('تم تحديث بيانات الحساب في Supabase');

      // تحديث الكاش
      final currentData = await _getCachedAccountData(userId) ?? {};
      await _cacheAccountData(userId, {...currentData, ...updateData});

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات الحساب: $e');
      return false;
    }
  }

  // تفعيل الحساب
  static Future<bool> activateAccount(
    String userId,
    String packageName,
    int daysToAdd,
  ) async {
    try {
      final now = DateTime.now();
      final expiryDate = now.add(Duration(days: daysToAdd));

      // ✅ تحويل أسماء الباقات العربية إلى الإنجليزية المقبولة في قاعدة البيانات
      String subscriptionType;
      switch (packageName.toLowerCase()) {
        case 'باقة شهرية':
        case 'شهرية':
        case 'monthly':
          subscriptionType = 'monthly';
          break;
        case 'باقة ربع سنوية':
        case 'ربع سنوية':
        case 'quarterly':
          subscriptionType = 'quarterly';
          break;
        case 'باقة نصف سنوية':
        case 'نصف سنوية':
        case 'semi_annual':
          subscriptionType = 'semi_annual';
          break;
        case 'باقة سنوية':
        case 'سنوية':
        case 'yearly':
        case 'annual':
          subscriptionType = 'annual'; // ✅ تصحيح من yearly إلى annual
          break;
        default:
          subscriptionType = 'monthly'; // افتراضي
          debugPrint(
            '⚠️ [ACTIVATE] نوع باقة غير معروف: $packageName، استخدام monthly كافتراضي',
          );
      }

      final updates = {
        'account_status': 'active',
        'subscription_type': subscriptionType,
        'subscription_start': now.toIso8601String(),
        'subscription_end': expiryDate.toIso8601String(),
        'trial_days_remaining': 0,
        'updated_at': now.toIso8601String(),
      };

      return await updateAccountData(userId, updates);
    } catch (e) {
      debugPrint('خطأ في تفعيل الحساب: $e');
      return false;
    }
  }

  // فحص انتهاء الفترة التجريبية (مهجور - استخدم النظام الجديد)
  @Deprecated('استخدم getAccountDataV2 وفحص account_status بدلاً من ذلك')
  static Future<bool> isTrialExpired(String userId) async {
    try {
      // استخدام النظام الجديد
      final accountData = await getAccountDataV2(userId);
      if (accountData == null) return false;

      final accountStatus = accountData['account_status'] as String?;
      return accountStatus == 'expired' || accountStatus == 'banned';
    } catch (e) {
      debugPrint('خطأ في فحص انتهاء الفترة التجريبية: $e');
      return false;
    }
  }

  // حساب الأيام المتبقية (محدث للنظام الجديد)
  static Future<int> getDaysLeft(String userId) async {
    try {
      debugPrint('[ACCOUNT_SERVICE] جلب الأيام المتبقية للمستخدم: $userId');

      // استخدام النظام الجديد
      final accountData = await getAccountDataV2(userId);
      if (accountData == null) {
        debugPrint('[ACCOUNT_SERVICE] لم يتم العثور على بيانات الحساب');
        return 0;
      }

      final accountStatus = accountData['account_status'] as String?;

      // إذا كان الحساب في فترة تجريبية
      if (accountStatus == 'trial') {
        final daysLeft = accountData['trial_days_remaining'] as int? ?? 0;
        debugPrint('[ACCOUNT_SERVICE] الأيام المتبقية من التجربة: $daysLeft');
        return daysLeft.clamp(0, 999);
      }

      // إذا كان الحساب مفعل، حساب الأيام المتبقية من الاشتراك
      if (accountStatus == 'active' &&
          accountData['subscription_end'] != null) {
        final subscriptionEnd = DateTime.parse(accountData['subscription_end']);
        final now = DateTime.now();
        final difference = subscriptionEnd.difference(now);
        final daysLeft = difference.inDays;
        debugPrint('[ACCOUNT_SERVICE] الأيام المتبقية من الاشتراك: $daysLeft');
        return daysLeft.clamp(0, 999);
      }

      debugPrint('[ACCOUNT_SERVICE] الحساب غير نشط أو منتهي الصلاحية');
      return 0;
    } catch (e) {
      debugPrint('[ACCOUNT_SERVICE] خطأ في جلب الأيام المتبقية: $e');
      // في حالة الخطأ، محاولة استخدام النظام القديم كبديل
      return await _calculateDaysLeftLocally(userId);
    }
  }

  // حساب الأيام المتبقية محلياً كبديل (محدث للنظام الجديد)
  static Future<int> _calculateDaysLeftLocally(String userId) async {
    try {
      final accountData = await getAccountDataV2(userId);
      if (accountData == null) return 0;

      final accountStatus = accountData['account_status'] as String?;

      // إذا كان الحساب في فترة تجريبية
      if (accountStatus == 'trial') {
        return accountData['trial_days_remaining'] as int? ?? 0;
      }

      // إذا كان الحساب مفعل، حساب الأيام المتبقية من الاشتراك
      if (accountStatus == 'active' &&
          accountData['subscription_end'] != null) {
        final subscriptionEnd = DateTime.parse(accountData['subscription_end']);
        final now = DateTime.now();
        final difference = subscriptionEnd.difference(now);
        return difference.inDays.clamp(0, 999);
      }

      return 0;
    } catch (e) {
      debugPrint('[ACCOUNT_SERVICE] خطأ في الحساب المحلي للأيام المتبقية: $e');
      return 0;
    }
  }

  // حفظ بيانات الحساب في الكاش المحلي (مبسط)
  static Future<void> _cacheAccountData(
    String userId,
    Map<String, dynamic> data,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ البيانات الأساسية فقط
      await prefs.setBool('cache_is_trial_$userId', data['is_trial'] ?? true);
      await prefs.setInt(
        'cache_expiry_millis_$userId',
        data['expiry_millis'] ?? 0,
      );
      await prefs.setInt(
        'cache_creation_millis_$userId',
        data['creation_millis'] ?? 0,
      );
      await prefs.setString(
        'cache_display_name_$userId',
        data['display_name'] ?? '',
      );
      await prefs.setInt(
        'cache_time_$userId',
        DateTime.now().millisecondsSinceEpoch,
      );

      debugPrint('تم حفظ بيانات الحساب في الكاش');
    } catch (e) {
      debugPrint('خطأ في حفظ الكاش: $e');
    }
  }

  // جلب بيانات الحساب من الكاش المحلي (مبسط)
  static Future<Map<String, dynamic>?> _getCachedAccountData(
    String userId,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheTime = prefs.getInt('cache_time_$userId') ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // فحص انتهاء صلاحية الكاش
      if (now - cacheTime > _cacheExpiry.inMilliseconds) {
        debugPrint('انتهت صلاحية الكاش');
        return null;
      }

      // جلب البيانات من الكاش
      final cachedData = {
        'is_trial': prefs.getBool('cache_is_trial_$userId') ?? true,
        'expiry_millis': prefs.getInt('cache_expiry_millis_$userId') ?? 0,
        'creation_millis': prefs.getInt('cache_creation_millis_$userId') ?? 0,
        'display_name': prefs.getString('cache_display_name_$userId') ?? '',
      };

      debugPrint('تم العثور على بيانات في الكاش');
      return cachedData;
    } catch (e) {
      debugPrint('خطأ في جلب الكاش: $e');
      return null;
    }
  }

  // مسح الكاش
  static Future<void> clearCache(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${_cachePrefix}account_$userId';
      final cacheTimeKey = '${_cachePrefix}time_$userId';

      await prefs.remove(cacheKey);
      await prefs.remove(cacheTimeKey);

      debugPrint('تم مسح كاش الحساب');
    } catch (e) {
      debugPrint('خطأ في مسح الكاش: $e');
    }
  }

  // مزامنة البيانات المحلية القديمة مع Supabase
  static Future<void> migrateLocalDataToSupabase(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // جلب البيانات المحلية القديمة وتحويلها للنظام الجديد
      final isTrial = prefs.getBool('is_trial') ?? true;
      final displayName = prefs.getString('user_display_name') ?? '';

      // تحويل البيانات للنظام الجديد
      final localData = {
        'user_id': userId,
        'display_name': displayName,
        'account_status': isTrial ? 'trial' : 'active',
        'subscription_type': isTrial ? 'trial' : 'monthly',
        'trial_days_remaining': isTrial ? 15 : 0,
        'max_devices': 3,
        'is_locked': false,
        'created_by': userId,
      };

      // التحقق من وجود بيانات في Supabase
      final existingData = await getAccountData(userId, useCache: false);

      if (existingData == null) {
        // إنشاء حساب جديد بالبيانات المحلية
        await createAccountV2(
          userId,
          localData['display_name']?.toString() ?? 'مستخدم',
          localData['email']?.toString() ?? '',
        );
        debugPrint('تم ترحيل البيانات المحلية إلى Supabase');
      } else {
        debugPrint('البيانات موجودة مسبقاً في Supabase');
      }
    } catch (e) {
      debugPrint('خطأ في ترحيل البيانات: $e');
    }
  }

  /// التحقق من وجود الحساب في Supabase (للاستعلام الدوري)
  static Future<bool> checkAccountExists(String userId) async {
    try {
      debugPrint('🔍 [ACCOUNT_CHECK] فحص وجود الحساب: $userId');

      // التحقق من صحة الجلسة أولاً
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser == null || currentUser.id != userId) {
        debugPrint(
          '❌ [ACCOUNT_CHECK] الجلسة غير صالحة أو معرف المستخدم غير متطابق',
        );
        return false;
      }

      // فحص وجود الحساب في جدول user_accounts
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('user_id')
          .eq('user_id', userId)
          .maybeSingle();

      final exists = response != null;
      debugPrint(
        '🔍 [ACCOUNT_CHECK] نتيجة الفحص: ${exists ? "موجود" : "غير موجود"}',
      );

      return exists;
    } catch (e) {
      debugPrint('❌ [ACCOUNT_CHECK] خطأ في فحص وجود الحساب: $e');

      // في حالة خطأ الشبكة، نعتبر الحساب موجود لتجنب قطع الجلسة بسبب مشاكل الاتصال
      if (e.toString().contains('network') ||
          e.toString().contains('connection') ||
          e.toString().contains('timeout')) {
        debugPrint('⚠️ [ACCOUNT_CHECK] خطأ شبكة - الاحتفاظ بالجلسة');
        return true;
      }

      // للأخطاء الأخرى (مثل عدم وجود الحساب)، نعتبر الحساب غير موجود
      return false;
    }
  }

  /// فحص وتجديد الجلسة قبل الوصول لقاعدة البيانات
  static Future<void> _ensureValidSession() async {
    try {
      debugPrint('🔍 [SESSION] فحص صلاحية الجلسة...');

      final session = Supabase.instance.client.auth.currentSession;
      if (session == null) {
        debugPrint('❌ [SESSION] لا توجد جلسة نشطة');
        throw Exception('لا توجد جلسة نشطة');
      }

      // فحص انتهاء صلاحية التوكن
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        session.expiresAt! * 1000,
      );
      final now = DateTime.now();
      final timeUntilExpiry = expiresAt.difference(now);

      debugPrint('🔍 [SESSION] انتهاء الجلسة: $expiresAt');
      debugPrint('🔍 [SESSION] الوقت الحالي: $now');
      debugPrint(
        '🔍 [SESSION] الوقت المتبقي: ${timeUntilExpiry.inMinutes} دقيقة',
      );

      // إذا كانت الجلسة ستنتهي خلال 5 دقائق، جددها
      if (timeUntilExpiry.inMinutes < 5) {
        debugPrint('⚠️ [SESSION] الجلسة قريبة من الانتهاء، جاري التجديد...');

        await Supabase.instance.client.auth.refreshSession();

        final newSession = Supabase.instance.client.auth.currentSession;
        if (newSession != null) {
          final newExpiresAt = DateTime.fromMillisecondsSinceEpoch(
            newSession.expiresAt! * 1000,
          );
          debugPrint('✅ [SESSION] تم تجديد الجلسة بنجاح');
          debugPrint('✅ [SESSION] انتهاء الجلسة الجديدة: $newExpiresAt');
        } else {
          throw Exception('فشل في تجديد الجلسة');
        }
      } else {
        debugPrint('✅ [SESSION] الجلسة صالحة ولا تحتاج تجديد');
      }
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في فحص/تجديد الجلسة: $e');
      rethrow;
    }
  }

  /// إنهاء الجلسة وتنظيف البيانات المحلية
  static Future<void> signOutAndCleanup() async {
    try {
      debugPrint('🚪 [ACCOUNT_CHECK] بدء إنهاء الجلسة وتنظيف البيانات...');

      // إنهاء جلسة Supabase
      await Supabase.instance.client.auth.signOut();

      // تنظيف الكاش المحلي
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith(_cachePrefix));
      for (final key in keys) {
        await prefs.remove(key);
      }

      debugPrint('✅ [ACCOUNT_CHECK] تم إنهاء الجلسة وتنظيف البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ [ACCOUNT_CHECK] خطأ في إنهاء الجلسة: $e');
    }
  }

  /// تشغيل فحص الحساب المحذوف
  static void _triggerDeletedAccountCheck() {
    // تشغيل فحص فوري في الخلفية
    Future.delayed(Duration.zero, () async {
      await DeletedAccountDetector.quickAccountCheck();
    });
  }

  /// فحص إذا كان الخطأ يشير لحساب محذوف
  static bool _isAccountDeletedError(dynamic error) {
    final errorStr = error.toString().toLowerCase();

    return errorStr.contains('404') ||
        errorStr.contains('not found') ||
        errorStr.contains('pgrst116') ||
        errorStr.contains('no rows found') ||
        errorStr.contains('forbidden') ||
        errorStr.contains('403') ||
        errorStr.contains('unauthorized') ||
        errorStr.contains('401');
  }
}
