class ServerModel {
  final String name;
  final String ip;
  final String user;
  final String pass;
  bool connected;

  ServerModel({
    required this.name,
    required this.ip,
    required this.user,
    required this.pass,
    this.connected = false,
  });

  factory ServerModel.fromJson(Map<String, dynamic> json) => ServerModel(
    name: json['name'],
    ip: json['ip'],
    user: json['user'],
    pass: json['pass'],
    connected: json['connected'] ?? false,
  );

  Map<String, dynamic> toJson() => {
    'name': name,
    'ip': ip,
    'user': user,
    'pass': pass,
    'connected': connected,
  };
}
