import 'package:flutter/material.dart';
import 'activation_by_code_screen.dart';
import 'package:url_launcher/url_launcher.dart';
import 'main_home_screen.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ActivationOptionsScreen extends StatefulWidget {
  // رقم الدعم الفني (يمكنك تعديله لاحقًا)
  // رقم الدعم الفني يتم استدعاؤه من الشاشة الرئيسية
  final void Function(String type, int amount) onSelect;
  const ActivationOptionsScreen({required this.onSelect, Key? key})
    : super(key: key);

  @override
  State<ActivationOptionsScreen> createState() =>
      _ActivationOptionsScreenState();
}

class _ActivationOptionsScreenState extends State<ActivationOptionsScreen> {
  int? selectedPlanIdx = null;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final features = [
      'تفعيل الاشتراكات من داخل التطبيق',
      'حساب الأرباح بشكل دوري',
      'رسائل تنبيه للمشترك على الواتساب',
      'تخزين سحابي ذكي',
      'دعم فني متواصل 24/7',
    ];
    final plans = [
      {
        'type': 'month',
        'label': 'شهري',
        'price': '5000',
        'desc': 'فاتورة شهرية',
        'highlight': false, // لا تمييز للبطاقة الأولى
        'save': null,
      },
      {
        'type': '3months',
        'label': '3 أشهر',
        'price': '14000',
        'desc': 'فاتورة كل 3 أشهر',
        'highlight': false,
        'save': 'وفر 1000 د.ع',
      },
      {
        'type': '6months',
        'label': '6 أشهر',
        'price': '27000',
        'desc': 'فاتورة كل 6 أشهر',
        'highlight': false,
        'save': 'وفر 3000 د.ع',
      },
      {
        'type': 'year',
        'label': 'سنوي',
        'price': '50000',
        'desc': 'فاتورة سنوية',
        'highlight': false,
        'save': 'وفر 10000 د.ع',
      },
    ];
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية مثل شاشة تسجيل الدخول
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 0), // مساحة للـ AppBar
                  // شعار دائري عصري مع أيقونة التفعيل
                  Container(
                    margin: const EdgeInsets.only(bottom: 32),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: colorScheme.primary.withValues(alpha: 0.18),
                          blurRadius: 24,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: CircleAvatar(
                      radius: 64,
                      backgroundColor: Colors.white.withValues(
                        alpha: isDark ? 0.08 : 0.18,
                      ),
                      child: Icon(
                        Icons.verified_rounded,
                        color: colorScheme.primary,
                        size: 64,
                      ),
                    ),
                  ),

                  // عنوان رئيسي
                  Text(
                    'اختر باقة التفعيل',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onPrimary,
                      letterSpacing: 1,
                      shadows: [
                        Shadow(
                          color: colorScheme.shadow.withValues(alpha: 0.13),
                          blurRadius: 4,
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),

                  // وصف
                  Text(
                    'استمتع بجميع الميزات المتقدمة',
                    style: TextStyle(
                      fontSize: 18,
                      color: colorScheme.onPrimary.withValues(alpha: 0.92),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 40),

                  // بطاقة شفافة عصرية للمحتوى
                  Card(
                    elevation: 0,
                    color: colorScheme.surface.withValues(
                      alpha: isDark ? 0.7 : 0.93,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 32,
                        horizontal: 20,
                      ),
                      child: Column(
                        children: [
                          // قائمة الميزات
                          _buildFeaturesSection(features, colorScheme),
                          const SizedBox(height: 32),

                          // شبكة الباقات
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  childAspectRatio: 0.95,
                                  crossAxisSpacing: 12,
                                  mainAxisSpacing: 14,
                                ),
                            itemCount: plans.length,
                            itemBuilder: (context, idx) {
                              final plan = plans[idx];
                              final isSelected = selectedPlanIdx == idx;
                              // جميع البطاقات بنفس اللون الآن
                              return AnimatedContainer(
                                duration: const Duration(milliseconds: 200),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? colorScheme.primary.withValues(
                                          alpha: 0.15,
                                        )
                                      : colorScheme.surface.withValues(
                                          alpha: 0.8,
                                        ),
                                  borderRadius: BorderRadius.circular(18),
                                  border: Border.all(
                                    color: isSelected
                                        ? colorScheme.primary
                                        : colorScheme.outline.withValues(
                                            alpha: 0.18,
                                          ),
                                    width: isSelected ? 3 : 1,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: colorScheme.primary.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 12,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(18),
                                  onTap: () {
                                    setState(() {
                                      selectedPlanIdx = idx;
                                    });
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 18,
                                      horizontal: 8,
                                    ),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          plan['label'] as String,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          '${plan['price']} د.ع',
                                          style: const TextStyle(
                                            fontSize: 22,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.black,
                                          ),
                                        ),
                                        if (plan['save'] != null)
                                          Padding(
                                            padding: const EdgeInsets.only(
                                              top: 4.0,
                                            ),
                                            child: Text(
                                              plan['save'] as String,
                                              style: const TextStyle(
                                                fontSize: 13,
                                                color: Colors.green,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                          ),
                                        const SizedBox(height: 6),
                                        Text(
                                          plan['desc'] as String,
                                          style: TextStyle(
                                            fontSize: 13,
                                            color: colorScheme.onSurfaceVariant,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 22),

                          // زر الاشتراك
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                textStyle: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                elevation: 3,
                              ),
                              onPressed: () async {
                                if (selectedPlanIdx == null) {
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'يرجى اختيار الباقة أولاً',
                                        ),
                                      ),
                                    );
                                  }
                                  return;
                                }
                                final plan = plans[selectedPlanIdx!];
                                final label = plan['label'] ?? '';
                                final price = plan['price'] ?? '';
                                // حفظ اسم الباقة المفعلة في SharedPreferences
                                final prefs =
                                    await SharedPreferences.getInstance();
                                await prefs.setString(
                                  'activePackage',
                                  label.toString(),
                                );
                                final msg =
                                    'مرحباً، تطبيق iTower\nاريد الاشتراك في التطبيق بباقة $label والسعر $price د.ع\nلطفاً';
                                final whatsappUrl = Uri.parse(
                                  'https://wa.me/${MainHomeScreen.supportPhone}?text=${Uri.encodeComponent(msg)}',
                                );
                                try {
                                  final launched = await launchUrl(
                                    whatsappUrl,
                                    mode: LaunchMode.externalApplication,
                                  );
                                  if (!launched && mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'تعذر فتح واتساب. تأكد من وجود التطبيق وصحة الرقم.',
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                } catch (e) {
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'تعذر إرسال رسالة واتساب: $e',
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                }
                              },
                              child: const Text('اشتراك'),
                            ),
                          ),
                          const SizedBox(height: 16),

                          // زر كود التفعيل
                          SizedBox(
                            width: double.infinity,
                            child: OutlinedButton.icon(
                              icon: const Icon(Icons.vpn_key_rounded),
                              label: const Text('لديك كود تفعيل؟'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: colorScheme.primary,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                side: BorderSide(
                                  color: colorScheme.primary,
                                  width: 2,
                                ),
                                textStyle: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              onPressed: () async {
                                // ✅ انتظار نتيجة التفعيل
                                final result = await Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (_) =>
                                        const ActivationByCodeScreen(),
                                  ),
                                );

                                // إذا تم التفعيل بنجاح، العودة للشاشة الرئيسية
                                if (result == true && mounted) {
                                  Navigator.of(
                                    context,
                                  ).pop(true); // إرجاع إشارة النجاح
                                }
                              },
                            ),
                          ),
                          const SizedBox(height: 20),

                          // نص إرشادي
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.green.withValues(alpha: 0.3),
                              ),
                            ),
                            child: const Text(
                              'بعد اختيار نوع التفعيل والدفع، اضغط على زر "لديك كود تفعيل؟" ثم ضع الكود واضغط على زر تفعيل الحساب',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم الميزات
  Widget _buildFeaturesSection(List<String> features, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.star_rounded, color: colorScheme.primary, size: 24),
              const SizedBox(width: 12),
              Text(
                'مميزات التفعيل',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...features.map(
            (feature) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.check_rounded,
                      color: Colors.green,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      feature,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
