// خدمة إدارة الموظفين والصلاحيات

import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import '../db_helper.dart';
import '../models/employee_models.dart';

class EmployeeService {
  static const String _currentEmployeeKey = 'current_employee_session';
  static const String _managerModeKey = 'is_manager_mode';

  /// إنشاء موظف جديد
  static Future<Map<String, dynamic>> createEmployee({
    required String name,
    required String email,
    required String password,
    required UserRole role,
    required Map<PermissionType, bool> permissions,
    String? phoneNumber,
  }) async {
    try {
      final db = await DBHelper.instance.database;

      // فحص إذا كان البريد الإلكتروني مستخدم
      final existingEmployee = await db.query(
        'employees',
        where: 'email = ?',
        whereArgs: [email],
      );

      if (existingEmployee.isNotEmpty) {
        return {'success': false, 'message': 'البريد الإلكتروني مستخدم بالفعل'};
      }

      // تشفير كلمة المرور
      final passwordHash = _hashPassword(password);

      // إنشاء الموظف
      final employee = Employee(
        name: name,
        email: email,
        passwordHash: passwordHash,
        role: role,
        permissions: permissions,
        createdAt: DateTime.now(),
        phoneNumber: phoneNumber,
      );

      // حفظ في قاعدة البيانات
      final employeeId = await db.insert('employees', employee.toMap());

      // تسجيل النشاط
      await _logActivity(
        employeeId,
        'تم إنشاء حساب موظف جديد',
        'الاسم: $name، البريد: $email، الدور: ${role.name}',
      );

      return {
        'success': true,
        'message': 'تم إنشاء حساب الموظف بنجاح',
        'employeeId': employeeId,
      };
    } catch (e) {
      return {'success': false, 'message': 'خطأ في إنشاء حساب الموظف: $e'};
    }
  }

  /// تسجيل دخول الموظف
  static Future<Map<String, dynamic>> loginEmployee({
    required String email,
    required String password,
  }) async {
    try {
      final db = await DBHelper.instance.database;

      // البحث عن الموظف
      final employeeData = await db.query(
        'employees',
        where: 'email = ? AND is_active = 1',
        whereArgs: [email],
      );

      if (employeeData.isEmpty) {
        return {
          'success': false,
          'message': 'البريد الإلكتروني غير موجود أو الحساب غير نشط',
        };
      }

      final employee = Employee.fromMap(employeeData.first);

      // التحقق من كلمة المرور
      if (!_verifyPassword(password, employee.passwordHash)) {
        return {'success': false, 'message': 'كلمة المرور غير صحيحة'};
      }

      // إنشاء جلسة
      final session = EmployeeSession(
        employeeId: employee.id!,
        employeeName: employee.name,
        employeeEmail: employee.email,
        role: employee.role,
        permissions: employee.permissions,
        loginTime: DateTime.now(),
      );

      // حفظ الجلسة
      await _saveCurrentSession(session);

      // تسجيل النشاط
      await _logActivity(employee.id!, 'تسجيل دخول', 'تم تسجيل الدخول بنجاح');

      return {
        'success': true,
        'message': 'تم تسجيل الدخول بنجاح',
        'session': session,
      };
    } catch (e) {
      return {'success': false, 'message': 'خطأ في تسجيل الدخول: $e'};
    }
  }

  /// الحصول على الجلسة الحالية
  static Future<EmployeeSession?> getCurrentSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionData = prefs.getString(_currentEmployeeKey);

      if (sessionData == null) return null;

      final sessionMap = jsonDecode(sessionData) as Map<String, dynamic>;

      // تحويل الصلاحيات
      final permissionsMap = sessionMap['permissions'] as Map<String, dynamic>;
      final permissions = <PermissionType, bool>{};

      for (final permission in PermissionType.values) {
        permissions[permission] = permissionsMap[permission.name] ?? false;
      }

      return EmployeeSession(
        employeeId: sessionMap['employeeId'] as int,
        employeeName: sessionMap['employeeName'] as String,
        employeeEmail: sessionMap['employeeEmail'] as String,
        role: UserRole.values.firstWhere((r) => r.name == sessionMap['role']),
        permissions: permissions,
        loginTime: DateTime.parse(sessionMap['loginTime'] as String),
        deviceInfo: sessionMap['deviceInfo'] as String?,
      );
    } catch (e) {
      return null;
    }
  }

  /// فحص إذا كان المستخدم في وضع المدير
  static Future<bool> isManagerMode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_managerModeKey) ?? true; // افتراضياً وضع المدير
  }

  /// تبديل إلى وضع المدير
  static Future<void> switchToManagerMode() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_managerModeKey, true);
    await prefs.remove(_currentEmployeeKey);
  }

  /// تسجيل خروج الموظف
  static Future<void> logoutEmployee() async {
    final session = await getCurrentSession();
    if (session != null) {
      await _logActivity(session.employeeId, 'تسجيل خروج', 'تم تسجيل الخروج');
    }

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentEmployeeKey);
    await prefs.setBool(_managerModeKey, true);
  }

  /// فحص صلاحية معينة للجلسة الحالية
  static Future<bool> hasPermission(PermissionType permission) async {
    // إذا كان في وضع المدير، له كل الصلاحيات
    if (await isManagerMode()) return true;

    final session = await getCurrentSession();
    return session?.hasPermission(permission) ?? false;
  }

  /// الحصول على قائمة الموظفين
  static Future<List<Employee>> getAllEmployees() async {
    final db = await DBHelper.instance.database;
    final employeesData = await db.query(
      'employees',
      orderBy: 'created_at DESC',
    );

    // طباعة البيانات للتحقق
    debugPrint(
      '🔍 [EMPLOYEE_SERVICE] عدد الموظفين في قاعدة البيانات: ${employeesData.length}',
    );
    for (final emp in employeesData) {
      debugPrint(
        '👤 موظف: ${emp['name']} - ${emp['email']} - نشط: ${emp['is_active']}',
      );
    }

    return employeesData.map((data) => Employee.fromMap(data)).toList();
  }

  /// تحديث صلاحيات موظف
  static Future<Map<String, dynamic>> updateEmployeePermissions({
    required int employeeId,
    required Map<PermissionType, bool> newPermissions,
  }) async {
    try {
      final db = await DBHelper.instance.database;

      await db.update(
        'employees',
        {
          'permissions': jsonEncode(
            newPermissions.map((k, v) => MapEntry(k.name, v)),
          ),
        },
        where: 'id = ?',
        whereArgs: [employeeId],
      );

      await _logActivity(
        employeeId,
        'تحديث الصلاحيات',
        'تم تحديث صلاحيات الموظف',
      );

      return {'success': true, 'message': 'تم تحديث الصلاحيات بنجاح'};
    } catch (e) {
      return {'success': false, 'message': 'خطأ في تحديث الصلاحيات: $e'};
    }
  }

  /// حذف موظف (إلغاء تفعيل)
  static Future<Map<String, dynamic>> deactivateEmployee(int employeeId) async {
    try {
      final db = await DBHelper.instance.database;

      await db.update(
        'employees',
        {'is_active': 0},
        where: 'id = ?',
        whereArgs: [employeeId],
      );

      await _logActivity(
        employeeId,
        'إلغاء تفعيل الحساب',
        'تم إلغاء تفعيل حساب الموظف',
      );

      return {'success': true, 'message': 'تم إلغاء تفعيل الموظف بنجاح'};
    } catch (e) {
      return {'success': false, 'message': 'خطأ في إلغاء تفعيل الموظف: $e'};
    }
  }

  /// تحديث بيانات موظف
  static Future<void> updateEmployee(Employee employee) async {
    final db = await DBHelper.instance.database;

    await db.update(
      'employees',
      employee.toMap(),
      where: 'id = ?',
      whereArgs: [employee.id],
    );
  }

  /// حذف موظف نهائياً
  static Future<void> deleteEmployee(int employeeId) async {
    final db = await DBHelper.instance.database;

    // تسجيل النشاط قبل الحذف
    await _logActivity(
      employeeId,
      'تم حذف الحساب',
      'تم حذف حساب الموظف نهائياً بواسطة المدير',
    );

    await db.delete('employees', where: 'id = ?', whereArgs: [employeeId]);
  }

  /// تسجيل نشاط الموظف (دالة عامة)
  static Future<void> logActivity({
    required int employeeId,
    required String action,
    required String details,
  }) async {
    await _logActivity(employeeId, action, details);
  }

  /// إنشاء موظف جديد من كائن Employee
  static Future<Employee> createEmployeeFromObject(
    Employee employee,
    String password,
  ) async {
    final permissionsMap = <PermissionType, bool>{};
    for (final entry in employee.permissions.entries) {
      if (entry.value) {
        permissionsMap[entry.key] = true;
      }
    }

    final result = await createEmployee(
      name: employee.name,
      email: employee.email,
      password: password,
      role: employee.role,
      permissions: permissionsMap,
      phoneNumber: employee.phoneNumber,
    );

    if (result['success'] == true) {
      return employee.copyWith(id: result['employeeId']);
    } else {
      throw Exception(result['message']);
    }
  }

  // دوال مساعدة خاصة
  static String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  static bool _verifyPassword(String password, String hash) {
    return _hashPassword(password) == hash;
  }

  static Future<void> _saveCurrentSession(EmployeeSession session) async {
    final prefs = await SharedPreferences.getInstance();

    final sessionData = {
      'employeeId': session.employeeId,
      'employeeName': session.employeeName,
      'employeeEmail': session.employeeEmail,
      'role': session.role.name,
      'permissions': session.permissions.map((k, v) => MapEntry(k.name, v)),
      'loginTime': session.loginTime.toIso8601String(),
      'deviceInfo': session.deviceInfo,
    };

    await prefs.setString(_currentEmployeeKey, jsonEncode(sessionData));
    await prefs.setBool(_managerModeKey, false);
  }

  static Future<void> _logActivity(
    int employeeId,
    String action,
    String? details,
  ) async {
    try {
      final db = await DBHelper.instance.database;

      final log = EmployeeActivityLog(
        employeeId: employeeId,
        action: action,
        details: details,
        timestamp: DateTime.now(),
      );

      await db.insert('employee_activity_logs', log.toMap());
    } catch (e) {
      // تسجيل الخطأ ولكن لا نوقف العملية
      debugPrint('خطأ في تسجيل النشاط: $e');
    }
  }

  /// مصادقة الموظف باستخدام البريد الإلكتروني وكلمة المرور
  static Future<Employee?> authenticateEmployee(
    String email,
    String password,
  ) async {
    try {
      debugPrint('🔐 [EMPLOYEE_AUTH] محاولة تسجيل دخول للموظف: $email');

      final db = await DBHelper.instance.database;

      // أولاً، دعنا نرى جميع الموظفين الموجودين
      final allEmployees = await db.query('employees');
      debugPrint(
        '📋 [EMPLOYEE_AUTH] جميع الموظفين في قاعدة البيانات: ${allEmployees.length}',
      );
      for (var emp in allEmployees) {
        debugPrint(
          '👤 [EMPLOYEE_AUTH] موظف: ${emp['name']} - البريد: ${emp['email']} - نشط: ${emp['is_active']}',
        );
      }

      // البحث عن الموظف
      final result = await db.query(
        'employees',
        where: 'email = ?',
        whereArgs: [email],
      );

      debugPrint(
        '🔍 [EMPLOYEE_AUTH] نتائج البحث للبريد "$email": ${result.length} موظف',
      );

      if (result.isEmpty) {
        debugPrint(
          '❌ [EMPLOYEE_AUTH] لم يتم العثور على موظف بهذا البريد الإلكتروني',
        );
        return null;
      }

      final employee = _createEmployeeFromMap(result.first);
      debugPrint('👤 [EMPLOYEE_AUTH] تم العثور على الموظف: ${employee.name}');

      // التحقق من كلمة المرور
      final passwordHash = _hashPassword(password);
      debugPrint('🔑 [EMPLOYEE_AUTH] مقارنة كلمات المرور...');
      debugPrint(
        '🔑 [EMPLOYEE_AUTH] كلمة المرور المدخلة (مشفرة): $passwordHash',
      );
      debugPrint(
        '🔑 [EMPLOYEE_AUTH] كلمة المرور المحفوظة: ${employee.passwordHash}',
      );

      if (employee.passwordHash != passwordHash) {
        debugPrint('❌ [EMPLOYEE_AUTH] كلمة المرور غير صحيحة');
        return null;
      }

      // التحقق من أن الموظف نشط
      if (!employee.isActive) {
        debugPrint('❌ [EMPLOYEE_AUTH] حساب الموظف غير نشط');
        throw Exception('حساب الموظف غير نشط');
      }

      debugPrint(
        '✅ [EMPLOYEE_AUTH] تم تسجيل الدخول بنجاح للموظف: ${employee.name}',
      );
      return employee;
    } catch (e) {
      debugPrint('❌ [EMPLOYEE_AUTH] خطأ في مصادقة الموظف: $e');
      return null;
    }
  }

  /// إنشاء جلسة للموظف (نسخة مبسطة)
  static Future<void> createSession(int employeeId) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ معرف الموظف في SharedPreferences (نسخة مبسطة)
      await prefs.setInt(_currentEmployeeKey, employeeId);
      await prefs.setString(
        'employee_login_time',
        DateTime.now().toIso8601String(),
      );
      await prefs.setBool('is_employee_logged_in', true);

      debugPrint(
        '✅ [EMPLOYEE_SESSION] تم إنشاء جلسة مبسطة للموظف: $employeeId',
      );

      // محاولة إنشاء جلسة في قاعدة البيانات (اختيارية)
      try {
        final db = await DBHelper.instance.database;

        // إنهاء الجلسات السابقة
        await db.update(
          'employee_sessions',
          {'is_active': 0, 'ended_at': DateTime.now().toIso8601String()},
          where: 'employee_id = ? AND is_active = 1',
          whereArgs: [employeeId],
        );

        // إنشاء جلسة جديدة
        final sessionData = {
          'employee_id': employeeId,
          'session_token':
              'emp_session_${DateTime.now().millisecondsSinceEpoch}',
          'created_at': DateTime.now().toIso8601String(),
          'is_active': 1,
        };

        final sessionId = await db.insert('employee_sessions', sessionData);
        await prefs.setInt('current_employee_session_id', sessionId);
        debugPrint(
          '✅ [EMPLOYEE_SESSION] تم إنشاء جلسة قاعدة البيانات: $sessionId',
        );
      } catch (dbError) {
        debugPrint(
          '⚠️ [EMPLOYEE_SESSION] فشل في إنشاء جلسة قاعدة البيانات: $dbError',
        );
        // لا نرمي خطأ هنا لأن الجلسة المبسطة تكفي
      }
    } catch (e) {
      debugPrint('❌ [EMPLOYEE_SESSION] خطأ في إنشاء الجلسة: $e');
      throw Exception('فشل في إنشاء الجلسة');
    }
  }

  /// الحصول على الموظف الحالي من الجلسة
  static Future<Employee?> getCurrentEmployee() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final employeeId = prefs.getInt(_currentEmployeeKey);
      final isLoggedIn = prefs.getBool('is_employee_logged_in') ?? false;

      debugPrint(
        '🔍 [EMPLOYEE_SESSION] فحص الجلسة - ID: $employeeId, مسجل دخول: $isLoggedIn',
      );

      if (employeeId == null || !isLoggedIn) {
        debugPrint('❌ [EMPLOYEE_SESSION] لا يوجد موظف مسجل دخول');
        return null;
      }

      final db = await DBHelper.instance.database;
      final result = await db.query(
        'employees',
        where: 'id = ? AND is_active = 1',
        whereArgs: [employeeId],
      );

      if (result.isEmpty) {
        debugPrint(
          '❌ [EMPLOYEE_SESSION] لم يتم العثور على الموظف في قاعدة البيانات',
        );
        return null;
      }

      final employee = _createEmployeeFromMap(result.first);
      debugPrint('✅ [EMPLOYEE_SESSION] تم العثور على الموظف: ${employee.name}');
      return employee;
    } catch (e) {
      debugPrint('❌ [EMPLOYEE_SESSION] خطأ في الحصول على الموظف الحالي: $e');
      return null;
    }
  }

  /// إنهاء جلسة الموظف
  static Future<void> endSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final employeeId = prefs.getInt(_currentEmployeeKey);
      final sessionId = prefs.getInt('current_employee_session_id');

      debugPrint('🔚 [EMPLOYEE_SESSION] إنهاء الجلسة - ID: $employeeId');

      // تسجيل نشاط تسجيل الخروج
      if (employeeId != null) {
        try {
          await logActivity(
            employeeId: employeeId,
            action: 'تسجيل خروج',
            details: 'تم تسجيل الخروج بنجاح',
          );
        } catch (e) {
          debugPrint('⚠️ [EMPLOYEE_SESSION] فشل في تسجيل النشاط: $e');
        }
      }

      // إنهاء الجلسة في قاعدة البيانات (اختياري)
      if (employeeId != null && sessionId != null) {
        try {
          final db = await DBHelper.instance.database;
          await db.update(
            'employee_sessions',
            {'is_active': 0, 'ended_at': DateTime.now().toIso8601String()},
            where: 'id = ?',
            whereArgs: [sessionId],
          );
          debugPrint('✅ [EMPLOYEE_SESSION] تم إنهاء الجلسة في قاعدة البيانات');
        } catch (e) {
          debugPrint(
            '⚠️ [EMPLOYEE_SESSION] فشل في إنهاء جلسة قاعدة البيانات: $e',
          );
        }
      }

      // مسح بيانات الجلسة من SharedPreferences
      await prefs.remove(_currentEmployeeKey);
      await prefs.remove('current_employee_session_id');
      await prefs.remove('employee_login_time');
      await prefs.setBool('is_employee_logged_in', false);

      debugPrint('✅ [EMPLOYEE_SESSION] تم مسح جميع بيانات الجلسة');
    } catch (e) {
      debugPrint('❌ [EMPLOYEE_SESSION] خطأ في إنهاء جلسة الموظف: $e');
    }
  }

  /// تحويل Map من قاعدة البيانات إلى كائن Employee
  static Employee _createEmployeeFromMap(Map<String, dynamic> map) {
    // تحويل الصلاحيات من JSON إلى Map
    Map<PermissionType, bool> permissions = {};
    if (map['permissions'] != null) {
      final permissionsJson = jsonDecode(map['permissions'] as String);
      for (final entry in permissionsJson.entries) {
        final permissionType = PermissionType.values.firstWhere(
          (p) => p.toString().split('.').last == entry.key,
          orElse: () => PermissionType.viewSubscribers,
        );
        permissions[permissionType] = entry.value as bool;
      }
    }

    return Employee(
      id: map['id'] as int?,
      name: map['name'] as String,
      email: map['email'] as String,
      passwordHash: map['password_hash'] as String,
      role: UserRole.values.firstWhere(
        (r) => r.toString().split('.').last == map['role'],
        orElse: () => UserRole.employee,
      ),
      permissions: permissions,
      phoneNumber: map['phone_number'] as String?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
    );
  }
}
