import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/unified_account_model.dart';
import '../managers/internet_status_manager.dart';
import '../../services/account_service.dart';
import '../../db_helper.dart';

/// خدمة تدفق البيانات الموحدة
/// المسؤولة عن إدارة تدفق البيانات من المصدر إلى النظام
class UnifiedDataFlowService {
  static const String _tag = '[UNIFIED_DATA_FLOW]';

  // المتحكم في تدفق البيانات
  static final StreamController<UnifiedAccountModel?> _dataController =
      StreamController<UnifiedAccountModel?>.broadcast();

  // البيانات الحالية
  static UnifiedAccountModel? _currentData;
  static String? _currentUserId;

  // حالة النظام
  static bool _isInitialized = false;
  static bool _isLoading = false;

  // الخصائص العامة
  static Stream<UnifiedAccountModel?> get dataStream => _dataController.stream;
  static UnifiedAccountModel? get currentData => _currentData;
  static bool get isInitialized => _isInitialized;
  static bool get isLoading => _isLoading;

  /// تهيئة النظام
  static Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('$_tag تهيئة نظام تدفق البيانات الموحد...');

    try {
      // مراقبة تغييرات الإنترنت
      InternetStatusManager.statusStream.listen(_onInternetStatusChanged);

      _isInitialized = true;
      debugPrint('$_tag تم تهيئة النظام بنجاح');
    } catch (e) {
      debugPrint('$_tag خطأ في تهيئة النظام: $e');
      rethrow;
    }
  }

  /// تحديث معرف المستخدم وتحميل البيانات
  static Future<void> updateUserId(String? userId) async {
    if (!_isInitialized) await initialize();

    if (_currentUserId == userId) return;

    debugPrint('$_tag تحديث معرف المستخدم: $userId');

    _currentUserId = userId;

    if (userId != null) {
      await _loadUserData(userId);
    } else {
      _clearData();
    }
  }

  /// إعادة تحميل البيانات
  static Future<void> refreshData() async {
    if (_currentUserId == null) return;

    debugPrint('$_tag إعادة تحميل البيانات للمستخدم: $_currentUserId');
    await _loadUserData(_currentUserId!);
  }

  /// تحميل بيانات المستخدم بالاستراتيجية الموحدة
  static Future<void> _loadUserData(String userId) async {
    if (_isLoading) return;

    _isLoading = true;

    try {
      debugPrint('$_tag بدء تحميل بيانات المستخدم: $userId');

      UnifiedAccountModel? data;

      // الاستراتيجية: Supabase أولاً، ثم المحلي، ثم الافتراضي
      if (InternetStatusManager.isConnected) {
        data = await _loadFromSupabase(userId);
      }

      // إذا فشل Supabase، جرب المحلي
      if (data == null) {
        data = await _loadFromLocal(userId);
      }

      // ✅ أمان: لا ننشئ بيانات افتراضية للمستخدمين غير الموجودين
      if (data == null) {
        debugPrint('$_tag ❌ لا توجد بيانات للمستخدم - رفض الوصول');
        throw Exception('FORCE_EXIT: لا توجد بيانات للمستخدم');
      }

      // تحديث البيانات الحالية
      _currentData = data;
      _dataController.add(_currentData);

      debugPrint(
        '$_tag تم تحميل البيانات بنجاح: ${data.status.name} (${data.dataSource})',
      );
    } catch (e) {
      debugPrint('$_tag خطأ في تحميل البيانات: $e');

      // ✅ أمان: لا ننشئ بيانات افتراضية - إنهاء الجلسة
      debugPrint('$_tag ❌ خطأ في تحميل البيانات - إنهاء الجلسة');
      await Supabase.instance.client.auth.signOut();
      _currentData = null;
      _dataController.add(null);
      throw Exception('FORCE_EXIT: خطأ في تحميل بيانات المستخدم');
    } finally {
      _isLoading = false;
    }
  }

  /// تحميل من Supabase
  static Future<UnifiedAccountModel?> _loadFromSupabase(String userId) async {
    try {
      debugPrint('$_tag تحميل من Supabase...');

      // جلب بيانات الحساب
      final accountData = await AccountService.getAccountDataV2(userId);
      if (accountData == null) {
        debugPrint('$_tag لا توجد بيانات في Supabase');
        return null;
      }

      // جلب بيانات المستخدم
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('$_tag لا يوجد مستخدم مسجل دخول');
        return null;
      }

      // إنشاء النموذج الموحد
      final model = UnifiedAccountModel.fromSupabase(
        data: accountData,
        userId: userId,
        email: user.email ?? '',
      );

      // حفظ في قاعدة البيانات المحلية للتراجع
      await _saveToLocal(model);

      debugPrint('$_tag تم تحميل البيانات من Supabase بنجاح');
      return model;
    } catch (e) {
      debugPrint('$_tag خطأ في تحميل البيانات من Supabase: $e');
      return null;
    }
  }

  /// تحميل من قاعدة البيانات المحلية
  static Future<UnifiedAccountModel?> _loadFromLocal(String userId) async {
    try {
      debugPrint('$_tag تحميل من قاعدة البيانات المحلية...');

      // جلب البيانات المحلية من قاعدة البيانات مباشرة
      final dbHelper = DBHelper.instance;
      final localData = await dbHelper.getLocalAccountStatus(userId);
      if (localData == null) {
        debugPrint('$_tag لا توجد بيانات محلية');
        return null;
      }

      // جلب الإيميل من المستخدم الحالي أو SharedPreferences
      final user = Supabase.instance.client.auth.currentUser;
      final email = user?.email ?? localData['email'] as String? ?? '';

      if (email.isEmpty) {
        debugPrint('$_tag لا يمكن تحديد الإيميل');
        return null;
      }

      // إنشاء النموذج الموحد
      final model = UnifiedAccountModel.fromLocal(
        data: localData,
        userId: userId,
        email: email,
      );

      debugPrint('$_tag تم تحميل البيانات من قاعدة البيانات المحلية بنجاح');
      return model;
    } catch (e) {
      debugPrint('$_tag خطأ في تحميل البيانات المحلية: $e');
      return null;
    }
  }

  // ✅ تم حذف دالة _createDefaultData لأسباب أمنية

  /// حفظ البيانات محلياً
  static Future<void> _saveToLocal(UnifiedAccountModel model) async {
    try {
      final dbHelper = DBHelper.instance;
      await dbHelper.saveAccountStatus(
        userId: model.userId,
        accountStatus: model.status.name,
        isTrial: model.isTrial,
        trialDaysRemaining: model.trialDaysRemaining,
        expiryMillis: model.trialEndDate?.millisecondsSinceEpoch,
      );
      debugPrint('$_tag تم حفظ البيانات محلياً');
    } catch (e) {
      debugPrint('$_tag خطأ في حفظ البيانات محلياً: $e');
    }
  }

  /// مسح البيانات
  static void _clearData() {
    debugPrint('$_tag مسح البيانات');
    _currentData = null;
    _dataController.add(null);
  }

  /// معالج تغيير حالة الإنترنت
  static void _onInternetStatusChanged(bool isConnected) {
    debugPrint('$_tag تغيير حالة الإنترنت: $isConnected');

    if (isConnected && _currentUserId != null) {
      // عند عودة الإنترنت، حاول تحديث البيانات من Supabase
      _loadUserData(_currentUserId!);
    }
  }

  /// تحديث البيانات الحالية
  static void updateCurrentData(UnifiedAccountModel newData) {
    debugPrint('$_tag تحديث البيانات الحالية');
    _currentData = newData;
    _dataController.add(_currentData);

    // حفظ التحديث محلياً
    _saveToLocal(newData);
  }

  /// إنهاء النظام
  static void dispose() {
    debugPrint('$_tag إنهاء النظام');
    _dataController.close();
    _currentData = null;
    _currentUserId = null;
    _isInitialized = false;
  }

  /// فحص صحة البيانات
  static bool validateData(UnifiedAccountModel? data) {
    if (data == null) return false;
    if (data.userId.isEmpty) return false;
    if (data.email.isEmpty) return false;
    return true;
  }

  /// إحصائيات النظام
  static Map<String, dynamic> getSystemStats() {
    return {
      'isInitialized': _isInitialized,
      'isLoading': _isLoading,
      'hasData': _currentData != null,
      'currentUserId': _currentUserId,
      'dataSource': _currentData?.dataSource,
      'accountStatus': _currentData?.status.name,
      'lastUpdate': _currentData?.updatedAt.toIso8601String(),
    };
  }
}
