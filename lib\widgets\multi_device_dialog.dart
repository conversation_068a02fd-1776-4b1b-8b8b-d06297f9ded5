import 'package:flutter/material.dart';
import '../services/multi_device_service.dart';
import '../services/supabase_auth_service.dart';

/// حوار إدارة الأجهزة المتعددة
class MultiDeviceDialog extends StatefulWidget {
  final Map<String, dynamic> existingAccount;
  final String currentDeviceId;
  final VoidCallback? onSuccess;

  const MultiDeviceDialog({
    super.key,
    required this.existingAccount,
    required this.currentDeviceId,
    this.onSuccess,
  });

  @override
  State<MultiDeviceDialog> createState() => _MultiDeviceDialogState();
}

class _MultiDeviceDialogState extends State<MultiDeviceDialog> {
  bool _isLoading = false;
  String _loadingMessage = '';

  @override
  Widget build(BuildContext context) {
    final accountData = widget.existingAccount['user_accounts'];
    final email = accountData['email'] ?? 'غير محدد';
    final displayName = accountData['display_name'] ?? 'مستخدم';

    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.devices, color: Colors.blue),
          SizedBox(width: 8),
          Text('جهاز مرتبط بحساب'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'هذا الجهاز مرتبط بالحساب التالي:',
            style: TextStyle(fontSize: 16, color: Colors.grey[700]),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.person, size: 20, color: Colors.blue),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        displayName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.email, size: 20, color: Colors.blue),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        email,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'ماذا تريد أن تفعل؟',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey[800],
            ),
          ),
          if (_isLoading) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 12),
                Text(
                  _loadingMessage,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ],
      ),
      actions: [
        if (!_isLoading) ...[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton.icon(
            onPressed: () => _loginToExistingAccount(),
            icon: const Icon(Icons.login),
            label: const Text('تسجيل الدخول'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
          ElevatedButton.icon(
            onPressed: () => _showDeviceManagement(),
            icon: const Icon(Icons.devices_other),
            label: const Text('إدارة الأجهزة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ],
    );
  }

  /// تسجيل الدخول للحساب الموجود
  Future<void> _loginToExistingAccount() async {
    setState(() {
      _isLoading = true;
      _loadingMessage = 'جاري تسجيل الدخول...';
    });

    try {
      // إظهار حوار إدخال كلمة المرور
      final password = await _showPasswordDialog();
      if (password == null) {
        setState(() => _isLoading = false);
        return;
      }

      setState(() => _loadingMessage = 'التحقق من البيانات...');

      final accountData = widget.existingAccount['user_accounts'];
      final email = accountData['email'];

      // محاولة تسجيل الدخول
      final authService = SupabaseAuthService();
      final response = await authService.signInWithEmail(
        email: email,
        password: password,
      );

      if (response.user != null) {
        setState(() => _loadingMessage = 'ربط الجهاز...');

        // ربط الجهاز بالحساب
        final success = await MultiDeviceService.linkDeviceToAccount(
          response.user!.id,
          widget.currentDeviceId,
        );

        if (success) {
          if (mounted) {
            Navigator.of(context).pop();
            widget.onSuccess?.call();
          }
        } else {
          throw Exception('فشل في ربط الجهاز');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _loadingMessage = '';
        });
      }
    }
  }

  /// إظهار حوار إدخال كلمة المرور
  Future<String?> _showPasswordDialog() async {
    final controller = TextEditingController();

    return showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('كلمة المرور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل كلمة المرور لتسجيل الدخول:'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                Navigator.of(context).pop(controller.text);
              }
            },
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );
  }

  /// إظهار شاشة إدارة الأجهزة
  Future<void> _showDeviceManagement() async {
    // TODO: تنفيذ شاشة إدارة الأجهزة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شاشة إدارة الأجهزة قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}

/// حوار بسيط لإظهار خيارات الجهاز المرتبط
class DeviceLinkedDialog extends StatelessWidget {
  final Map<String, dynamic> linkedAccount;
  final VoidCallback? onLoginPressed;
  final VoidCallback? onManagePressed;

  const DeviceLinkedDialog({
    super.key,
    required this.linkedAccount,
    this.onLoginPressed,
    this.onManagePressed,
  });

  @override
  Widget build(BuildContext context) {
    final accountData = linkedAccount['user_accounts'];
    final displayName = accountData['display_name'] ?? 'مستخدم';

    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.info, color: Colors.blue),
          SizedBox(width: 8),
          Text('جهاز مرتبط'),
        ],
      ),
      content: Text(
        'هذا الجهاز مرتبط بحساب "$displayName".\n\n'
        'يمكنك تسجيل الدخول للحساب الموجود أو إدارة الأجهزة المرتبطة.',
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onLoginPressed?.call();
          },
          child: const Text('تسجيل الدخول'),
        ),
      ],
    );
  }
}
