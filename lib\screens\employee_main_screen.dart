// الشاشة الرئيسية للموظفين

import 'package:flutter/material.dart';
import '../models/employee_models.dart';
import '../services/employee_service.dart';
import '../supabase_login_screen.dart';
import '../features/subscribers/presentation/subscribers_list_screen.dart';
import '../features/subscribers/presentation/add_subscriber_screen.dart';
import '../features/subscribers/presentation/transactions_log_screen.dart';
import '../features/subscribers/presentation/subscription_prices_screen.dart';
import '../features/subscribers/presentation/renew_subscription_bottom_sheet.dart';
import '../features/profits/profits_screen.dart';
import '../features/devices_screen.dart';
import '../settings_page.dart';
import '../Backup_Restore_Screen.dart';
import '../features/employee_management/employee_management_screen.dart';
import '../screens/activity_log_screen.dart';
import '../db_helper.dart';
import '../features/subscribers/domain/subscribers_repository_impl.dart';
import '../features/subscribers/data/subscribers_storage_impl.dart';
import '../services/enhanced_sync_service.dart';
import '../services/data_protection_service.dart';

class EmployeeMainScreen extends StatefulWidget {
  final Employee employee;

  const EmployeeMainScreen({super.key, required this.employee});

  @override
  State<EmployeeMainScreen> createState() => _EmployeeMainScreenState();
}

class _EmployeeMainScreenState extends State<EmployeeMainScreen> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text('مرحباً ${widget.employee.name}'),
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        actions: [
          // زر تسجيل الخروج
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'تسجيل الخروج',
            onPressed: _logout,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الموظف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: colorScheme.primary,
                          child: Text(
                            widget.employee.name.substring(0, 1),
                            style: TextStyle(
                              color: colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.employee.name,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                widget.employee.email,
                                style: TextStyle(
                                  color: colorScheme.onSurface.withValues(
                                    alpha: 0.7,
                                  ),
                                ),
                              ),
                              Text(
                                'الدور: ${widget.employee.role == UserRole.manager ? 'مدير' : 'موظف'}',
                                style: TextStyle(
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // الصلاحيات المتاحة
            Text(
              'الصلاحيات المتاحة:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),

            Expanded(child: _buildPermissionsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsList() {
    final availablePermissions = widget.employee.permissions.entries
        .where((entry) => entry.value)
        .toList();

    if (availablePermissions.isEmpty) {
      return const Center(
        child: Text('لا توجد صلاحيات متاحة', style: TextStyle(fontSize: 16)),
      );
    }

    return ListView.builder(
      itemCount: availablePermissions.length,
      itemBuilder: (context, index) {
        final permission = availablePermissions[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Icon(
              _getPermissionIcon(permission.key),
              color: Theme.of(context).colorScheme.primary,
            ),
            title: Text(_getPermissionName(permission.key)),
            subtitle: Text(_getPermissionDescription(permission.key)),
            onTap: () => _handlePermissionTap(permission.key),
          ),
        );
      },
    );
  }

  IconData _getPermissionIcon(PermissionType permission) {
    switch (permission) {
      case PermissionType.viewSubscribers:
        return Icons.people_outline;
      case PermissionType.addSubscribers:
        return Icons.person_add;
      case PermissionType.editSubscribers:
        return Icons.edit;
      case PermissionType.deleteSubscribers:
        return Icons.person_remove;
      case PermissionType.viewTransactions:
        return Icons.receipt_long;
      case PermissionType.addTransactions:
        return Icons.add_circle_outline;
      case PermissionType.editTransactions:
        return Icons.edit_note;
      case PermissionType.deleteTransactions:
        return Icons.delete_outline;
      case PermissionType.renewSubscriptions:
        return Icons.refresh;
      case PermissionType.payDebts:
        return Icons.payment;
      case PermissionType.viewReports:
        return Icons.analytics;
      case PermissionType.exportData:
        return Icons.download;
      case PermissionType.viewDevices:
        return Icons.devices_other;
      case PermissionType.manageDevices:
        return Icons.devices;
      case PermissionType.createBackup:
        return Icons.backup;
      case PermissionType.restoreBackup:
        return Icons.restore;
      case PermissionType.manageEmployees:
        return Icons.admin_panel_settings;
      case PermissionType.viewEmployeeReports:
        return Icons.list_alt;
    }
  }

  String _getPermissionName(PermissionType permission) {
    switch (permission) {
      case PermissionType.viewSubscribers:
        return 'عرض المشتركين';
      case PermissionType.addSubscribers:
        return 'إضافة مشتركين';
      case PermissionType.editSubscribers:
        return 'تعديل المشتركين';
      case PermissionType.deleteSubscribers:
        return 'حذف المشتركين';
      case PermissionType.viewTransactions:
        return 'عرض المعاملات';
      case PermissionType.addTransactions:
        return 'إضافة معاملات';
      case PermissionType.editTransactions:
        return 'تعديل المعاملات';
      case PermissionType.deleteTransactions:
        return 'حذف المعاملات';
      case PermissionType.renewSubscriptions:
        return 'تجديد الاشتراكات';
      case PermissionType.payDebts:
        return 'تسديد الديون';
      case PermissionType.viewReports:
        return 'عرض التقارير';
      case PermissionType.exportData:
        return 'تصدير البيانات';
      case PermissionType.viewDevices:
        return 'عرض الأجهزة';
      case PermissionType.manageDevices:
        return 'إدارة الأجهزة';
      case PermissionType.createBackup:
        return 'إنشاء نسخة احتياطية';
      case PermissionType.restoreBackup:
        return 'استعادة نسخة احتياطية';
      case PermissionType.manageEmployees:
        return 'إدارة الموظفين';
      case PermissionType.viewEmployeeReports:
        return 'عرض تقارير الموظفين';
    }
  }

  String _getPermissionDescription(PermissionType permission) {
    switch (permission) {
      case PermissionType.viewSubscribers:
        return 'عرض قائمة المشتركين وتفاصيلهم';
      case PermissionType.addSubscribers:
        return 'إضافة مشتركين جدد للنظام';
      case PermissionType.editSubscribers:
        return 'تعديل بيانات المشتركين الموجودين';
      case PermissionType.deleteSubscribers:
        return 'حذف المشتركين من النظام';
      case PermissionType.viewTransactions:
        return 'عرض المعاملات المالية';
      case PermissionType.addTransactions:
        return 'إضافة معاملات مالية جديدة';
      case PermissionType.editTransactions:
        return 'تعديل المعاملات المالية';
      case PermissionType.deleteTransactions:
        return 'حذف المعاملات المالية';
      case PermissionType.renewSubscriptions:
        return 'تجديد اشتراكات المشتركين';
      case PermissionType.payDebts:
        return 'تسديد ديون المشتركين';
      case PermissionType.viewReports:
        return 'عرض التقارير والإحصائيات';
      case PermissionType.exportData:
        return 'تصدير البيانات إلى ملفات';
      case PermissionType.viewDevices:
        return 'عرض قائمة الأجهزة المتصلة';
      case PermissionType.manageDevices:
        return 'إدارة الأجهزة المتصلة';
      case PermissionType.createBackup:
        return 'إنشاء نسخ احتياطية من البيانات';
      case PermissionType.restoreBackup:
        return 'استعادة البيانات من النسخ الاحتياطية';
      case PermissionType.manageEmployees:
        return 'إدارة حسابات الموظفين';
      case PermissionType.viewEmployeeReports:
        return 'عرض تقارير أنشطة الموظفين';
    }
  }

  void _handlePermissionTap(PermissionType permission) async {
    try {
      switch (permission) {
        case PermissionType.viewSubscribers:
          await _navigateToSubscribersList();
          break;
        case PermissionType.addSubscribers:
          await _navigateToAddSubscriber();
          break;
        case PermissionType.editSubscribers:
          await _navigateToSubscribersList(editMode: true);
          break;
        case PermissionType.deleteSubscribers:
          await _navigateToSubscribersList(deleteMode: true);
          break;
        case PermissionType.viewTransactions:
          await _navigateToTransactions();
          break;
        case PermissionType.addTransactions:
          await _navigateToTransactions(addMode: true);
          break;
        case PermissionType.editTransactions:
          await _navigateToTransactions(editMode: true);
          break;
        case PermissionType.deleteTransactions:
          await _navigateToTransactions(deleteMode: true);
          break;
        case PermissionType.renewSubscriptions:
          await _navigateToRenewSubscriptions();
          break;
        case PermissionType.payDebts:
          await _navigateToPayDebts();
          break;
        case PermissionType.viewReports:
          await _navigateToReports();
          break;
        case PermissionType.exportData:
          await _showExportOptions();
          break;
        case PermissionType.viewDevices:
          await _navigateToDevices();
          break;
        case PermissionType.manageDevices:
          await _navigateToDevices(manageMode: true);
          break;
        case PermissionType.createBackup:
          await _showBackupOptions();
          break;
        case PermissionType.restoreBackup:
          await _showRestoreOptions();
          break;
        case PermissionType.manageEmployees:
          await _navigateToEmployeeManagement();
          break;
        case PermissionType.viewEmployeeReports:
          await _navigateToEmployeeReports();
          break;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  // دوال التنقل للصلاحيات المختلفة
  Future<void> _navigateToSubscribersList({
    bool editMode = false,
    bool deleteMode = false,
  }) async {
    if (editMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اختر مشترك من القائمة لتعديل بياناته'),
          duration: Duration(seconds: 3),
        ),
      );
    } else if (deleteMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اختر مشترك من القائمة لحذفه'),
          duration: Duration(seconds: 3),
        ),
      );
    }

    final repository = SubscribersRepositoryImpl(SubscribersStorageImpl());
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => SubscribersListScreen(
          repository: repository,
          isDarkMode: Theme.of(context).brightness == Brightness.dark,
          onToggleTheme: () {},
          showThemeStatus: (context) {},
        ),
      ),
    );
  }

  Future<void> _navigateToAddSubscriber() async {
    final repository = SubscribersRepositoryImpl(SubscribersStorageImpl());
    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddSubscriberScreen(repository: repository),
      ),
    );
  }

  Future<void> _navigateToTransactions({
    bool addMode = false,
    bool editMode = false,
    bool deleteMode = false,
  }) async {
    if (addMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يمكنك إضافة معاملة جديدة من خلال شاشة المعاملات'),
          duration: Duration(seconds: 3),
        ),
      );
    } else if (editMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اختر معاملة من القائمة لتعديلها'),
          duration: Duration(seconds: 3),
        ),
      );
    } else if (deleteMode) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('اختر معاملة من القائمة لحذفها'),
          duration: Duration(seconds: 3),
        ),
      );
    }

    await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const TransactionsLogScreen()),
    );
  }

  Future<void> _navigateToRenewSubscriptions() async {
    // الانتقال مباشرة إلى قائمة المشتركين مع رسالة توضيحية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('اختر مشترك من القائمة لتجديد اشتراكه'),
        duration: Duration(seconds: 3),
      ),
    );
    await _navigateToSubscribersList();
  }

  Future<void> _navigateToPayDebts() async {
    // الانتقال مباشرة إلى قائمة المشتركين مع رسالة توضيحية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('اختر مشترك من القائمة لتسديد دينه'),
        duration: Duration(seconds: 3),
      ),
    );
    await _navigateToSubscribersList();
  }

  Future<void> _navigateToReports() async {
    await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ProfitsScreen()));
  }

  Future<void> _showExportOptions() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير البيانات'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.download),
              title: const Text('تصدير قاعدة البيانات'),
              subtitle: const Text('تصدير جميع البيانات كملف قاعدة بيانات'),
              onTap: () {
                Navigator.pop(context);
                _exportDatabase();
              },
            ),
            ListTile(
              leading: const Icon(Icons.table_chart),
              title: const Text('تصدير كـ Excel'),
              subtitle: const Text('تصدير البيانات كملف Excel'),
              onTap: () {
                Navigator.pop(context);
                _exportToExcel();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToDevices({bool manageMode = false}) async {
    await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const DevicesScreen()));
  }

  Future<void> _showBackupOptions() async {
    await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const BackupRestoreScreen()),
    );
  }

  Future<void> _showRestoreOptions() async {
    await Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const BackupRestoreScreen()),
    );
  }

  Future<void> _navigateToEmployeeManagement() async {
    // فحص إذا كان الموظف له صلاحية إدارة الموظفين
    if (widget.employee.permissions[PermissionType.manageEmployees] == true) {
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const EmployeeManagementScreen(),
        ),
      );
    } else {
      _showFeatureMessage('إدارة الموظفين', 'هذه الميزة متاحة للمديرين فقط');
    }
  }

  Future<void> _navigateToEmployeeReports() async {
    await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ActivityLogScreen()));
  }

  // دوال التصدير
  Future<void> _exportDatabase() async {
    try {
      _showFeatureMessage(
        'تصدير قاعدة البيانات',
        'جاري تصدير قاعدة البيانات...',
      );

      // يمكن إضافة منطق التصدير هنا
      await Future.delayed(const Duration(seconds: 2));

      _showFeatureMessage('تم التصدير', 'تم تصدير قاعدة البيانات بنجاح');
    } catch (e) {
      _showFeatureMessage('خطأ في التصدير', 'فشل في تصدير قاعدة البيانات: $e');
    }
  }

  Future<void> _exportToExcel() async {
    try {
      _showFeatureMessage('تصدير Excel', 'جاري تصدير البيانات كملف Excel...');

      // يمكن إضافة منطق التصدير هنا
      await Future.delayed(const Duration(seconds: 2));

      _showFeatureMessage('تم التصدير', 'تم تصدير البيانات كملف Excel بنجاح');
    } catch (e) {
      _showFeatureMessage('خطأ في التصدير', 'فشل في تصدير ملف Excel: $e');
    }
  }

  void _showFeatureMessage(String title, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$title: $message'),
        duration: const Duration(seconds: 3),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Future<void> _logout() async {
    try {
      // إنهاء جلسة الموظف
      await EmployeeService.endSession();

      // العودة إلى شاشة تسجيل الدخول
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const SupabaseLoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسجيل الخروج: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
