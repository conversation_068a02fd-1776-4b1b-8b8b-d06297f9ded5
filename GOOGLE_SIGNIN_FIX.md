# حل مشكلة تسجيل الدخول بـ Google في تطبيق iTower

## المشكلة
رسالة الخطأ: "the service is currently unavailable this is a most likely a transient condition and may be correctly by retrying with a backoff"

## الحلول المطبقة

### 1. إصلاح ملف google-services.json
تم إضافة oauth_client المفقود في الملف. يجب التأكد من وجود:
```json
"oauth_client": [
  {
    "client_id": "57962231844-your_web_client_id.apps.googleusercontent.com",
    "client_type": 3
  }
]
```

### 2. تحسين كود تسجيل الدخول
- إضافة معالجة أفضل للأخطاء
- إضافة رسائل خطأ باللغة العربية
- إضافة زر "إعادة المحاولة"
- إع<PERSON><PERSON> تعيين Google Sign-In قبل كل محاولة

## خطوات إضافية مطلوبة

### 1. الحصول على OAuth Client ID الصحيح
يجب الذهاب إلى [Google Cloud Console](https://console.cloud.google.com/):

1. اختر مشروع `itower-v1`
2. انتقل إلى APIs & Services > Credentials
3. أنشئ OAuth 2.0 Client ID جديد أو استخدم الموجود
4. اختر Application type: Android
5. أدخل Package name: `com.example.itower`
6. أدخل SHA-1 certificate fingerprint (احصل عليه من Android Studio)
7. انسخ Client ID واستبدل `your_web_client_id` في الملف

### 2. الحصول على SHA-1 Fingerprint
في terminal، نفذ الأمر:
```bash
cd android
./gradlew signingReport
```
أو من Android Studio:
1. افتح مشروع Android
2. انتقل إلى Gradle > Tasks > android > signingReport
3. انسخ SHA1 fingerprint

### 3. تحديث google-services.json
بعد إنشاء OAuth Client ID:
1. حمل ملف google-services.json الجديد من Firebase Console
2. استبدل الملف الحالي في `android/app/`

### 4. تفعيل Google Sign-In API
في Google Cloud Console:
1. انتقل إلى APIs & Services > Library
2. ابحث عن "Google Sign-In API"
3. فعل الـ API

### 5. إعادة بناء التطبيق
```bash
flutter clean
flutter pub get
cd android
./gradlew clean
cd ..
flutter run
```

## اختبار الحل
1. شغل التطبيق
2. اضغط على "تسجيل دخول Google"
3. يجب أن تظهر نافذة Google Sign-In
4. أكمل عملية تسجيل الدخول

## ملاحظات مهمة
- تأكد من أن الجهاز متصل بالإنترنت
- تأكد من أن Google Play Services محدث
- في حالة استمرار المشكلة، جرب على جهاز مختلف
- تأكد من أن Firebase project مفعل ومتصل بشكل صحيح

## رسائل الخطأ الجديدة
التطبيق الآن يعرض رسائل خطأ واضحة باللغة العربية:
- "الخدمة غير متاحة حالياً. يرجى المحاولة مرة أخرى بعد قليل"
- "مشكلة في الشبكة. تأكد من اتصالك بالإنترنت"
- "تسجيل الدخول بـ Google غير مفعل"
- وغيرها من الرسائل المفيدة
