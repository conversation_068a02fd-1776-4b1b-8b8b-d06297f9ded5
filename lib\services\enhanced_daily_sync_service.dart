import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'enhanced_sync_service.dart';
import 'daily_sync_limits.dart';
import '../core/managers/internet_status_manager.dart';
import 'account_service.dart';
import 'deleted_account_detector.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة المزامنة اليومية الشاملة المحسنة مع النظام الجديد
/// تشمل جميع البيانات: المشتركين، المعاملات، الأجهزة، الإعدادات، اللوحات، SharedPreferences
class EnhancedDailySyncService {
  static final EnhancedDailySyncService _instance =
      EnhancedDailySyncService._internal();
  factory EnhancedDailySyncService() => _instance;
  EnhancedDailySyncService._internal();

  final EnhancedSyncService _syncService = EnhancedSyncService();

  bool _isSyncing = false;

  // مفاتيح SharedPreferences للمزامنة الشاملة
  static const String _lastSyncDateKey = 'last_comprehensive_sync_date';
  static const String _syncEnabledKey = 'comprehensive_sync_enabled';
  static const String _syncCountKey = 'comprehensive_sync_count';
  static const String _lastSyncStatusKey = 'last_comprehensive_sync_status';
  static const String _lastSyncErrorKey = 'last_comprehensive_sync_error';

  /// التحقق من إذا كان يحتاج مزامنة اليوم
  Future<bool> needsDailySync() async {
    try {
      // فحص حالة الحساب أولاً
      final user = Supabase.instance.client.auth.currentUser;
      if (user != null) {
        // فحص إذا كان الحساب منتهي محلياً
        if (await DeletedAccountDetector.isAccountExpiredLocally()) {
          debugPrint('🚫 [ENHANCED_SYNC] الحساب منتهي محلياً - إيقاف المزامنة');
          return false;
        }

        // فحص علامة إيقاف المزامنة
        final prefs = await _getSafePreferences();
        if (prefs?.getBool('account_expired_stop_sync') == true) {
          debugPrint('🚫 [ENHANCED_SYNC] المزامنة معطلة للحساب المنتهي');
          return false;
        }

        // فحص حالة الحساب من قاعدة البيانات
        try {
          final accountData = await AccountService.getAccountDataV2(user.id);
          final accountStatus = accountData?['account_status'] as String?;
          if (accountStatus == 'expired' || accountStatus == 'banned') {
            debugPrint(
              '🚫 [ENHANCED_SYNC] الحساب منتهي/محظور في قاعدة البيانات',
            );
            return false;
          }
        } catch (e) {
          debugPrint('⚠️ [ENHANCED_SYNC] خطأ في فحص حالة الحساب: $e');
          // في حالة الخطأ، نسمح بالمزامنة
        }
      }

      final prefs = await _getSafePreferences();
      if (prefs == null) {
        // في حالة فشل الوصول إلى SharedPreferences، افترض الحاجة للمزامنة
        return true;
      }

      final lastSyncDateStr = prefs.getString(_lastSyncDateKey);
      if (lastSyncDateStr == null) {
        debugPrint('لم يتم تنفيذ مزامنة محسنة من قبل - يحتاج مزامنة');
        return true;
      }

      final lastSyncDate = DateTime.parse(lastSyncDateStr);
      final today = DateTime.now();

      // فحص إذا كان آخر مزامنة في يوم مختلف
      final needsSync =
          lastSyncDate.day != today.day ||
          lastSyncDate.month != today.month ||
          lastSyncDate.year != today.year;

      // فحص الحدود المحلية للمزامنة التلقائية
      if (needsSync) {
        final autoLimits = await DailySyncLimits.canPerformAutoSync();
        if (!autoLimits['canSync']) {
          debugPrint(
            '🚫 [ENHANCED_SYNC] تم استنفاد المزامنات التلقائية اليوم: ${autoLimits['currentCount']}/${autoLimits['maxCount']}',
          );
          return false;
        }
        debugPrint(
          'آخر مزامنة محسنة كانت في: ${_formatDate(lastSyncDate)} - يحتاج مزامنة اليوم',
        );
      } else {
        debugPrint(
          'تم تنفيذ المزامنة المحسنة اليوم بالفعل في: ${_formatTime(lastSyncDate)}',
        );
      }

      return needsSync;
    } catch (e) {
      debugPrint('خطأ في فحص الحاجة للمزامنة المحسنة: $e');
      return false;
    }
  }

  /// تنفيذ المزامنة اليومية الشاملة المحسنة
  Future<DailySyncResult> performDailySync() async {
    // منع تشغيل المزامنة المتزامنة
    if (_isSyncing) {
      return DailySyncResult(
        success: true,
        message: 'المزامنة الشاملة المحسنة قيد التشغيل بالفعل',
        skipped: true,
      );
    }

    _isSyncing = true;

    try {
      debugPrint('🚀 بدء المزامنة اليومية الشاملة المحسنة...');

      // التحقق من الحاجة للمزامنة
      if (!await needsDailySync()) {
        _isSyncing = false;
        return DailySyncResult(
          success: true,
          message: 'تم تنفيذ المزامنة الشاملة المحسنة اليوم بالفعل',
          skipped: true,
        );
      }

      // التحقق من الاتصال بالإنترنت
      if (!InternetStatusManager.isConnected) {
        await _saveLastSyncStatus(false, 'لا يوجد اتصال بالإنترنت');
        _isSyncing = false;
        return DailySyncResult(
          success: false,
          message: 'لا يوجد اتصال بالإنترنت',
          skipped: false,
        );
      }

      // فحص إمكانية المزامنة التلقائية
      final eligibilityCheck = await _syncService.checkSyncEligibility(
        isManual: false,
      );
      if (!eligibilityCheck['canSync']) {
        await _saveLastSyncStatus(false, eligibilityCheck['reason']);
        _isSyncing = false;
        return DailySyncResult(
          success: false,
          message: eligibilityCheck['reason'],
          skipped: false,
        );
      }

      // تنفيذ المزامنة الشاملة التلقائية
      debugPrint(
        '✅ [ENHANCED_SYNC] جميع الشروط مستوفاة - بدء المزامنة الشاملة التلقائية...',
      );
      final syncResult = await _syncService.performFullSync(isManual: false);

      if (syncResult['success']) {
        await _saveLastSyncStatus(true, syncResult['message']);
        await _incrementSyncCount();

        debugPrint('✅ [ENHANCED_SYNC] تمت المزامنة الشاملة المحسنة بنجاح!');
        debugPrint('📊 [ENHANCED_SYNC] تفاصيل المزامنة الشاملة:');
        debugPrint(
          '   - حجم البيانات الأصلي: ${_formatBytes(syncResult['originalSize'])}',
        );
        debugPrint(
          '   - الحجم بعد الضغط والتشفير: ${_formatBytes(syncResult['compressedSize'])}',
        );
        debugPrint('   - نسبة الضغط: ${syncResult['compressionRatio']}%');
        debugPrint('   - مدة المزامنة: ${syncResult['duration']} ثانية');
        debugPrint(
          '   - النسخ المحذوفة: ${syncResult['cleanupResult']['deletedCount']}',
        );
        debugPrint('   - البيانات المزامنة: جميع الجداول والإعدادات');

        // إضافة معلومات التنظيف التلقائي
        if (syncResult['dynamicCleanup'] == true) {
          final deletedCount = syncResult['deletedOldBackups'] ?? 0;
          debugPrint(
            '🔄 [ENHANCED_SYNC] تنظيف تلقائي: تم حذف $deletedCount نسخة قديمة',
          );
        }

        // إنشاء رسالة النجاح مع معلومات التنظيف
        String successMessage = 'تمت المزامنة الشاملة المحسنة بنجاح';
        if (syncResult['dynamicCleanup'] == true) {
          final deletedCount = syncResult['deletedOldBackups'] ?? 0;
          successMessage += ' (حُذفت $deletedCount نسخة قديمة تلقائياً)';
        }

        return DailySyncResult(
          success: true,
          message: successMessage,
          skipped: false,
          details: syncResult,
        );
      } else {
        await _saveLastSyncStatus(false, syncResult['message']);

        debugPrint(
          '❌ [ENHANCED_SYNC] فشلت المزامنة الشاملة المحسنة: ${syncResult['message']}',
        );

        return DailySyncResult(
          success: false,
          message: syncResult['message'],
          skipped: false,
        );
      }
    } catch (e) {
      await _saveLastSyncStatus(false, 'خطأ غير متوقع: $e');
      debugPrint('❌ [ENHANCED_SYNC] خطأ في المزامنة الشاملة المحسنة: $e');

      return DailySyncResult(
        success: false,
        message: 'خطأ في المزامنة الشاملة المحسنة: $e',
        skipped: false,
      );
    } finally {
      _isSyncing = false;
    }
  }

  /// الحصول على إحصائيات المزامنة
  Future<Map<String, dynamic>> getSyncStatistics() async {
    try {
      return await _syncService.getUserSyncDashboard();
    } catch (e) {
      debugPrint('خطأ في جلب إحصائيات المزامنة: $e');
      return {'error': true, 'message': 'فشل في جلب الإحصائيات: $e'};
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية
  Future<List<Map<String, dynamic>>> getBackupsList() async {
    try {
      return await _syncService.getUserBackups();
    } catch (e) {
      debugPrint('خطأ في جلب قائمة النسخ: $e');
      return [];
    }
  }

  /// حفظ حالة آخر مزامنة
  Future<void> _saveLastSyncStatus(bool success, String message) async {
    try {
      final prefs = await _getSafePreferences();
      if (prefs != null) {
        await prefs.setString(
          _lastSyncDateKey,
          DateTime.now().toIso8601String(),
        );
        await prefs.setBool(_lastSyncStatusKey, success);
        await prefs.setString(_lastSyncErrorKey, success ? '' : message);
      }
    } catch (e) {
      debugPrint('خطأ في حفظ حالة المزامنة: $e');
    }
  }

  /// زيادة عداد المزامنة
  Future<void> _incrementSyncCount() async {
    try {
      final prefs = await _getSafePreferences();
      if (prefs != null) {
        final currentCount = prefs.getInt(_syncCountKey) ?? 0;
        await prefs.setInt(_syncCountKey, currentCount + 1);
      }
    } catch (e) {
      debugPrint('خطأ في تحديث عداد المزامنة: $e');
    }
  }

  /// الحصول على SharedPreferences بأمان
  Future<SharedPreferences?> _getSafePreferences() async {
    try {
      return await SharedPreferences.getInstance();
    } catch (e) {
      debugPrint('خطأ في الوصول إلى SharedPreferences: $e');
      return null;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// تنسيق الوقت
  String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق حجم البيانات
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// نتيجة المزامنة اليومية
class DailySyncResult {
  final bool success;
  final String message;
  final bool skipped;
  final Map<String, dynamic>? details;

  DailySyncResult({
    required this.success,
    required this.message,
    required this.skipped,
    this.details,
  });
}
