// نماذج بيانات الموظفين والصلاحيات

import 'dart:convert';

/// نموذج الموظف
class Employee {
  final int? id;
  final String name;
  final String email;
  final String passwordHash;
  final UserRole role;
  final Map<PermissionType, bool> permissions;
  final DateTime createdAt;
  final bool isActive;
  final String? createdByUserId;
  final String? profileImage;
  final String? phoneNumber;

  Employee({
    this.id,
    required this.name,
    required this.email,
    required this.passwordHash,
    required this.role,
    required this.permissions,
    required this.createdAt,
    this.isActive = true,
    this.createdByUserId,
    this.profileImage,
    this.phoneNumber,
  });

  // تحويل إلى Map للحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'password_hash': passwordHash,
      'role': role.name,
      'permissions': jsonEncode(permissions.map((k, v) => MapEntry(k.name, v))),
      'created_at': createdAt.toIso8601String(),
      'is_active': isActive ? 1 : 0,
      'created_by_user_id': createdByUserId,
      'profile_image': profileImage,
      'phone_number': phoneNumber,
    };
  }

  // إنشاء من Map
  factory Employee.fromMap(Map<String, dynamic> map) {
    final permissionsMap =
        jsonDecode(map['permissions'] as String) as Map<String, dynamic>;
    final permissions = <PermissionType, bool>{};

    for (final permission in PermissionType.values) {
      permissions[permission] = permissionsMap[permission.name] ?? false;
    }

    return Employee(
      id: map['id'] as int?,
      name: map['name'] as String,
      email: map['email'] as String,
      passwordHash: map['password_hash'] as String,
      role: UserRole.values.firstWhere((r) => r.name == map['role']),
      permissions: permissions,
      createdAt: DateTime.parse(map['created_at'] as String),
      isActive: (map['is_active'] as int) == 1,
      createdByUserId: map['created_by_user_id'] as String?,
      profileImage: map['profile_image'] as String?,
      phoneNumber: map['phone_number'] as String?,
    );
  }

  // فحص صلاحية معينة
  bool hasPermission(PermissionType permission) {
    if (role == UserRole.manager) return true; // المدير له كل الصلاحيات
    return permissions[permission] ?? false;
  }

  // نسخ مع تعديل
  Employee copyWith({
    int? id,
    String? name,
    String? email,
    String? passwordHash,
    UserRole? role,
    Map<PermissionType, bool>? permissions,
    DateTime? createdAt,
    bool? isActive,
    String? createdByUserId,
    String? profileImage,
    String? phoneNumber,
  }) {
    return Employee(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      passwordHash: passwordHash ?? this.passwordHash,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      createdByUserId: createdByUserId ?? this.createdByUserId,
      profileImage: profileImage ?? this.profileImage,
      phoneNumber: phoneNumber ?? this.phoneNumber,
    );
  }
}

/// أنواع الصلاحيات
enum PermissionType {
  // إدارة المشتركين
  viewSubscribers,
  addSubscribers,
  editSubscribers,
  deleteSubscribers,

  // إدارة المعاملات
  viewTransactions,
  addTransactions,
  editTransactions,
  deleteTransactions,

  // العمليات المالية
  renewSubscriptions,
  payDebts,

  // إدارة الأجهزة
  viewDevices,
  manageDevices,

  // التقارير والإحصائيات
  viewReports,
  exportData,

  // النسخ الاحتياطي
  createBackup,
  restoreBackup,

  // إدارة الموظفين
  manageEmployees,
  viewEmployeeReports,
}

/// أدوار المستخدمين
enum UserRole {
  manager, // المدير - له كل الصلاحيات ويدير الموظفين
  employee, // الموظف - صلاحيات محددة من قبل المدير
}

/// نموذج جلسة الموظف
class EmployeeSession {
  final int employeeId;
  final String employeeName;
  final String employeeEmail;
  final UserRole role;
  final Map<PermissionType, bool> permissions;
  final DateTime loginTime;
  final String? deviceInfo;

  EmployeeSession({
    required this.employeeId,
    required this.employeeName,
    required this.employeeEmail,
    required this.role,
    required this.permissions,
    required this.loginTime,
    this.deviceInfo,
  });

  // فحص صلاحية
  bool hasPermission(PermissionType permission) {
    if (role == UserRole.manager) return true;
    return permissions[permission] ?? false;
  }
}

/// نموذج سجل نشاط الموظف
class EmployeeActivityLog {
  final int? id;
  final int employeeId;
  final String action;
  final String? details;
  final DateTime timestamp;
  final String? ipAddress;

  EmployeeActivityLog({
    this.id,
    required this.employeeId,
    required this.action,
    this.details,
    required this.timestamp,
    this.ipAddress,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'employee_id': employeeId,
      'action': action,
      'details': details,
      'timestamp': timestamp.toIso8601String(),
      'ip_address': ipAddress,
    };
  }

  factory EmployeeActivityLog.fromMap(Map<String, dynamic> map) {
    return EmployeeActivityLog(
      id: map['id'] as int?,
      employeeId: map['employee_id'] as int,
      action: map['action'] as String,
      details: map['details'] as String?,
      timestamp: DateTime.parse(map['timestamp'] as String),
      ipAddress: map['ip_address'] as String?,
    );
  }
}
