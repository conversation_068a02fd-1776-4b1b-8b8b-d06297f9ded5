import 'subscriber_model.dart';
import 'transaction_model.dart';

abstract class SubscribersStorage {
  Future<List<Subscriber>> getAll({int? boardId});
  Future<void> add(Subscriber subscriber);
  Future<void> update(Subscriber subscriber);
  Future<void> delete(int id);
  Future<void> payDebt(int subscriberId, double amount, {String? note});
  Future<void> insertTransaction(Transaction transaction);
  Future<Subscriber?> getById(int id);
}
