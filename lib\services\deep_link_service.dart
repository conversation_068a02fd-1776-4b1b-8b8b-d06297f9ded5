import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../password_recovery_screen.dart';

/// خدمة التعامل مع Deep Links لاستعادة كلمة المرور
class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  factory DeepLinkService() => _instance;
  DeepLinkService._internal();

  late AppLinks _appLinks;
  BuildContext? _context;

  // متغير لتتبع ما إذا كان هناك طلب استعادة كلمة مرور معلق
  bool _hasPendingPasswordReset = false;

  // متغير لتتبع الروابط المعالجة لمنع المعالجة المضاعفة
  final Set<String> _processedLinks = {};

  /// تهيئة خدمة Deep Links
  Future<void> initialize(BuildContext context) async {
    _context = context;
    _appLinks = AppLinks();

    debugPrint('🔗 [DEEP_LINK] تهيئة خدمة Deep Links');
    debugPrint('🔗 [DEEP_LINK] السياق: ${context.runtimeType}');

    // الاستماع للروابط الواردة
    _appLinks.uriLinkStream.listen(
      (Uri uri) {
        debugPrint('🔗 [DEEP_LINK] تم استلام رابط جديد: $uri');
        _handleIncomingLink(uri);
      },
      onError: (err) {
        debugPrint('❌ [DEEP_LINK] خطأ في معالجة الرابط: $err');
      },
    );

    // فحص الرابط الأولي (إذا تم فتح التطبيق من رابط)
    try {
      debugPrint('🔗 [DEEP_LINK] فحص الرابط الأولي...');
      final initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        debugPrint('🔗 [DEEP_LINK] وُجد رابط أولي: $initialUri');
        debugPrint(
          '🔗 [DEEP_LINK] نوع الرابط: ${initialUri.scheme}://${initialUri.host}${initialUri.path}',
        );

        // معالجة فورية للرابط الأولي
        _handleIncomingLink(initialUri);
      } else {
        debugPrint('🔗 [DEEP_LINK] لا يوجد رابط أولي');
      }
    } catch (e) {
      debugPrint('❌ [DEEP_LINK] خطأ في الحصول على الرابط الأولي: $e');
    }
  }

  /// معالجة الروابط الواردة
  void _handleIncomingLink(Uri uri) {
    debugPrint('🔍 [DEEP_LINK] معالجة الرابط: ${uri.toString()}');
    debugPrint('🔍 [DEEP_LINK] المضيف: ${uri.host}');
    debugPrint('🔍 [DEEP_LINK] المسار: ${uri.path}');
    debugPrint('🔍 [DEEP_LINK] المعاملات: ${uri.queryParameters}');
    debugPrint('🔍 [DEEP_LINK] Fragment: ${uri.fragment}');

    // فحص فوري للرابط
    if (uri.host == 'iwtvsvfqmafsziqnoekm.supabase.co') {
      debugPrint('🔍 [DEEP_LINK] رابط Supabase - معالجة فورية');
      _handleSupabaseLink(uri);
    } else if (uri.scheme == 'itower') {
      debugPrint('🔍 [DEEP_LINK] رابط مخصص - معالجة فورية');
      _handleCustomSchemeLink(uri);
    } else {
      debugPrint('⚠️ [DEEP_LINK] رابط غير معروف: ${uri.toString()}');
      debugPrint('⚠️ [DEEP_LINK] المضيف: ${uri.host}');
      debugPrint('⚠️ [DEEP_LINK] المخطط: ${uri.scheme}');

      // محاولة معالجة كرابط Supabase إذا كان يحتوي على tokens
      if (uri.fragment.contains('access_token') ||
          uri.queryParameters.containsKey('access_token')) {
        debugPrint(
          '🔍 [DEEP_LINK] يحتوي على access_token - معالجة كرابط Supabase',
        );
        _handleSupabaseLink(uri);
      }
    }
  }

  /// معالجة روابط Supabase
  void _handleSupabaseLink(Uri uri) {
    debugPrint('🔍 [SUPABASE_LINK] معالجة رابط Supabase');
    debugPrint('🔍 [SUPABASE_LINK] الرابط الكامل: ${uri.toString()}');
    debugPrint('🔍 [SUPABASE_LINK] Fragment: ${uri.fragment}');
    debugPrint('🔍 [SUPABASE_LINK] Query Parameters: ${uri.queryParameters}');

    // محاولة استخراج الـ tokens من fragment أو query parameters
    String? accessToken;
    String? refreshToken;
    String? type;

    // أولاً: محاولة استخراج من fragment
    if (uri.fragment.isNotEmpty) {
      accessToken = _extractTokenFromFragment(uri.fragment, 'access_token');
      refreshToken = _extractTokenFromFragment(uri.fragment, 'refresh_token');
      type = _extractTokenFromFragment(uri.fragment, 'type');
    }

    // ثانياً: محاولة استخراج من query parameters إذا لم توجد في fragment
    if (accessToken == null || refreshToken == null || type == null) {
      accessToken ??= uri.queryParameters['access_token'];
      refreshToken ??= uri.queryParameters['refresh_token'];
      type ??= uri.queryParameters['type'];
    }

    debugPrint('🔍 [SUPABASE_LINK] نوع العملية: $type');
    debugPrint('🔍 [SUPABASE_LINK] Access Token موجود: ${accessToken != null}');
    debugPrint(
      '🔍 [SUPABASE_LINK] Refresh Token موجود: ${refreshToken != null}',
    );

    if (type == 'recovery' && accessToken != null && refreshToken != null) {
      _handlePasswordRecovery(accessToken, refreshToken);
    } else {
      debugPrint('⚠️ [SUPABASE_LINK] رابط غير صالح أو ناقص');
      debugPrint(
        '⚠️ [SUPABASE_LINK] Type: $type, Access: ${accessToken != null}, Refresh: ${refreshToken != null}',
      );
      _showErrorMessage('رابط استعادة كلمة المرور غير صالح أو منتهي الصلاحية');
    }
  }

  /// معالجة روابط التطبيق المخصصة
  void _handleCustomSchemeLink(Uri uri) {
    debugPrint('🔍 [CUSTOM_LINK] معالجة رابط مخصص: ${uri.host}${uri.path}');
    debugPrint('🔍 [CUSTOM_LINK] المعاملات: ${uri.queryParameters}');
    debugPrint('🔍 [CUSTOM_LINK] المضيف: "${uri.host}"');
    debugPrint('🔍 [CUSTOM_LINK] المسار: "${uri.path}"');

    if (uri.host == 'reset-password' || uri.path == '/reset-password') {
      debugPrint('✅ [CUSTOM_LINK] تم التعرف على طلب إعادة تعيين كلمة المرور');

      // فحص وجود code
      final code = uri.queryParameters['code'];
      debugPrint('🔍 [CUSTOM_LINK] البحث عن code في المعاملات...');

      if (code != null && code.isNotEmpty) {
        debugPrint('✅ [CUSTOM_LINK] وُجد code: ${code.substring(0, 8)}...');
        debugPrint('🔄 [CUSTOM_LINK] استدعاء _handlePasswordResetCode...');
        _handlePasswordResetCode(code);
      } else {
        debugPrint('❌ [CUSTOM_LINK] لا يوجد code في الرابط');
        debugPrint(
          '❌ [CUSTOM_LINK] المعاملات المتاحة: ${uri.queryParameters.keys.toList()}',
        );
        _showErrorMessage(
          'رابط استعادة كلمة المرور غير صالح - لا يوجد رمز تحقق',
        );
      }
    } else {
      debugPrint(
        '❌ [CUSTOM_LINK] رابط مخصص غير معروف: "${uri.host}${uri.path}"',
      );
      debugPrint(
        '❌ [CUSTOM_LINK] المتوقع: "reset-password" أو "/reset-password"',
      );
    }
  }

  /// استخراج token من fragment
  String? _extractTokenFromFragment(String fragment, String key) {
    try {
      debugPrint('🔍 [TOKEN_EXTRACT] البحث عن $key في: $fragment');

      // تنظيف fragment من # في البداية
      String cleanFragment = fragment.startsWith('#')
          ? fragment.substring(1)
          : fragment;

      // تقسيم المعاملات
      final params = cleanFragment.split('&');
      debugPrint('🔍 [TOKEN_EXTRACT] المعاملات المقسمة: $params');

      for (final param in params) {
        if (param.contains('=')) {
          final parts = param.split('=');
          if (parts.length == 2 && parts[0] == key) {
            final value = Uri.decodeComponent(parts[1]);
            debugPrint(
              '✅ [TOKEN_EXTRACT] تم العثور على $key: ${value.substring(0, 20)}...',
            );
            return value;
          }
        }
      }

      debugPrint('❌ [TOKEN_EXTRACT] لم يتم العثور على $key');
      return null;
    } catch (e) {
      debugPrint('❌ [TOKEN_EXTRACT] خطأ في استخراج $key: $e');
      return null;
    }
  }

  /// معالجة رمز استعادة كلمة المرور
  Future<void> _handlePasswordResetCode(String code) async {
    debugPrint('🚀 [PASSWORD_RESET_CODE] تم استدعاء الدالة بنجاح!');
    debugPrint(
      '🔍 [PASSWORD_RESET_CODE] الكود المستلم: ${code.substring(0, 8)}...',
    );

    // فحص إذا تم معالجة هذا الرابط من قبل
    if (_processedLinks.contains(code)) {
      debugPrint(
        '⚠️ [PASSWORD_RESET_CODE] تم معالجة هذا الرابط من قبل - تجاهل',
      );
      return;
    }

    // إضافة الرابط للقائمة المعالجة
    _processedLinks.add(code);

    // فحص إذا كان المستخدم مسجل دخول بالفعل
    final currentUser = Supabase.instance.client.auth.currentUser;
    if (currentUser != null) {
      debugPrint(
        '⚠️ [PASSWORD_RESET_CODE] المستخدم مسجل دخول بالفعل - تجاهل الرابط',
      );
      debugPrint(
        '⚠️ [PASSWORD_RESET_CODE] معرف المستخدم الحالي: ${currentUser.id}',
      );
      // لا نظهر رسالة خطأ للمستخدم المسجل دخول، فقط نتجاهل الرابط بصمت
      // _showErrorMessage('أنت مسجل دخول بالفعل. لا حاجة لاستعادة كلمة المرور');
      return;
    }

    try {
      debugPrint('🔍 [PASSWORD_RESET_CODE] بدء معالجة رمز الاستعادة');

      // تبديل الـ code بـ session باستخدام Supabase
      // استخدام exchangeCodeForSession بدلاً من verifyOTP
      debugPrint('🔄 [PASSWORD_RESET_CODE] استدعاء exchangeCodeForSession...');

      final response = await Supabase.instance.client.auth
          .exchangeCodeForSession(code);

      if (response.session.accessToken.isNotEmpty) {
        debugPrint(
          '✅ [PASSWORD_RESET_CODE] تم تبديل الـ code بـ session بنجاح',
        );
        debugPrint(
          '✅ [PASSWORD_RESET_CODE] معرف المستخدم: ${response.session.user.id}',
        );

        // إظهار رسالة نجاح للمستخدم
        _showSuccessMessage(
          'تم التحقق من الرابط بنجاح! جاري الانتقال لشاشة تحديث كلمة المرور...',
        );

        // الانتقال الفوري لشاشة استعادة كلمة المرور
        _hasPendingPasswordReset = true;

        // محاولة التنقل الفوري
        _navigateToPasswordRecovery();

        // محاولة إضافية مؤجلة للتأكد
        Future.delayed(const Duration(milliseconds: 500), () {
          if (_hasPendingPasswordReset) {
            _navigateToPasswordRecovery();
          }
        });
      } else {
        debugPrint('❌ [PASSWORD_RESET_CODE] فشل في تبديل الـ code');
        _showErrorMessage('رمز استعادة كلمة المرور غير صالح أو منتهي الصلاحية');
      }
    } catch (e) {
      debugPrint('❌ [PASSWORD_RESET_CODE] خطأ في معالجة رمز الاستعادة: $e');

      String errorMessage = 'حدث خطأ في معالجة رابط الاستعادة';

      if (e.toString().contains('expired')) {
        errorMessage =
            'رابط استعادة كلمة المرور منتهي الصلاحية. يرجى طلب رابط جديد';
      } else if (e.toString().contains('invalid')) {
        errorMessage =
            'رابط استعادة كلمة المرور غير صالح. يرجى التأكد من الرابط';
      } else if (e.toString().contains('already_used')) {
        errorMessage = 'تم استخدام هذا الرابط من قبل. يرجى طلب رابط جديد';
      }

      _showErrorMessage(errorMessage);
    }
  }

  /// معالجة استعادة كلمة المرور
  Future<void> _handlePasswordRecovery(
    String accessToken,
    String refreshToken,
  ) async {
    try {
      debugPrint('🔍 [PASSWORD_RECOVERY] بدء عملية استعادة كلمة المرور');

      // تعيين الجلسة في Supabase باستخدام الطريقة الصحيحة
      final response = await Supabase.instance.client.auth.getSessionFromUrl(
        Uri.parse(
          'https://iwtvsvfqmafsziqnoekm.supabase.co/auth/v1/callback#access_token=$accessToken&refresh_token=$refreshToken&type=recovery',
        ),
      );

      if (response.session.accessToken.isNotEmpty) {
        debugPrint('✅ [PASSWORD_RECOVERY] تم تعيين الجلسة بنجاح');
        debugPrint(
          '✅ [PASSWORD_RECOVERY] معرف المستخدم: ${response.session.user.id}',
        );

        // إظهار رسالة نجاح للمستخدم
        _showSuccessMessage(
          'تم تعيين الجلسة بنجاح! جاري الانتقال لشاشة تحديث كلمة المرور...',
        );

        // الانتقال الفوري لشاشة استعادة كلمة المرور
        _hasPendingPasswordReset = true;

        // محاولة التنقل الفوري
        _navigateToPasswordRecovery();

        // محاولة إضافية مؤجلة للتأكد
        Future.delayed(const Duration(milliseconds: 500), () {
          if (_hasPendingPasswordReset) {
            _navigateToPasswordRecovery();
          }
        });

        // محاولة أخيرة بعد تأخير أطول
        Future.delayed(const Duration(milliseconds: 2000), () {
          if (_hasPendingPasswordReset) {
            _navigateToPasswordRecovery();
          }
        });
      } else {
        debugPrint('❌ [PASSWORD_RECOVERY] فشل في تعيين الجلسة');
        debugPrint('❌ [PASSWORD_RECOVERY] الاستجابة: $response');
        _showErrorMessage('فشل في تعيين الجلسة. يرجى المحاولة مرة أخرى');
      }
    } catch (e) {
      debugPrint('❌ [PASSWORD_RECOVERY] خطأ في معالجة استعادة كلمة المرور: $e');
      _showErrorMessage('حدث خطأ في معالجة رابط الاستعادة');
    }
  }

  /// إظهار رسالة خطأ
  void _showErrorMessage(String message) {
    if (_context != null && _context!.mounted) {
      ScaffoldMessenger.of(_context!).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// إظهار رسالة نجاح
  void _showSuccessMessage(String message) {
    if (_context != null && _context!.mounted) {
      ScaffoldMessenger.of(_context!).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// التنقل لشاشة استعادة كلمة المرور
  void _navigateToPasswordRecovery() {
    debugPrint('🔄 [NAVIGATION] فحص السياق المتاح...');

    // محاولة استخدام السياق المحلي أولاً
    BuildContext? context = _context;

    // إذا لم يكن متاحاً، محاولة الحصول على سياق من Navigator العالمي
    if (context == null || !context.mounted) {
      debugPrint('🔄 [NAVIGATION] السياق المحلي غير متاح، البحث عن بديل...');

      // محاولة الحصول على السياق من WidgetsBinding
      try {
        final navigatorState = WidgetsBinding.instance.rootElement
            ?.findAncestorStateOfType<NavigatorState>();
        if (navigatorState != null) {
          context = navigatorState.context;
          debugPrint('✅ [NAVIGATION] تم العثور على سياق من NavigatorState');
        }
      } catch (e) {
        debugPrint('❌ [NAVIGATION] خطأ في الحصول على NavigatorState: $e');
      }
    }

    if (context == null || !context.mounted) {
      debugPrint('❌ [NAVIGATION] لا يوجد سياق متاح للتنقل');
      return;
    }

    try {
      debugPrint('🔄 [NAVIGATION] محاولة الانتقال لشاشة استعادة كلمة المرور');

      _hasPendingPasswordReset = false;

      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const PasswordRecoveryScreen()),
        (route) => false, // إزالة جميع الشاشات السابقة
      );

      debugPrint('✅ [NAVIGATION] تم الانتقال لشاشة استعادة كلمة المرور بنجاح');
    } catch (e) {
      debugPrint('❌ [NAVIGATION] خطأ في التنقل: $e');

      // محاولة بديلة
      try {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const PasswordRecoveryScreen(),
          ),
        );
        debugPrint('✅ [NAVIGATION] تم الانتقال باستخدام pushReplacement');
        _hasPendingPasswordReset = false;
      } catch (e2) {
        debugPrint('❌ [NAVIGATION] فشل في التنقل نهائياً: $e2');
        _showErrorMessage(
          'تم تعيين الجلسة بنجاح. يرجى الذهاب لشاشة تحديث كلمة المرور من الإعدادات.',
        );
      }
    }
  }

  /// تحديث السياق
  void updateContext(BuildContext context) {
    _context = context;

    // إذا كان هناك طلب استعادة كلمة مرور معلق، حاول التنقل مرة أخرى
    if (_hasPendingPasswordReset) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_context != null && _context!.mounted && _hasPendingPasswordReset) {
          _hasPendingPasswordReset = false;
          try {
            Navigator.of(_context!).pushAndRemoveUntil(
              MaterialPageRoute(
                builder: (context) => const PasswordRecoveryScreen(),
              ),
              (route) => false,
            );
            debugPrint(
              '✅ [NAVIGATION] تم الانتقال المؤجل لشاشة استعادة كلمة المرور',
            );
          } catch (e) {
            debugPrint('❌ [NAVIGATION] فشل في التنقل المؤجل: $e');
          }
        }
      });
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _context = null;
  }
}
