-- ===================================================================
-- إصلاح مشكلة العمود is_trial المفقود
-- يجب تنفيذ هذا في Supabase SQL Editor
-- ===================================================================

-- 1. فحص وجود العمود is_trial في جدول user_accounts
DO $$
BEGIN
    -- فحص إذا كان العمود موجود
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'is_trial'
    ) THEN
        -- إضافة العمود إذا لم يكن موجود
        ALTER TABLE user_accounts ADD COLUMN is_trial BOOLEAN DEFAULT true;
        RAISE NOTICE 'تم إضافة العمود is_trial إلى جدول user_accounts';
    ELSE
        RAISE NOTICE 'العمود is_trial موجود بالفعل في جدول user_accounts';
    END IF;
END $$;

-- 2. فحص وجود العمود trial_days
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'trial_days'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN trial_days INTEGER DEFAULT 15;
        RAISE NOTICE 'تم إضافة العمود trial_days إلى جدول user_accounts';
    ELSE
        RAISE NOTICE 'العمود trial_days موجود بالفعل في جدول user_accounts';
    END IF;
END $$;

-- 3. فحص وجود العمود expiry_millis
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'expiry_millis'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN expiry_millis BIGINT DEFAULT 0;
        RAISE NOTICE 'تم إضافة العمود expiry_millis إلى جدول user_accounts';
    ELSE
        RAISE NOTICE 'العمود expiry_millis موجود بالفعل في جدول user_accounts';
    END IF;
END $$;

-- 4. فحص وجود العمود creation_millis
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'creation_millis'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN creation_millis BIGINT DEFAULT 0;
        RAISE NOTICE 'تم إضافة العمود creation_millis إلى جدول user_accounts';
    ELSE
        RAISE NOTICE 'العمود creation_millis موجود بالفعل في جدول user_accounts';
    END IF;
END $$;

-- 5. فحص وجود العمود activation_millis
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'activation_millis'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN activation_millis BIGINT DEFAULT 0;
        RAISE NOTICE 'تم إضافة العمود activation_millis إلى جدول user_accounts';
    ELSE
        RAISE NOTICE 'العمود activation_millis موجود بالفعل في جدول user_accounts';
    END IF;
END $$;

-- 6. فحص وجود العمود active_package
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'active_package'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN active_package TEXT DEFAULT '';
        RAISE NOTICE 'تم إضافة العمود active_package إلى جدول user_accounts';
    ELSE
        RAISE NOTICE 'العمود active_package موجود بالفعل في جدول user_accounts';
    END IF;
END $$;

-- 7. فحص وجود العمود display_name
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'display_name'
    ) THEN
        ALTER TABLE user_accounts ADD COLUMN display_name TEXT DEFAULT '';
        RAISE NOTICE 'تم إضافة العمود display_name إلى جدول user_accounts';
    ELSE
        RAISE NOTICE 'العمود display_name موجود بالفعل في جدول user_accounts';
    END IF;
END $$;

-- 8. إصلاح أو إعادة إنشاء trigger تحديث updated_at
DROP TRIGGER IF EXISTS update_user_accounts_updated_at ON user_accounts;

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_accounts_updated_at
    BEFORE UPDATE ON user_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. إصلاح trigger تحديث حالة الحساب (إذا كان موجود)
DROP TRIGGER IF EXISTS update_account_status_trigger ON user_accounts;

CREATE OR REPLACE FUNCTION update_account_status()
RETURNS TRIGGER AS $$
BEGIN
    -- تحديث حالة الحساب بناءً على البيانات الجديدة
    IF NEW.is_trial = true THEN
        IF NEW.expiry_millis > EXTRACT(EPOCH FROM NOW()) * 1000 THEN
            NEW.account_status = 'trial';
            NEW.trial_days_remaining = GREATEST(0, FLOOR((NEW.expiry_millis - EXTRACT(EPOCH FROM NOW()) * 1000) / (24 * 60 * 60 * 1000)));
        ELSE
            NEW.account_status = 'expired';
            NEW.trial_days_remaining = 0;
        END IF;
    ELSE
        NEW.account_status = 'active';
        NEW.trial_days_remaining = 0;
        IF NEW.expiry_millis > 0 THEN
            NEW.subscription_end = TO_TIMESTAMP(NEW.expiry_millis / 1000);
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق الدالة على جدول user_accounts فقط إذا كانت الأعمدة الجديدة موجودة
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_accounts' 
        AND column_name = 'account_status'
    ) THEN
        CREATE TRIGGER update_account_status_trigger
            BEFORE INSERT OR UPDATE ON user_accounts
            FOR EACH ROW EXECUTE FUNCTION update_account_status();
        RAISE NOTICE 'تم إنشاء trigger تحديث حالة الحساب';
    ELSE
        RAISE NOTICE 'العمود account_status غير موجود - تخطي إنشاء trigger';
    END IF;
END $$;

-- 10. تحديث البيانات الموجودة لضمان التوافق
UPDATE user_accounts 
SET 
    is_trial = COALESCE(is_trial, true),
    trial_days = COALESCE(trial_days, 15),
    expiry_millis = COALESCE(expiry_millis, 0),
    creation_millis = COALESCE(creation_millis, 0),
    activation_millis = COALESCE(activation_millis, 0),
    active_package = COALESCE(active_package, ''),
    display_name = COALESCE(display_name, '')
WHERE 
    is_trial IS NULL 
    OR trial_days IS NULL 
    OR expiry_millis IS NULL 
    OR creation_millis IS NULL 
    OR activation_millis IS NULL 
    OR active_package IS NULL 
    OR display_name IS NULL;

-- 11. إنشاء فهارس إضافية إذا لم تكن موجودة
CREATE INDEX IF NOT EXISTS idx_user_accounts_is_trial ON user_accounts(is_trial);
CREATE INDEX IF NOT EXISTS idx_user_accounts_expiry ON user_accounts(expiry_millis);
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON user_accounts(user_id);

-- 12. رسالة تأكيد
SELECT 'تم إصلاح جدول user_accounts وجميع الأعمدة المطلوبة موجودة الآن! ✅' as status;
