import 'package:flutter/material.dart';
import '../../../models/employee_models.dart';
import '../../../services/employee_service.dart';

/// حوار إضافة موظف جديد
class AddEmployeeDialog extends StatefulWidget {
  final VoidCallback onEmployeeAdded;

  const AddEmployeeDialog({super.key, required this.onEmployeeAdded});

  @override
  State<AddEmployeeDialog> createState() => _AddEmployeeDialogState();
}

class _AddEmployeeDialogState extends State<AddEmployeeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();

  UserRole _selectedRole = UserRole.employee;
  Map<PermissionType, bool> _selectedPermissions = {};
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                colorScheme.surface,
                colorScheme.surface.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header مع تصميم عصري
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primary.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        Icons.person_add_rounded,
                        color: colorScheme.onPrimary,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'إضافة موظف جديد',
                            style: TextStyle(
                              color: colorScheme.onPrimary,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'إنشاء حساب موظف جديد مع تحديد الصلاحيات',
                            style: TextStyle(
                              color: colorScheme.onPrimary.withValues(
                                alpha: 0.8,
                              ),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: Icon(
                        Icons.close_rounded,
                        color: colorScheme.onPrimary,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Flexible(
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // اسم الموظف
                        _buildModernTextField(
                          controller: _nameController,
                          label: 'اسم الموظف',
                          icon: Icons.person_rounded,
                          isRequired: true,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال اسم الموظف';
                            }
                            if (value.trim().length < 2) {
                              return 'اسم الموظف يجب أن يكون أكثر من حرفين';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),

                        // البريد الإلكتروني
                        _buildModernTextField(
                          controller: _emailController,
                          label: 'البريد الإلكتروني',
                          icon: Icons.email_rounded,
                          isRequired: true,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'يرجى إدخال البريد الإلكتروني';
                            }
                            if (!RegExp(
                              r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                            ).hasMatch(value)) {
                              return 'يرجى إدخال بريد إلكتروني صحيح';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),

                        // كلمة المرور
                        _buildModernTextField(
                          controller: _passwordController,
                          label: 'كلمة المرور',
                          icon: Icons.lock_rounded,
                          isRequired: true,
                          obscureText: _obscurePassword,
                          suffixIcon: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility_rounded
                                  : Icons.visibility_off_rounded,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال كلمة المرور';
                            }
                            if (value.length < 6) {
                              return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 20),

                        // رقم الهاتف (اختياري)
                        _buildModernTextField(
                          controller: _phoneController,
                          label: 'رقم الهاتف (اختياري)',
                          icon: Icons.phone_rounded,
                          keyboardType: TextInputType.phone,
                        ),
                        const SizedBox(height: 20),

                        // نوع المستخدم
                        _buildUserRoleSelector(),
                        const SizedBox(height: 20),

                        // الصلاحيات
                        if (_selectedRole == UserRole.employee) ...[
                          _buildPermissionsSection(),
                        ],
                      ],
                    ),
                  ),
                ),
              ),

              // Action Buttons مع تصميم عصري
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(24),
                    bottomRight: Radius.circular(24),
                  ),
                  border: Border(
                    top: BorderSide(
                      color: colorScheme.outline.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    // زر الإلغاء
                    Expanded(
                      child: Container(
                        height: 48,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: colorScheme.outline.withValues(alpha: 0.3),
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: _isLoading
                                ? null
                                : () => Navigator.of(context).pop(),
                            child: Center(
                              child: Text(
                                'إلغاء',
                                style: TextStyle(
                                  color: colorScheme.onSurface,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    // زر الإضافة
                    Expanded(
                      flex: 2,
                      child: Container(
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              colorScheme.primary,
                              colorScheme.primary.withValues(alpha: 0.8),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: colorScheme.primary.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: _isLoading ? null : _addEmployee,
                            child: Center(
                              child: _isLoading
                                  ? SizedBox(
                                      width: 24,
                                      height: 24,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              colorScheme.onPrimary,
                                            ),
                                      ),
                                    )
                                  : Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.person_add_rounded,
                                          color: colorScheme.onPrimary,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'إضافة الموظف',
                                          style: TextStyle(
                                            color: colorScheme.onPrimary,
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPermissionsGrid() {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      children: [
        // صلاحيات المشتركين
        _buildModernPermissionSection(
          'إدارة المشتركين',
          Icons.people_rounded,
          colorScheme.primary,
          [
            PermissionType.viewSubscribers,
            PermissionType.addSubscribers,
            PermissionType.editSubscribers,
            PermissionType.deleteSubscribers,
          ],
        ),
        const SizedBox(height: 16),

        // صلاحيات المعاملات
        _buildModernPermissionSection(
          'المعاملات المالية',
          Icons.payment_rounded,
          Colors.green,
          [
            PermissionType.viewTransactions,
            PermissionType.addTransactions,
            PermissionType.renewSubscriptions,
            PermissionType.payDebts,
          ],
        ),
        const SizedBox(height: 16),

        // صلاحيات التقارير والبيانات
        _buildModernPermissionSection(
          'التقارير والبيانات',
          Icons.analytics_rounded,
          Colors.orange,
          [
            PermissionType.viewReports,
            PermissionType.exportData,
            PermissionType.viewEmployeeReports,
          ],
        ),
        const SizedBox(height: 16),

        // صلاحيات النظام
        _buildModernPermissionSection(
          'إدارة النظام',
          Icons.settings_rounded,
          Colors.purple,
          [
            PermissionType.viewDevices,
            PermissionType.manageDevices,
            PermissionType.createBackup,
            PermissionType.restoreBackup,
            PermissionType.manageEmployees,
          ],
        ),
      ],
    );
  }

  Widget _buildModernPermissionSection(
    String title,
    IconData icon,
    Color sectionColor,
    List<PermissionType> permissions,
  ) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: sectionColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: sectionColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: sectionColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 18, color: sectionColor),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    color: sectionColor,
                  ),
                ),
              ),
              // عداد الصلاحيات المحددة
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: sectionColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${permissions.where((p) => _selectedPermissions[p] == true).length}/${permissions.length}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: sectionColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // قائمة الصلاحيات
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: permissions.map((permission) {
              final isSelected = _selectedPermissions[permission] == true;
              return Container(
                decoration: BoxDecoration(
                  color: isSelected ? sectionColor : colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? sectionColor
                        : colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: () {
                      setState(() {
                        _selectedPermissions[permission] = !isSelected;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isSelected) ...[
                            Icon(
                              Icons.check_circle_rounded,
                              size: 16,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 6),
                          ],
                          Text(
                            _getPermissionTitle(permission),
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: isSelected
                                  ? Colors.white
                                  : colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  String _getPermissionTitle(PermissionType permission) {
    switch (permission) {
      case PermissionType.viewSubscribers:
        return 'عرض المشتركين';
      case PermissionType.addSubscribers:
        return 'إضافة مشتركين';
      case PermissionType.editSubscribers:
        return 'تعديل المشتركين';
      case PermissionType.deleteSubscribers:
        return 'حذف المشتركين';
      case PermissionType.viewTransactions:
        return 'عرض المعاملات';
      case PermissionType.addTransactions:
        return 'إضافة معاملات';
      case PermissionType.editTransactions:
        return 'تعديل المعاملات';
      case PermissionType.deleteTransactions:
        return 'حذف المعاملات';
      case PermissionType.renewSubscriptions:
        return 'تجديد الاشتراكات';
      case PermissionType.payDebts:
        return 'تسديد الديون';
      case PermissionType.viewDevices:
        return 'عرض الأجهزة';
      case PermissionType.manageDevices:
        return 'إدارة الأجهزة';
      case PermissionType.viewReports:
        return 'عرض التقارير';
      case PermissionType.exportData:
        return 'تصدير البيانات';
      case PermissionType.createBackup:
        return 'إنشاء نسخة احتياطية';
      case PermissionType.restoreBackup:
        return 'استعادة نسخة احتياطية';
      case PermissionType.manageEmployees:
        return 'إدارة الموظفين';
      case PermissionType.viewEmployeeReports:
        return 'عرض تقارير الموظفين';
    }
  }

  Future<void> _addEmployee() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // التحقق من وجود صلاحيات للموظف
    if (_selectedRole == UserRole.employee &&
        !_selectedPermissions.values.any((hasPermission) => hasPermission)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صلاحية واحدة على الأقل للموظف'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // إعداد الصلاحيات
      Map<PermissionType, bool> finalPermissions;
      if (_selectedRole == UserRole.manager) {
        finalPermissions = {};
        for (final permission in PermissionType.values) {
          finalPermissions[permission] = true;
        }
      } else {
        finalPermissions = _selectedPermissions;
      }

      final result = await EmployeeService.createEmployee(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        password: _passwordController.text,
        role: _selectedRole,
        permissions: finalPermissions,
        phoneNumber: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
      );

      if (result['success'] != true) {
        throw Exception(result['message']);
      }

      if (mounted) {
        Navigator.of(context).pop();
        widget.onEmployeeAdded();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الموظف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // بناء محدد نوع المستخدم
  Widget _buildUserRoleSelector() {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع المستخدم',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            // خيار المدير
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: _selectedRole == UserRole.manager
                        ? colorScheme.primary
                        : colorScheme.outline.withValues(alpha: 0.3),
                    width: _selectedRole == UserRole.manager ? 2 : 1,
                  ),
                  color: _selectedRole == UserRole.manager
                      ? colorScheme.primary.withValues(alpha: 0.1)
                      : colorScheme.surface,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: () {
                      setState(() {
                        _selectedRole = UserRole.manager;
                        // إذا كان مدير، أعطه جميع الصلاحيات
                        _selectedPermissions = {};
                        for (final permission in PermissionType.values) {
                          _selectedPermissions[permission] = true;
                        }
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: _selectedRole == UserRole.manager
                                  ? colorScheme.primary
                                  : colorScheme.outline.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.admin_panel_settings_rounded,
                              color: _selectedRole == UserRole.manager
                                  ? colorScheme.onPrimary
                                  : colorScheme.onSurface.withValues(
                                      alpha: 0.6,
                                    ),
                              size: 24,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'مدير',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: _selectedRole == UserRole.manager
                                  ? colorScheme.primary
                                  : colorScheme.onSurface,
                            ),
                          ),
                          Text(
                            'جميع الصلاحيات',
                            style: TextStyle(
                              fontSize: 12,
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // خيار الموظف
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: _selectedRole == UserRole.employee
                        ? colorScheme.primary
                        : colorScheme.outline.withValues(alpha: 0.3),
                    width: _selectedRole == UserRole.employee ? 2 : 1,
                  ),
                  color: _selectedRole == UserRole.employee
                      ? colorScheme.primary.withValues(alpha: 0.1)
                      : colorScheme.surface,
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(16),
                    onTap: () {
                      setState(() {
                        _selectedRole = UserRole.employee;
                        _selectedPermissions = {};
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: _selectedRole == UserRole.employee
                                  ? colorScheme.primary
                                  : colorScheme.outline.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.person_rounded,
                              color: _selectedRole == UserRole.employee
                                  ? colorScheme.onPrimary
                                  : colorScheme.onSurface.withValues(
                                      alpha: 0.6,
                                    ),
                              size: 24,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'موظف',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: _selectedRole == UserRole.employee
                                  ? colorScheme.primary
                                  : colorScheme.onSurface,
                            ),
                          ),
                          Text(
                            'صلاحيات محددة',
                            style: TextStyle(
                              fontSize: 12,
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.6,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // بناء قسم الصلاحيات
  Widget _buildPermissionsSection() {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.security_rounded,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'تحديد الصلاحيات',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildPermissionsGrid(),
        ],
      ),
    );
  }

  // بناء حقل إدخال عصري
  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool isRequired = false,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    Widget? suffixIcon,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label مع علامة الإجبارية
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: TextStyle(
                  color: colorScheme.error,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),

        // حقل الإدخال
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextFormField(
            controller: controller,
            obscureText: obscureText,
            keyboardType: keyboardType,
            validator: validator,
            style: TextStyle(fontSize: 16, color: colorScheme.onSurface),
            decoration: InputDecoration(
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: colorScheme.primary, size: 20),
              ),
              suffixIcon: suffixIcon,
              filled: true,
              fillColor: isDark
                  ? colorScheme.surface.withValues(alpha: 0.8)
                  : colorScheme.surface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide.none,
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: colorScheme.primary, width: 2),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: colorScheme.error, width: 1),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
                borderSide: BorderSide(color: colorScheme.error, width: 2),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
