import 'dart:typed_data';
import 'package:crypto/crypto.dart';

Digest md5convert(Uint8List data) => md5.convert(data);

String md5ToHex(Digest digest) =>
    digest.bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();

Uint8List hexToBytes(String hex) {
  final result = Uint8List(hex.length ~/ 2);
  for (int i = 0; i < hex.length; i += 2) {
    result[i ~/ 2] = int.parse(hex.substring(i, i + 2), radix: 16);
  }
  return result;
}
