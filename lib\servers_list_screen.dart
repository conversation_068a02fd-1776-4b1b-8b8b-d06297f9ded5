import 'package:flutter/material.dart';
import 'package:itower/db_helper.dart';
import 'add_server_screen.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:async';
import 'package:crypto/crypto.dart';
import 'package:itower/core/services/mikrotik_service.dart';
import 'package:dartssh2/dartssh2.dart';
import 'package:itower/features/main_home_screen.dart';
import 'package:itower/main.dart'; // لاستيراد anyDeviceConnectedNotifier

class ServersListScreen extends StatefulWidget {
  const ServersListScreen({super.key});
  @override
  State<ServersListScreen> createState() => _ServersListScreenState();
}

class _ServersListScreenState extends State<ServersListScreen> {
  List<Map<String, dynamic>> servers = [];
  late final MikrotikService mikrotikService = MikrotikService();
  final Map<int, SSHClient> activeClients = {};
  final Map<int, bool> isActuallyConnected = {};

  @override
  void initState() {
    super.initState();
    DBHelper.instance.createServersTableIfNotExists().then((_) async {
      await _loadServers();
      // إعادة الاتصال التلقائي إذا كان السيرفر متصل في قاعدة البيانات
      for (int i = 0; i < servers.length; i++) {
        if (servers[i]['connected'] == true) {
          await _tryReconnectOrReset(i);
        }
      }
    });
  }

  // محاولة إعادة الاتصال أو تحديث قاعدة البيانات إذا فشل
  Future<void> _tryReconnectOrReset(int i) async {
    final server = servers[i];
    try {
      final socket = await SSHSocket.connect(
        server['ip'],
        22,
      ).timeout(const Duration(seconds: 7));
      final client = SSHClient(
        socket,
        username: server['user'],
        onPasswordRequest: () => server['pass'],
      );
      final session = await client.execute('system identity print');
      await session.stdout
          .transform(
            StreamTransformer<Uint8List, String>.fromHandlers(
              handleData: (data, sink) => sink.add(utf8.decode(data)),
            ),
          )
          .join();
      activeClients[i] = client;
      isActuallyConnected[i] = true;
      // مراقبة فقدان الاتصال
      socket.done.catchError((_) {
        _handleConnectionLost(i);
      });
      // إذا نجح الاتصال، حدث قاعدة البيانات
      final updatedServer = Map<String, dynamic>.from(server);
      updatedServer['connected'] = true;
      final id = updatedServer['id'];
      await DBHelper.instance.updateServer(id, updatedServer);
      await _loadServers();
      if (!mounted) return;
      // إشعار الاتصال
      ServerStatusChangedNotification().dispatch(context);
    } catch (e) {
      // إذا فشل الاتصال، حدث قاعدة البيانات فقط
      final updatedServer = Map<String, dynamic>.from(server);
      updatedServer['connected'] = false;
      final id = updatedServer['id'];
      await DBHelper.instance.updateServer(id, updatedServer);
      await _loadServers();
      if (!mounted) return;
      // إشعار قطع الاتصال
      ServerStatusChangedNotification().dispatch(context);
    }
  }

  Future<void> _loadServers() async {
    final list = await DBHelper.instance.getAllServers();
    if (!mounted) return;
    setState(() {
      servers = list;
      print('DEBUG: تم تحديث قائمة السيرفرات (${servers.length}) عنصر.');
    });
  }

  Future<void> _addServer(dynamic server) async {
    final Map<String, dynamic> srv = Map<String, dynamic>.from(server);
    srv['connected'] = 0;
    await DBHelper.instance.insertServer(srv);
    await _loadServers();
  }

  Future<void> _updateServer(int i, Map<String, dynamic> server) async {
    final id = servers[i]['id'];
    await DBHelper.instance.updateServer(id, server);
    await _loadServers();
  }

  // منطق الاتصال الفعلي مع مراقبة فقدان الاتصال
  Future<void> _connectToServer(int i, {bool showSnackbar = true}) async {
    print('DEBUG: تم الضغط على زر الاتصال للسيرفر رقم $i');
    final server = servers[i];
    bool connected = false;
    String errorMsg = '';
    try {
      final socket = await SSHSocket.connect(server['ip'], 22);
      final client = SSHClient(
        socket,
        username: server['user'],
        onPasswordRequest: () => server['pass'],
      );
      final session = await client.execute('system identity print');
      await session.stdout
          .transform(
            StreamTransformer<Uint8List, String>.fromHandlers(
              handleData: (data, sink) => sink.add(utf8.decode(data)),
            ),
          )
          .join();
      activeClients[i] = client;
      isActuallyConnected[i] = true;
      connected = true;
      // مراقبة فقدان الاتصال
      socket.done.catchError((_) {
        _handleConnectionLost(i);
      });
    } catch (e) {
      errorMsg = e.toString();
      isActuallyConnected[i] = false;
      connected = false;
      // إزالة الجلسة من Mikrotik عند فشل الاتصال
      try {
        await mikrotikService.removeActiveSession(
          server['ip'],
          server['user'],
          server['pass'],
          server['user'],
        );
      } catch (_) {}
    }
    final updatedServer = Map<String, dynamic>.from(server);
    updatedServer['connected'] = connected;
    final id = updatedServer['id'];
    await DBHelper.instance.updateServer(id, updatedServer);
    await _loadServers();
    if (!mounted) return;
    // إشعار الاتصال
    ServerStatusChangedNotification().dispatch(context);
    print('DEBUG: نهاية دالة الاتصال، الحالة: $connected');
    if (showSnackbar && mounted) {
      if (connected) {
        anyDeviceConnectedNotifier.value = true;
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            connected
                ? 'تم الاتصال بنجاح (الاتصال سيبقى مفتوحًا حتى تضغط قطع)'
                : 'فشل الاتصال: $errorMsg',
          ),
        ),
      );
    }
  }

  // معالجة فقد الاتصال: تحديث قاعدة البيانات وإزالة الجلسة من Mikrotik
  void _handleConnectionLost(int i) async {
    isActuallyConnected[i] = false;
    final server = servers[i];
    final updatedServer = Map<String, dynamic>.from(server);
    updatedServer['connected'] = false;
    final id = updatedServer['id'];
    await DBHelper.instance.updateServer(id, updatedServer);
    await _loadServers();
    // إزالة الجلسة من Mikrotik عند فقد الاتصال
    try {
      await mikrotikService.removeActiveSession(
        server['ip'],
        server['user'],
        server['pass'],
        server['user'],
      );
    } catch (_) {}
    if (!mounted) return;
    // إرسال الإشعار إلى الـ Navigator الأعلى لضمان وصوله حتى لو لم يكن MainHomeScreen ظاهرًا
    ServerStatusChangedNotification().dispatch(
      Navigator.of(context, rootNavigator: true).context,
    );
    if (mounted) {
      anyDeviceConnectedNotifier.value = false;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم قطع الاتصال مع السيرفر')),
      );
    }
  }

  // عند قطع الاتصال يدويًا
  Future<void> _disconnectServer(int i) async {
    if (activeClients.containsKey(i)) {
      activeClients[i]?.close();
      activeClients.remove(i);
    }
    final server = servers[i];
    final updatedServer = Map<String, dynamic>.from(server);
    updatedServer['connected'] = false;
    final id = updatedServer['id'];
    await DBHelper.instance.updateServer(id, updatedServer);
    await _loadServers();
    // إزالة الجلسة من Mikrotik
    try {
      await mikrotikService.removeActiveSession(
        server['ip'],
        server['user'],
        server['pass'],
        server['user'],
      );
    } catch (_) {}
    if (!mounted) return;
    // إشعار قطع الاتصال
    ServerStatusChangedNotification().dispatch(context);
    // تحديث التدرج اللوني بناءً على حالة جميع السيرفرات
    final anyConnected = servers.any(
      (s) =>
          s['connected'] == true ||
          s['connected'] == 1 ||
          s['connected'] == '1',
    );
    anyDeviceConnectedNotifier.value = anyConnected;
    // إظهار رسالة القطع دائمًا بعد التأكد من أن الشاشة ما زالت موجودة
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم قطع الاتصال مع السيرفر')),
        );
      }
    });
  }

  // عند حذف السيرفر
  Future<void> _removeServer(int i) async {
    // إذا كان هناك اتصال فعلي، أغلقه أولاً
    if (activeClients.containsKey(i)) {
      activeClients[i]?.close();
      activeClients.remove(i);
    }
    final server = servers[i];
    // إزالة الجلسة من Mikrotik عند الحذف
    try {
      await mikrotikService.removeActiveSession(
        server['ip'],
        server['user'],
        server['pass'],
        server['user'],
      );
    } catch (_) {}
    final id = server['id'];
    await DBHelper.instance.deleteServer(id);
    await _loadServers();
  }

  Digest md5convert(Uint8List data) {
    return md5.convert(data);
  }

  String md5ToHex(Digest digest) {
    return digest.bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
  }

  Future<void> _refreshServers() async {
    await _loadServers();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return NotificationListener<ServerStatusChangedNotification>(
      onNotification: (notification) {
        _loadServers(); // تحديث القائمة تلقائيًا عند استقبال الإشعار
        return true;
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('السيرفرات'),
          centerTitle: true,
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _refreshServers,
              tooltip: 'تحديث القائمة',
            ),
          ],
        ),
        body: servers.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'لا يوجد سيرفر مضاف',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 24),
                    ElevatedButton.icon(
                      onPressed: () async {
                        final server = await Navigator.of(context).push(
                          MaterialPageRoute(builder: (_) => AddServerScreen()),
                        );
                        if (server != null) await _addServer(server);
                        await _loadServers();
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('إضافة سيرفر جديد'),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: servers.length,
                itemBuilder: (context, i) {
                  final server = servers[i];
                  final isConnected =
                      server['connected'] == true ||
                      server['connected'] == 1 ||
                      server['connected'] == '1';
                  final colorScheme = Theme.of(context).colorScheme;
                  final connectedColor = colorScheme.secondary;
                  final disconnectedColor = colorScheme.error;
                  return Container(
                    margin: const EdgeInsets.only(bottom: 24),
                    decoration: BoxDecoration(
                      color: isConnected
                          ? connectedColor.withOpacity(0.08)
                          : disconnectedColor.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(18),
                      border: Border.all(
                        color: isConnected ? connectedColor : disconnectedColor,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color:
                              (isConnected ? connectedColor : disconnectedColor)
                                  .withOpacity(0.08),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              // Container الدائرة
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: isConnected
                                      ? connectedColor
                                      : disconnectedColor,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                server['name'] ?? '',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.onSurface,
                                ),
                              ),
                              const Spacer(),
                              // Container نص متصل/غير متصل
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: isConnected
                                      ? Colors.green.withOpacity(0.15)
                                      : Colors.red.withOpacity(0.15),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  isConnected ? 'متصل' : 'غير متصل',
                                  style: TextStyle(
                                    color: isConnected
                                        ? Colors.green
                                        : Colors.red,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              isConnected
                                  ? _actionButton(
                                      'قطع',
                                      Icons.link_off,
                                      colorScheme.error,
                                      () async {
                                        await _disconnectServer(i);
                                      },
                                    )
                                  : _actionButton(
                                      'اتصال',
                                      Icons.link,
                                      colorScheme.secondary,
                                      () async {
                                        await _connectToServer(i);
                                      },
                                    ),
                              _actionButton(
                                'مزامنة',
                                Icons.sync,
                                colorScheme.primary,
                                () async {
                                  showDialog(
                                    context: context,
                                    barrierDismissible: false,
                                    builder: (ctx) => const Center(
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                  List<Map<String, String>> users = [];
                                  String errorMsg = '';
                                  try {
                                    SSHClient? client = activeClients[i];
                                    SSHSocket? socket;
                                    if (client == null) {
                                      socket = await SSHSocket.connect(
                                        server['ip'],
                                        22,
                                      );
                                      client = SSHClient(
                                        socket,
                                        username: server['user'],
                                        onPasswordRequest: () => server['pass'],
                                      );
                                      activeClients[i] = client;
                                    }
                                    final session = await client.execute(
                                      '/ppp/active/print terse',
                                    );
                                    final output = await session.stdout
                                        .transform(
                                          StreamTransformer<
                                            Uint8List,
                                            String
                                          >.fromHandlers(
                                            handleData: (data, sink) =>
                                                sink.add(utf8.decode(data)),
                                          ),
                                        )
                                        .join();
                                    final lines = output
                                        .split('\n')
                                        .where((l) => l.trim().isNotEmpty)
                                        .toList();
                                    for (final line in lines) {
                                      final fields = line.split(' ');
                                      final user = <String, String>{};
                                      for (final field in fields) {
                                        final kv = field.split('=');
                                        if (kv.length == 2) {
                                          user[kv[0]] = kv[1];
                                        }
                                      }
                                      if (user.isNotEmpty) {
                                        users.add({
                                          'name': user['name'] ?? '',
                                          'address': user['address'] ?? '',
                                          'uptime': user['uptime'] ?? '',
                                        });
                                      }
                                    }
                                  } catch (e) {
                                    errorMsg = e.toString();
                                  }
                                  Navigator.of(context).pop();
                                  if (errorMsg.isNotEmpty) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'فشل المزامنة: $errorMsg',
                                        ),
                                      ),
                                    );
                                  } else {
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (_) => PppActiveScreen(
                                          users: users,
                                          ip: server['ip'],
                                          user: server['user'],
                                          pass: server['pass'],
                                          client: activeClients[i],
                                        ),
                                      ),
                                    );
                                  }
                                },
                              ),
                              _actionButton(
                                'تعديل',
                                Icons.edit,
                                colorScheme.tertiary,
                                () async {
                                  final edited = await Navigator.of(context)
                                      .push(
                                        MaterialPageRoute(
                                          builder: (_) => AddServerScreen(
                                            initialServer: server,
                                          ),
                                        ),
                                      );
                                  if (edited != null) {
                                    await _updateServer(i, edited);
                                    await _loadServers();
                                  }
                                },
                              ),
                              _actionButton(
                                'حذف',
                                Icons.delete,
                                colorScheme.error,
                                () async {
                                  showDialog(
                                    context: context,
                                    builder: (ctx) => AlertDialog(
                                      title: const Text('تأكيد الحذف'),
                                      content: const Text(
                                        'هل أنت متأكد من حذف هذا السيرفر؟',
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed: () =>
                                              Navigator.of(ctx).pop(),
                                          child: const Text('إلغاء'),
                                        ),
                                        TextButton(
                                          onPressed: () async {
                                            await _removeServer(i);
                                            Navigator.of(ctx).pop();
                                          },
                                          child: Text(
                                            'حذف',
                                            style: TextStyle(
                                              color: colorScheme.error,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
        floatingActionButton: servers.isEmpty
            ? null
            : FloatingActionButton.extended(
                onPressed: () async {
                  final server = await Navigator.of(
                    context,
                  ).push(MaterialPageRoute(builder: (_) => AddServerScreen()));
                  if (server != null) await _addServer(server);
                  await _loadServers();
                },
                icon: const Icon(Icons.add),
                label: const Text('إضافة سيرفر جديد'),
              ),
      ),
    );
  }

  Widget _actionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return ElevatedButton.icon(
      onPressed: onTap,
      icon: Icon(icon, size: 18, color: colorScheme.onPrimary),
      label: Text(
        label,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: colorScheme.onPrimary,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: colorScheme.onPrimary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        elevation: 0,
        textStyle: const TextStyle(fontSize: 14),
      ),
    );
  }
}

class PppActiveScreen extends StatefulWidget {
  final List<Map<String, String>> users;
  final String ip;
  final String user;
  final String pass;
  final SSHClient? client;
  const PppActiveScreen({
    super.key,
    required this.users,
    required this.ip,
    required this.user,
    required this.pass,
    this.client,
  });

  @override
  State<PppActiveScreen> createState() => _PppActiveScreenState();
}

class _PppActiveScreenState extends State<PppActiveScreen> {
  List<Map<String, String>> users = [];
  bool loading = false;
  Timer? timer;
  String errorMsg = '';
  SSHClient? _client;

  @override
  void initState() {
    super.initState();
    users = widget.users;
    _client = widget.client;
    _fetchActiveUsers();
    timer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _fetchActiveUsers(),
    );
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  Future<void> _fetchActiveUsers() async {
    if (!mounted) return;
    setState(() {
      loading = true;
      errorMsg = '';
    });
    try {
      SSHClient? client = _client;
      SSHSocket? socket;
      if (client == null) {
        socket = await SSHSocket.connect(widget.ip, 22);
        client = SSHClient(
          socket,
          username: widget.user,
          onPasswordRequest: () => widget.pass,
        );
        _client = client;
      }
      final session = await client.execute('/ppp/active/print terse');
      final output = await session.stdout
          .transform(
            StreamTransformer<Uint8List, String>.fromHandlers(
              handleData: (data, sink) => sink.add(utf8.decode(data)),
            ),
          )
          .join();
      final lines = output
          .split('\n')
          .where((l) => l.trim().isNotEmpty)
          .toList();
      final newUsers = <Map<String, String>>[];
      for (final line in lines) {
        final fields = line.split(' ');
        final user = <String, String>{};
        for (final field in fields) {
          final kv = field.split('=');
          if (kv.length == 2) {
            user[kv[0]] = kv[1];
          }
        }
        if (user.isNotEmpty) {
          newUsers.add({
            'name': user['name'] ?? '',
            'address': user['address'] ?? '',
            'uptime': user['uptime'] ?? '',
          });
        }
      }
      if (mounted) {
        setState(() {
          users = newUsers;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          errorMsg = e.toString();
        });
      }
    }
    if (mounted) {
      setState(() {
        loading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المستخدمين النشطين'),
        centerTitle: true,
      ),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : errorMsg.isNotEmpty
          ? Center(
              child: Text(
                'خطأ: $errorMsg',
                style: const TextStyle(color: Colors.red),
              ),
            )
          : users.isEmpty
          ? const Center(
              child: Text(
                'لا توجد بيانات مستخدمين نشطين',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: users.length,
              itemBuilder: (context, i) {
                final user = users[i];
                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'اسم المستخدم: ${user['name']}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'الـ IP: ${user['address']}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'مدة الاتصال: ${user['uptime']}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                );
              },
            ),
    );
  }
}
