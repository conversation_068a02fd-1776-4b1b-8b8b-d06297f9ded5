-- إصلاح trigger تحديث حالة المستخدم في Supabase
-- يجب تشغيل هذا السكريبت في Supabase SQL Editor

-- 1. حذف الـ trigger القديم
DROP TRIGGER IF EXISTS update_user_status_trigger ON user_accounts;

-- 2. حذ<PERSON> الدالة القديمة
DROP FUNCTION IF EXISTS update_user_status();

-- 3. إنشاء دالة محدثة لتحديث حالة المستخدم
CREATE OR REPLACE FUNCTION update_user_status()
RETURNS TRIGGER AS $$
BEGIN
  -- تحديث حالة المستخدم بناءً على انتهاء الاشتراك المدفوع فقط
  IF NEW.subscription_end IS NOT NULL AND NEW.subscription_end < NOW() THEN
    NEW.account_status = 'expired';
  END IF;

  -- عدم تغيير حالة الحساب التجريبي تلقائياً
  -- سيتم التحكم في انتهاء الفترة التجريبية من التطبيق

  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 4. إنشاء trigger جديد محسن
CREATE TRIGGER update_user_status_trigger
  BEFORE INSERT OR UPDATE ON user_accounts
  FOR EACH ROW EXECUTE FUNCTION update_user_status();

-- 5. إضافة عمود تاريخ انتهاء الفترة التجريبية إذا لم يكن موجوداً
ALTER TABLE user_accounts
ADD COLUMN IF NOT EXISTS trial_end_date TIMESTAMP WITH TIME ZONE;

-- 6. إصلاح الحسابات التجريبية الموجودة التي تم تحويلها خطأً إلى expired
UPDATE user_accounts
SET
  account_status = 'trial',
  trial_end_date = CASE
    WHEN trial_end_date IS NULL THEN created_at + INTERVAL '15 days'
    ELSE trial_end_date
  END,
  trial_days_remaining = CASE
    WHEN trial_end_date IS NULL THEN
      GREATEST(0, 15 - EXTRACT(DAY FROM (NOW() - created_at)))
    ELSE
      GREATEST(0, EXTRACT(DAY FROM (trial_end_date - NOW())))
  END,
  updated_at = NOW()
WHERE
  account_status = 'expired'
  AND subscription_type = 'trial'
  AND (trial_end_date IS NULL OR trial_end_date > NOW())
  AND created_at > NOW() - INTERVAL '15 days';

-- 6. إنشاء دالة لفحص انتهاء الفترة التجريبية يدوياً
CREATE OR REPLACE FUNCTION check_trial_expiry()
RETURNS INTEGER AS $$
DECLARE
  expired_count INTEGER;
BEGIN
  -- تحديث الحسابات التجريبية المنتهية فعلياً
  UPDATE user_accounts
  SET 
    account_status = 'expired',
    updated_at = NOW()
  WHERE 
    account_status = 'trial'
    AND subscription_type = 'trial'
    AND trial_days_remaining <= 0;
    
  GET DIAGNOSTICS expired_count = ROW_COUNT;
  
  RETURN expired_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. إنشاء دالة لحساب الأيام المتبقية بناءً على التاريخ
CREATE OR REPLACE FUNCTION calculate_trial_days_remaining(user_uuid UUID)
RETURNS INTEGER AS $$
DECLARE
  account_record RECORD;
  days_remaining INTEGER;
BEGIN
  -- جلب بيانات الحساب
  SELECT created_at, trial_days_remaining 
  INTO account_record
  FROM user_accounts 
  WHERE user_id = user_uuid;
  
  IF NOT FOUND THEN
    RETURN 0;
  END IF;
  
  -- حساب الأيام المتبقية بناءً على تاريخ الإنشاء
  days_remaining := 15 - EXTRACT(DAY FROM (NOW() - account_record.created_at));
  
  -- التأكد من أن القيمة لا تقل عن 0
  IF days_remaining < 0 THEN
    days_remaining := 0;
  END IF;
  
  RETURN days_remaining;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. إنشاء دالة لتحديث الأيام المتبقية لجميع الحسابات التجريبية
CREATE OR REPLACE FUNCTION update_all_trial_days()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER;
BEGIN
  -- تحديث الأيام المتبقية لجميع الحسابات التجريبية
  UPDATE user_accounts
  SET 
    trial_days_remaining = calculate_trial_days_remaining(user_id),
    updated_at = NOW()
  WHERE 
    account_status = 'trial' 
    AND subscription_type = 'trial';
    
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  
  -- تحديث الحسابات المنتهية
  UPDATE user_accounts
  SET 
    account_status = 'expired',
    updated_at = NOW()
  WHERE 
    account_status = 'trial'
    AND subscription_type = 'trial'
    AND trial_days_remaining <= 0;
  
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. منح الصلاحيات للدوال الجديدة
GRANT EXECUTE ON FUNCTION check_trial_expiry TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_trial_days_remaining TO authenticated;
GRANT EXECUTE ON FUNCTION update_all_trial_days TO authenticated;

-- 10. تشغيل التحديث الأولي
SELECT update_all_trial_days() as updated_accounts;

-- 11. عرض النتائج
SELECT 
  account_status,
  subscription_type,
  COUNT(*) as count,
  AVG(trial_days_remaining) as avg_days_remaining
FROM user_accounts 
WHERE subscription_type = 'trial'
GROUP BY account_status, subscription_type;

-- تأكيد إنجاز الإصلاح
SELECT 'تم إصلاح trigger حالة المستخدم بنجاح!' as status;
