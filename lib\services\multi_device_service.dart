import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// خدمة إدارة الأجهزة المتعددة للحساب الواحد - محدثة للنظام الجديد
class MultiDeviceService {
  static const String _deviceIdKey = 'permanent_device_id';
  static const String _primaryDeviceKey = 'is_primary_device';

  /// الحصول على معرف الجهاز الثابت
  static Future<String> getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? savedDeviceId = prefs.getString(_deviceIdKey);

      if (savedDeviceId != null && savedDeviceId.isNotEmpty) {
        debugPrint('🔍 [DEVICE] استخدام المعرف المحفوظ: $savedDeviceId');
        return savedDeviceId;
      }

      // إنشاء معرف جديد
      final deviceInfo = DeviceInfoPlugin();
      String deviceId = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceId = 'android_${androidInfo.fingerprint}_${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceId = 'ios_${iosInfo.identifierForVendor ?? 'unknown'}';
      } else {
        deviceId = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
      }

      await prefs.setString(_deviceIdKey, deviceId);
      debugPrint('✅ [DEVICE] تم إنشاء معرف جديد: $deviceId');
      return deviceId;
    } catch (e) {
      debugPrint('❌ [DEVICE] خطأ في الحصول على معرف الجهاز: $e');
      return 'error_device_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// فحص إذا كان الجهاز مرتبط بحساب
  static Future<Map<String, dynamic>?> getLinkedAccount(String deviceId) async {
    try {
      debugPrint('🔍 [MULTI_DEVICE] فحص ربط الجهاز: $deviceId');

      final response = await Supabase.instance.client
          .from('user_devices')
          .select('''
            user_id,
            device_name,
            device_type,
            is_primary,
            last_active,
            user_accounts!inner(
              email,
              display_name,
              is_trial,
              expiry_millis
            )
          ''')
          .eq('device_id', deviceId)
          .maybeSingle();

      if (response != null) {
        debugPrint(
          '✅ [MULTI_DEVICE] الجهاز مرتبط بحساب: ${response['user_id']}',
        );
        return response;
      }

      debugPrint('ℹ️ [MULTI_DEVICE] الجهاز غير مرتبط بأي حساب');
      return null;
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE] خطأ في فحص ربط الجهاز: $e');
      return null;
    }
  }

  /// ربط جهاز جديد بحساب موجود
  static Future<bool> linkDeviceToAccount(
    String userId,
    String deviceId, {
    String? deviceName,
    bool isPrimary = false,
  }) async {
    try {
      debugPrint('🔗 [MULTI_DEVICE] ربط جهاز بالحساب: $userId');

      // تجربة إنشاء الجدول أولاً إذا لم يكن موجوداً
      await _ensureUserDevicesTableExists();

      // فحص إذا كان الجهاز مرتبط بحساب آخر (مع معالجة الأخطاء)
      final existingLink = await _safeGetLinkedAccount(deviceId);
      if (existingLink != null && existingLink['user_id'] != userId) {
        throw Exception('الجهاز مرتبط بحساب آخر');
      }

      // إذا كان الجهاز مرتبط بنفس الحساب، حدث البيانات فقط
      if (existingLink != null && existingLink['user_id'] == userId) {
        await _updateDeviceActivity(deviceId);
        return true;
      }

      // ربط جهاز جديد - استخدام البيانات الأساسية فقط
      // ✅ بيانات الجهاز متوافقة مع بنية Supabase الفعلية
      final deviceData = {
        'user_id': userId,
        'device_id': deviceId,
        'device_name': deviceName ?? await _getDeviceName(),
        'device_type': Platform.isAndroid
            ? 'android'
            : (Platform.isIOS ? 'ios' : 'web'), // ✅ تغيير من desktop إلى web
        'device_status': 'active',
        'first_login': DateTime.now().toIso8601String(),
        'last_used': DateTime.now().toIso8601String(),
        'total_sessions': 1,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await Supabase.instance.client.from('user_devices').insert(deviceData);

      debugPrint('✅ [MULTI_DEVICE] تم ربط الجهاز بنجاح');

      // حفظ معلومات الجهاز محلياً
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_primaryDeviceKey, isPrimary);

      return true;
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE] خطأ في ربط الجهاز: $e');
      return false;
    }
  }

  /// إلغاء ربط جهاز من حساب
  static Future<bool> unlinkDevice(String deviceId) async {
    try {
      debugPrint('🔓 [MULTI_DEVICE] إلغاء ربط الجهاز: $deviceId');

      await Supabase.instance.client
          .from('user_devices')
          .delete()
          .eq('device_id', deviceId);

      // مسح البيانات المحلية
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_primaryDeviceKey);

      debugPrint('✅ [MULTI_DEVICE] تم إلغاء ربط الجهاز');
      return true;
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE] خطأ في إلغاء ربط الجهاز: $e');
      return false;
    }
  }

  /// جلب جميع الأجهزة المرتبطة بحساب
  static Future<List<Map<String, dynamic>>> getLinkedDevices(
    String userId,
  ) async {
    try {
      debugPrint('📱 [MULTI_DEVICE] جلب الأجهزة المرتبطة للمستخدم: $userId');

      final response = await Supabase.instance.client
          .from('user_devices')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      debugPrint('✅ [MULTI_DEVICE] تم جلب ${response.length} جهاز');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE] خطأ في جلب الأجهزة: $e');
      return [];
    }
  }

  /// تحديث نشاط الجهاز
  static Future<void> _updateDeviceActivity(String deviceId) async {
    try {
      await Supabase.instance.client
          .from('user_devices')
          .update({'last_active': DateTime.now().toIso8601String()})
          .eq('device_id', deviceId);
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE] خطأ في تحديث نشاط الجهاز: $e');
    }
  }

  /// الحصول على اسم الجهاز
  static Future<String> _getDeviceName() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return '${iosInfo.name} (${iosInfo.model})';
      }

      return 'جهاز غير معروف';
    } catch (e) {
      return 'جهاز غير معروف';
    }
  }

  /// فحص إذا كان الجهاز الحالي هو الجهاز الأساسي
  static Future<bool> isPrimaryDevice() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_primaryDeviceKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// تعيين الجهاز كجهاز أساسي
  static Future<bool> setPrimaryDevice(String userId, String deviceId) async {
    try {
      // إزالة الجهاز الأساسي السابق
      await Supabase.instance.client
          .from('user_devices')
          .update({'is_primary': false})
          .eq('user_id', userId);

      // تعيين الجهاز الحالي كأساسي
      await Supabase.instance.client
          .from('user_devices')
          .update({'is_primary': true})
          .eq('device_id', deviceId);

      // حفظ محلياً
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_primaryDeviceKey, true);

      debugPrint('✅ [MULTI_DEVICE] تم تعيين الجهاز كأساسي');
      return true;
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE] خطأ في تعيين الجهاز الأساسي: $e');
      return false;
    }
  }

  /// ربط جهاز بالحساب وفقاً للنظام الجديد (محدث لبنية Supabase)
  static Future<bool> linkDeviceV2(
    String userId,
    String deviceId, {
    String? deviceName,
    String? deviceType,
    bool isPrimary = false,
  }) async {
    try {
      debugPrint(
        '🔗 [MULTI_DEVICE_V2] ربط الجهاز: $deviceId بالمستخدم: $userId',
      );

      // التحقق من عدد الأجهزة المسموح
      final userAccount = await Supabase.instance.client
          .from('user_accounts')
          .select('max_devices')
          .eq('user_id', userId)
          .single();

      final maxDevices = userAccount['max_devices'] as int;

      // عد الأجهزة النشطة الحالية
      final currentDevices = await Supabase.instance.client
          .from('user_devices')
          .select('id')
          .eq('user_id', userId)
          .eq('device_status', 'active');

      if (currentDevices.length >= maxDevices) {
        // التحقق إذا كان الجهاز موجود مسبقاً
        final existingDevice = await Supabase.instance.client
            .from('user_devices')
            .select('*')
            .eq('user_id', userId)
            .eq('device_id', deviceId)
            .maybeSingle();

        if (existingDevice == null) {
          throw Exception(
            'تم الوصول للحد الأقصى من الأجهزة المسموحة ($maxDevices)',
          );
        }
      }

      // الحصول على معلومات الجهاز
      final deviceInfo = await _getDeviceInfo();

      // بيانات الجهاز
      final deviceData = {
        'user_id': userId,
        'device_id': deviceId,
        'device_name': deviceName ?? deviceInfo['device_name'],
        'device_type': deviceType ?? deviceInfo['device_type'],
        'device_fingerprint': deviceInfo['device_fingerprint'],
        'device_status': 'active',
        'first_login': DateTime.now().toIso8601String(),
        'last_used': DateTime.now().toIso8601String(),
        'ip_address': null, // يمكن إضافة منطق للحصول على IP
        'user_agent': deviceInfo['user_agent'],
        'location_info': null,
        'total_sessions': 1,
        'total_usage_time': '0 seconds',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      // إدراج أو تحديث الجهاز
      await Supabase.instance.client
          .from('user_devices')
          .upsert(deviceData, onConflict: 'user_id,device_id');

      debugPrint('✅ [MULTI_DEVICE_V2] تم ربط الجهاز بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE_V2] خطأ في ربط الجهاز: $e');
      return false;
    }
  }

  /// تحديث حالة الجهاز
  static Future<bool> updateDeviceStatus(
    String userId,
    String deviceId,
    String newStatus,
  ) async {
    try {
      debugPrint(
        '🔄 [MULTI_DEVICE_V2] تحديث حالة الجهاز: $deviceId -> $newStatus',
      );

      await Supabase.instance.client
          .from('user_devices')
          .update({
            'device_status': newStatus,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('device_id', deviceId);

      debugPrint('✅ [MULTI_DEVICE_V2] تم تحديث حالة الجهاز بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE_V2] خطأ في تحديث حالة الجهاز: $e');
      return false;
    }
  }

  /// جلب أجهزة المستخدم وفقاً للنظام الجديد
  static Future<List<Map<String, dynamic>>> getUserDevicesV2(
    String userId,
  ) async {
    try {
      debugPrint('📱 [MULTI_DEVICE_V2] جلب أجهزة المستخدم: $userId');

      final response = await Supabase.instance.client
          .from('user_devices')
          .select('*')
          .eq('user_id', userId)
          .order('last_used', ascending: false);

      debugPrint('✅ [MULTI_DEVICE_V2] تم جلب ${response.length} جهاز');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ [MULTI_DEVICE_V2] خطأ في جلب الأجهزة: $e');
      return [];
    }
  }

  /// الحصول على معلومات الجهاز المفصلة
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'device_name': '${androidInfo.brand} ${androidInfo.model}',
          'device_type': 'android',
          'device_fingerprint': androidInfo.fingerprint,
          'user_agent':
              '${androidInfo.brand} ${androidInfo.model} Android ${androidInfo.version.release}',
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'device_name': '${iosInfo.name} ${iosInfo.model}',
          'device_type': 'ios',
          'device_fingerprint': iosInfo.identifierForVendor ?? 'unknown',
          'user_agent':
              '${iosInfo.name} ${iosInfo.model} iOS ${iosInfo.systemVersion}',
        };
      }

      return {
        'device_name': 'Unknown Device',
        'device_type': 'web', // تغيير من unknown إلى web للتوافق مع Supabase
        'device_fingerprint': 'unknown',
        'user_agent': 'Unknown Device',
      };
    } catch (e) {
      return {
        'device_name': 'Unknown Device',
        'device_type': 'web', // تغيير من unknown إلى web للتوافق مع Supabase
        'device_fingerprint': 'unknown',
        'user_agent': 'Unknown Device',
      };
    }
  }

  /// التأكد من وجود جدول user_devices
  static Future<void> _ensureUserDevicesTableExists() async {
    try {
      // محاولة بسيطة للتحقق من وجود الجدول
      await Supabase.instance.client.from('user_devices').select('id').limit(1);
    } catch (e) {
      debugPrint('ℹ️ [MULTI_DEVICE] جدول user_devices قد يحتاج إنشاء: $e');
    }
  }

  /// فحص ربط الجهاز مع معالجة الأخطاء
  static Future<Map<String, dynamic>?> _safeGetLinkedAccount(
    String deviceId,
  ) async {
    try {
      final response = await Supabase.instance.client
          .from('user_devices')
          .select('user_id, device_name, device_type, is_primary, last_seen')
          .eq('device_id', deviceId)
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('ℹ️ [MULTI_DEVICE] لا يمكن فحص ربط الجهاز: $e');
      return null;
    }
  }
}
