// شاشة إدارة الموظفين

import 'package:flutter/material.dart';
import '../services/employee_service.dart';
import '../models/employee_models.dart';

class EmployeeManagementScreen extends StatefulWidget {
  const EmployeeManagementScreen({super.key});

  @override
  State<EmployeeManagementScreen> createState() =>
      _EmployeeManagementScreenState();
}

class _EmployeeManagementScreenState extends State<EmployeeManagementScreen> {
  List<Employee> _employees = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEmployees();
  }

  Future<void> _loadEmployees() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final employees = await EmployeeService.getAllEmployees();
      setState(() {
        _employees = employees;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الموظفين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showAddEmployeeDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AddEmployeeDialog(
        onEmployeeAdded: () {
          _loadEmployees();
        },
      ),
    );
  }

  Future<void> _deactivateEmployee(Employee employee) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد إلغاء التفعيل'),
        content: Text('هل أنت متأكد من إلغاء تفعيل الموظف "${employee.name}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إلغاء التفعيل'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await EmployeeService.deactivateEmployee(employee.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message']),
            backgroundColor: result['success'] ? Colors.green : Colors.red,
          ),
        );

        if (result['success']) {
          _loadEmployees();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الموظفين'),
        backgroundColor: colorScheme.surfaceContainer,
        actions: [
          IconButton(
            onPressed: _showAddEmployeeDialog,
            icon: const Icon(Icons.person_add),
            tooltip: 'إضافة موظف جديد',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _employees.isEmpty
          ? _buildEmptyState(colorScheme)
          : _buildEmployeesList(colorScheme),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddEmployeeDialog,
        child: const Icon(Icons.person_add),
      ),
    );
  }

  Widget _buildEmptyState(ColorScheme colorScheme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد موظفين',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على الزر لإضافة موظف جديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddEmployeeDialog,
            icon: const Icon(Icons.person_add),
            label: const Text('إضافة موظف جديد'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeesList(ColorScheme colorScheme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _employees.length,
      itemBuilder: (context, index) {
        final employee = _employees[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: employee.isActive
                  ? colorScheme.primaryContainer
                  : colorScheme.errorContainer,
              child: Icon(
                Icons.person,
                color: employee.isActive
                    ? colorScheme.onPrimaryContainer
                    : colorScheme.onErrorContainer,
              ),
            ),
            title: Text(
              employee.name,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: employee.isActive
                    ? colorScheme.onSurface
                    : colorScheme.onSurfaceVariant,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(employee.email),
                const SizedBox(height: 4),
                Row(
                  children: [
                    _buildRoleChip(employee.role, colorScheme),
                    const SizedBox(width: 8),
                    if (!employee.isActive)
                      Chip(
                        label: const Text('غير نشط'),
                        backgroundColor: colorScheme.errorContainer,
                        labelStyle: TextStyle(
                          color: colorScheme.onErrorContainer,
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'permissions':
                    _showPermissionsDialog(employee);
                    break;
                  case 'deactivate':
                    _deactivateEmployee(employee);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'permissions',
                  child: ListTile(
                    leading: Icon(Icons.security),
                    title: Text('إدارة الصلاحيات'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                if (employee.isActive)
                  const PopupMenuItem(
                    value: 'deactivate',
                    child: ListTile(
                      leading: Icon(Icons.person_off, color: Colors.red),
                      title: Text(
                        'إلغاء التفعيل',
                        style: TextStyle(color: Colors.red),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRoleChip(UserRole role, ColorScheme colorScheme) {
    String roleText;
    Color backgroundColor;
    Color textColor;

    switch (role) {
      case UserRole.manager:
        roleText = 'مدير';
        backgroundColor = colorScheme.primary;
        textColor = colorScheme.onPrimary;
        break;
      case UserRole.employee:
        roleText = 'موظف';
        backgroundColor = colorScheme.secondary;
        textColor = colorScheme.onSecondary;
        break;
    }

    return Chip(
      label: Text(roleText),
      backgroundColor: backgroundColor,
      labelStyle: TextStyle(
        color: textColor,
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  void _showPermissionsDialog(Employee employee) {
    showDialog(
      context: context,
      builder: (context) => PermissionsDialog(
        employee: employee,
        onPermissionsUpdated: () {
          _loadEmployees();
        },
      ),
    );
  }
}

// حوار إضافة موظف جديد
class AddEmployeeDialog extends StatefulWidget {
  final VoidCallback onEmployeeAdded;

  const AddEmployeeDialog({super.key, required this.onEmployeeAdded});

  @override
  State<AddEmployeeDialog> createState() => _AddEmployeeDialogState();
}

class _AddEmployeeDialogState extends State<AddEmployeeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneController = TextEditingController();

  UserRole _selectedRole = UserRole.employee;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _createEmployee() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    // إنشاء صلاحيات افتراضية حسب الدور
    final permissions = _getDefaultPermissions(_selectedRole);

    final result = await EmployeeService.createEmployee(
      name: _nameController.text.trim(),
      email: _emailController.text.trim(),
      password: _passwordController.text,
      role: _selectedRole,
      permissions: permissions,
      phoneNumber: _phoneController.text.trim().isEmpty
          ? null
          : _phoneController.text.trim(),
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message']),
          backgroundColor: result['success'] ? Colors.green : Colors.red,
        ),
      );

      if (result['success']) {
        widget.onEmployeeAdded();
        Navigator.of(context).pop();
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  Map<PermissionType, bool> _getDefaultPermissions(UserRole role) {
    final permissions = <PermissionType, bool>{};

    // تعيين جميع الصلاحيات إلى false أولاً
    for (final permission in PermissionType.values) {
      permissions[permission] = false;
    }

    // تعيين الصلاحيات حسب الدور
    switch (role) {
      case UserRole.manager:
        // المدير له كل الصلاحيات
        for (final permission in PermissionType.values) {
          permissions[permission] = true;
        }
        break;
      case UserRole.employee:
        // الموظف له صلاحيات أساسية يمكن تخصيصها لاحقاً
        permissions[PermissionType.viewSubscribers] = true;
        permissions[PermissionType.addSubscribers] = true;
        permissions[PermissionType.viewTransactions] = true;
        permissions[PermissionType.addTransactions] = true;
        permissions[PermissionType.renewSubscriptions] = true;
        permissions[PermissionType.payDebts] = true;
        permissions[PermissionType.viewDevices] = true;
        break;
    }

    return permissions;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة موظف جديد'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الموظف',
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم الموظف';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  prefixIcon: Icon(Icons.email),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال البريد الإلكتروني';
                  }
                  if (!value.contains('@')) {
                    return 'يرجى إدخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور',
                  prefixIcon: Icon(Icons.lock),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _phoneController,
                keyboardType: TextInputType.phone,
                decoration: const InputDecoration(
                  labelText: 'رقم الهاتف (اختياري)',
                  prefixIcon: Icon(Icons.phone),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<UserRole>(
                value: _selectedRole,
                decoration: const InputDecoration(
                  labelText: 'الدور',
                  prefixIcon: Icon(Icons.work),
                ),
                items: [UserRole.employee].map((role) {
                  String roleText;
                  switch (role) {
                    case UserRole.manager:
                      roleText = 'مدير';
                      break;
                    case UserRole.employee:
                      roleText = 'موظف';
                      break;
                  }
                  return DropdownMenuItem(value: role, child: Text(roleText));
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedRole = value!;
                  });
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _createEmployee,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('إضافة'),
        ),
      ],
    );
  }
}

// حوار إدارة الصلاحيات
class PermissionsDialog extends StatefulWidget {
  final Employee employee;
  final VoidCallback onPermissionsUpdated;

  const PermissionsDialog({
    super.key,
    required this.employee,
    required this.onPermissionsUpdated,
  });

  @override
  State<PermissionsDialog> createState() => _PermissionsDialogState();
}

class _PermissionsDialogState extends State<PermissionsDialog> {
  late Map<PermissionType, bool> _permissions;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _permissions = Map.from(widget.employee.permissions);
  }

  Future<void> _updatePermissions() async {
    setState(() {
      _isLoading = true;
    });

    final result = await EmployeeService.updateEmployeePermissions(
      employeeId: widget.employee.id!,
      newPermissions: _permissions,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result['message']),
          backgroundColor: result['success'] ? Colors.green : Colors.red,
        ),
      );

      if (result['success']) {
        widget.onPermissionsUpdated();
        Navigator.of(context).pop();
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  String _getPermissionTitle(PermissionType permission) {
    switch (permission) {
      case PermissionType.viewSubscribers:
        return 'عرض المشتركين';
      case PermissionType.addSubscribers:
        return 'إضافة مشتركين';
      case PermissionType.editSubscribers:
        return 'تعديل المشتركين';
      case PermissionType.deleteSubscribers:
        return 'حذف المشتركين';
      case PermissionType.viewTransactions:
        return 'عرض المعاملات';
      case PermissionType.addTransactions:
        return 'إضافة معاملات';
      case PermissionType.editTransactions:
        return 'تعديل المعاملات';
      case PermissionType.deleteTransactions:
        return 'حذف المعاملات';
      case PermissionType.renewSubscriptions:
        return 'تجديد الاشتراكات';
      case PermissionType.payDebts:
        return 'تسديد الديون';
      case PermissionType.viewDevices:
        return 'عرض الأجهزة';
      case PermissionType.manageDevices:
        return 'إدارة الأجهزة';
      case PermissionType.viewReports:
        return 'عرض التقارير';
      case PermissionType.exportData:
        return 'تصدير البيانات';
      case PermissionType.createBackup:
        return 'إنشاء نسخ احتياطية';
      case PermissionType.restoreBackup:
        return 'استعادة النسخ';
      case PermissionType.manageEmployees:
        return 'إدارة الموظفين';
      case PermissionType.viewEmployeeReports:
        return 'عرض تقارير الموظفين';
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colorScheme.surface,
                colorScheme.surface.withValues(alpha: 0.95),
              ],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header مع تصميم عصري
              _buildModernHeader(colorScheme, isDark),

              // المحتوى الرئيسي
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // معلومات الموظف
                      _buildEmployeeInfo(colorScheme),
                      const SizedBox(height: 20),

                      // الصلاحيات
                      if (widget.employee.role == UserRole.employee)
                        _buildPermissionsSection(colorScheme)
                      else
                        _buildManagerPermissionsInfo(colorScheme),
                    ],
                  ),
                ),
              ),

              // أزرار الإجراءات
              _buildActionButtons(colorScheme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primary,
            colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          // أيقونة الصلاحيات
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.security_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // العنوان والوصف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'صلاحيات ${widget.employee.name}',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.employee.role == UserRole.manager
                      ? 'المدير له جميع الصلاحيات'
                      : 'تحديد الصلاحيات المتاحة للموظف',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // زر الإغلاق
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.close_rounded, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeInfo(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 24,
            backgroundColor: colorScheme.primary,
            child: Text(
              widget.employee.name.isNotEmpty
                  ? widget.employee.name[0].toUpperCase()
                  : 'م',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.employee.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                Text(
                  widget.employee.email,
                  style: TextStyle(
                    fontSize: 14,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: widget.employee.role == UserRole.manager
                  ? Colors.orange.withValues(alpha: 0.2)
                  : colorScheme.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              widget.employee.role == UserRole.manager ? 'مدير' : 'موظف',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: widget.employee.role == UserRole.manager
                    ? Colors.orange[700]
                    : colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagerPermissionsInfo(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.orange.withValues(alpha: 0.1),
            Colors.orange.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.admin_panel_settings_rounded,
            size: 48,
            color: Colors.orange[700],
          ),
          const SizedBox(height: 16),
          Text(
            'صلاحيات المدير',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.orange[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'المدير له جميع الصلاحيات في النظام ولا يمكن تعديلها',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsSection(ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الصلاحيات',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: colorScheme.outline.withValues(alpha: 0.3),
            ),
            boxShadow: [
              BoxShadow(
                color: colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children:
                [
                  // صلاحيات المشتركين
                  _buildModernPermissionSection(
                    'إدارة المشتركين',
                    Icons.people_rounded,
                    Colors.blue,
                    [
                      PermissionType.viewSubscribers,
                      PermissionType.addSubscribers,
                      PermissionType.editSubscribers,
                      PermissionType.deleteSubscribers,
                    ],
                    colorScheme,
                  ),
                  const SizedBox(height: 16),

                  // صلاحيات المعاملات
                  _buildModernPermissionSection(
                    'إدارة المعاملات',
                    Icons.payment_rounded,
                    Colors.green,
                    [
                      PermissionType.viewTransactions,
                      PermissionType.addTransactions,
                      PermissionType.editTransactions,
                      PermissionType.deleteTransactions,
                      PermissionType.renewSubscriptions,
                      PermissionType.payDebts,
                    ],
                    colorScheme,
                  ),
                  const SizedBox(height: 16),

                  // صلاحيات النظام
                  _buildModernPermissionSection(
                    'إدارة النظام',
                    Icons.settings_rounded,
                    Colors.orange,
                    [
                      PermissionType.viewReports,
                      PermissionType.manageDevices,
                      PermissionType.createBackup,
                      PermissionType.restoreBackup,
                    ],
                    colorScheme,
                  ),
                ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernPermissionSection(
    String title,
    IconData icon,
    Color color,
    List<PermissionType> permissions,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 20, color: color),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: permissions.map((permission) {
              final isSelected = _permissions[permission] == true;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _permissions[permission] = !isSelected;
                  });
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            colors: [color, color.withValues(alpha: 0.8)],
                          )
                        : null,
                    color: isSelected ? null : colorScheme.surface,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected ? color : color.withValues(alpha: 0.5),
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: isSelected
                        ? [
                            BoxShadow(
                              color: color.withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ]
                        : null,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isSelected
                            ? Icons.check_circle_rounded
                            : Icons.circle_outlined,
                        size: 16,
                        color: isSelected ? Colors.white : color,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        _getPermissionTitle(permission),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? Colors.white
                              : colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(color: colorScheme.outline),
              ),
              child: Text(
                'إلغاء',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
            ),
          ),
          if (widget.employee.role != UserRole.manager) ...[
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _updatePermissions,
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.save_rounded, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            'حفظ التعديلات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
