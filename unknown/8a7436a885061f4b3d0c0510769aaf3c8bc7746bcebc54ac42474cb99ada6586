import 'package:http/io_client.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:async';
import 'package:flutter/material.dart';
import '../db_helper.dart';

class DevicesScreen extends StatefulWidget {
  const DevicesScreen({Key? key}) : super(key: key);

  @override
  State<DevicesScreen> createState() => _DevicesScreenState();
}

class _DevicesScreenState extends State<DevicesScreen> {
  // تنسيق آخر ظهور للجهاز بشكل آمن
  String _formatLastSeen(dynamic lastSeen) {
    if (lastSeen == null) return '-';
    try {
      if (lastSeen is DateTime) {
        return lastSeen.toLocal().toString().substring(0, 19);
      } else if (lastSeen is int) {
        // إذا كان رقم، اعتبره timestamp بالميلي ثانية
        final dt = DateTime.fromMillisecondsSinceEpoch(lastSeen);
        return dt.toLocal().toString().substring(0, 19);
      } else if (lastSeen is String) {
        // حاول تحويله إلى DateTime إذا كان ISO
        try {
          final dt = DateTime.parse(lastSeen);
          return dt.toLocal().toString().substring(0, 19);
        } catch (_) {
          return lastSeen;
        }
      } else {
        return lastSeen.toString();
      }
    } catch (e) {
      return lastSeen.toString();
    }
  }

  List<Map<String, dynamic>> _devices = [];
  List<Map<String, dynamic>> _filteredDevices = [];
  bool _loadingList = true;
  bool _loadingDevice = false;
  Timer? _refreshTimer;
  // جلب تفاصيل الجهاز بعد تسجيل الدخول مع دعم منافذ متعددة
  Future<Map<String, dynamic>> fetchDeviceDetails(
    String ip,
    String cookies, {
    String? port,
    String? protocol,
  }) async {
    debugPrint('[NETWORK] جلب تفاصيل الجهاز: $ip');
    final ioc = HttpClient()
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
    final ioClient = IOClient(ioc);

    // إنشاء timestamp للطلب (مثل المتصفح)
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    // تحديد البروتوكول والبورت المناسب
    final detectedProtocol = protocol ?? 'https';
    final detectedPort = port ?? '443';
    final baseIp = ip.contains(':') ? ip.split(':')[0] : ip;
    final ipWithPort = '$baseIp:$detectedPort';

    // قائمة المحاولات لجلب تفاصيل الجهاز مع البروتوكول والبورت المكتشف أولاً
    final detailsAttempts = [
      // المحاولة بالبروتوكول والبورت المكتشف أولاً
      '$detectedProtocol://$ipWithPort/status.cgi?_=$timestamp',
      '$detectedProtocol://$ipWithPort/api/status?_=$timestamp',
      '$detectedProtocol://$ipWithPort/api/system/status?_=$timestamp',
      '$detectedProtocol://$ipWithPort/api/device/status?_=$timestamp',
      '$detectedProtocol://$ipWithPort/cgi-bin/status.cgi?_=$timestamp',

      // محاولات إضافية بالبروتوكولات والبورتات الأخرى
      'https://$baseIp:443/status.cgi?_=$timestamp',
      'https://$baseIp:443/api/status?_=$timestamp',
      'https://$baseIp:8443/status.cgi?_=$timestamp',
      'http://$baseIp:80/status.cgi?_=$timestamp',
      'http://$baseIp:80/api/status?_=$timestamp',
      'http://$baseIp:8080/status.cgi?_=$timestamp',
      'https://$baseIp/status.cgi?_=$timestamp',
      'http://$baseIp/status.cgi?_=$timestamp',
    ];

    bool gotDetails = false;
    for (final url in detailsAttempts) {
      try {
        debugPrint('[NETWORK] محاولة جلب التفاصيل من: $url');
        final uri = Uri.parse(url);
        String cleanCookies = '';
        if (cookies.contains('AIROS_')) {
          final cookieParts = cookies.split(';');
          final airosCookie = cookieParts.firstWhere(
            (part) => part.trim().startsWith('AIROS_'),
            orElse: () => '',
          );
          if (airosCookie.isNotEmpty) {
            cleanCookies = airosCookie.trim();
          }
        }
        final basicCookies = [
          'ui_language=en_US',
          'last_check=${DateTime.now().millisecondsSinceEpoch}',
        ];
        if (cleanCookies.isNotEmpty) {
          cleanCookies = '$cleanCookies; ${basicCookies.join('; ')}';
        } else {
          cleanCookies = basicCookies.join('; ');
        }
        debugPrint('[NETWORK] PREPARED COOKIES: $cleanCookies');
        final response = await ioClient
            .get(
              uri,
              headers: {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
                'Connection': 'keep-alive',
                'Cookie': cleanCookies,
                'Host': ip,
                'Referer': 'https://$ip/index.cgi',
                'Sec-Ch-Ua':
                    '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
              },
            )
            .timeout(const Duration(seconds: 8));
        debugPrint('[NETWORK] RESPONSE [$url]: ${response.statusCode}');
        if (response.statusCode == 200) {
          final data = response.body;
          debugPrint(
            '[NETWORK] BODY [$url]: ${data.length > 200 ? data.substring(0, 200) + "..." : data}',
          );
          try {
            final json = data.startsWith('{') ? jsonDecode(data) : null;
            if (json != null) {
              final model =
                  json['host']?['devmodel'] ??
                  json['device']?['model'] ??
                  json['system']?['model'] ??
                  '-';
              final name =
                  json['host']?['hostname'] ??
                  json['device']?['name'] ??
                  json['system']?['hostname'] ??
                  '-';
              final version =
                  json['host']?['fwversion'] ??
                  json['firmware']?['version'] ??
                  json['system']?['version'] ??
                  '-';
              final fwprefix = json['host']?['fwprefix'] ?? '';
              final uptime =
                  json['host']?['uptime'] ?? json['system']?['uptime'] ?? '-';
              final lan0 =
                  json['interfaces'] != null && json['interfaces'].isNotEmpty
                  ? json['interfaces'][0]['hwaddr'] ?? '-'
                  : json['network']?['mac'] ?? '-';
              final essid = json['wireless']?['essid']?.toString() ?? '-';
              final channel = json['wireless']?['channel']?.toString() ?? '-';
              final frequency =
                  json['wireless']?['frequency']?.toString() ?? '-';
              final apmac = json['wireless']?['apmac']?.toString() ?? '-';
              final txrate = json['wireless']?['txrate']?.toString() ?? '-';
              final rxrate = json['wireless']?['rxrate']?.toString() ?? '-';
              final security = json['wireless']?['security']?.toString() ?? '-';
              final cpuload = json['host']?['cpuload']?.toString() ?? '-';
              final freeram = json['host']?['freeram']?.toString() ?? '-';
              final totalram = json['host']?['totalram']?.toString() ?? '-';
              final signal =
                  json['wireless']?['signal'] ??
                  json['radio']?['signal'] ??
                  '-';
              final ccq =
                  json['wireless']?['ccq'] ?? json['radio']?['ccq'] ?? '-';
              debugPrint('[NETWORK] تم جلب التفاصيل بنجاح من: $url');
              gotDetails = true;
              return {
                'Device Model': model,
                'Device Name': name,
                'Version': version.startsWith('v')
                    ? version
                    : 'v$version${fwprefix.isNotEmpty ? ' ($fwprefix)' : ''}',
                'Uptime': _formatUptime(uptime),
                'LAN0': lan0,
                'Signal Strength': signal,
                'Transmit CCQ': ccq,
                'ESSID': essid,
                'Channel': channel,
                'Frequency': frequency,
                'AP MAC': apmac,
                'TX Rate': txrate,
                'RX Rate': rxrate,
                'Security': security,
                'CPU Load': cpuload,
                'Free RAM': freeram,
                'Total RAM': totalram,
              };
            }
          } catch (e) {
            debugPrint('[NETWORK] خطأ في فك JSON من $url: $e');
          }
          if (data.contains('Device Model') ||
              data.contains('hostname') ||
              data.contains('devmodel')) {
            debugPrint('[NETWORK] محاولة تحليل البيانات كنص من: $url');
            gotDetails = true;
            return _parseTextResponse(data);
          }
        } else if (response.statusCode == 204) {
          debugPrint(
            '[NETWORK] استجابة 204 من $url - الجهاز متصل لكن لا يوجد محتوى',
          );
          if (url.contains('status.cgi') && url.contains('_=')) {
            final simpleUrl = url.split('?')[0];
            try {
              debugPrint('[NETWORK] محاولة بدون timestamp: $simpleUrl');
              final simpleResponse = await ioClient
                  .get(
                    Uri.parse(simpleUrl),
                    headers: {
                      'Accept':
                          'application/json, text/javascript, */*; q=0.01',
                      'Cookie': cleanCookies,
                      'User-Agent':
                          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                      'X-Requested-With': 'XMLHttpRequest',
                      'Referer': 'https://$ip/index.cgi',
                    },
                  )
                  .timeout(const Duration(seconds: 5));
              debugPrint(
                '[NETWORK] SIMPLE RESPONSE [$simpleUrl]: ${simpleResponse.statusCode}',
              );
              if (simpleResponse.statusCode == 200 &&
                  simpleResponse.body.isNotEmpty) {
                final data = simpleResponse.body;
                debugPrint(
                  '[NETWORK] SIMPLE BODY: ${data.substring(0, data.length > 200 ? 200 : data.length)}...',
                );
                try {
                  final json = jsonDecode(data);
                  final model = json['host']?['devmodel'] ?? 'UBNT Device';
                  final name = json['host']?['hostname'] ?? 'Unknown';
                  final version = json['host']?['fwversion'] ?? '-';
                  final fwprefix = json['host']?['fwprefix'] ?? '';
                  debugPrint('[NETWORK] تم جلب التفاصيل بنجاح من: $simpleUrl');
                  gotDetails = true;
                  return {
                    'Device Model': model,
                    'Device Name': name,
                    'Version': version.startsWith('v')
                        ? version
                        : 'v$version${fwprefix.isNotEmpty ? ' ($fwprefix)' : ''}',
                    'Uptime': _formatUptime(json['host']?['uptime']),
                    'LAN0': json['interfaces']?[0]?['hwaddr'] ?? '-',
                    'Signal Strength':
                        json['wireless']?['signal']?.toString() ?? '-',
                    'Transmit CCQ': json['wireless']?['ccq']?.toString() ?? '-',
                    'ESSID': json['wireless']?['essid']?.toString() ?? '-',
                    'Channel': json['wireless']?['channel']?.toString() ?? '-',
                    'Frequency':
                        json['wireless']?['frequency']?.toString() ?? '-',
                    'AP MAC': json['wireless']?['apmac']?.toString() ?? '-',
                    'TX Rate': json['wireless']?['txrate']?.toString() ?? '-',
                    'RX Rate': json['wireless']?['rxrate']?.toString() ?? '-',
                    'Security':
                        json['wireless']?['security']?.toString() ?? '-',
                    'CPU Load': json['host']?['cpuload']?.toString() ?? '-',
                    'Free RAM': json['host']?['freeram']?.toString() ?? '-',
                    'Total RAM': json['host']?['totalram']?.toString() ?? '-',
                  };
                } catch (e) {
                  debugPrint('[NETWORK] خطأ في فك JSON البسيط: $e');
                }
              }
            } catch (e) {
              debugPrint('[NETWORK] خطأ في الطلب البسيط: $e');
            }
          }
        }
      } catch (e) {
        debugPrint('[NETWORK] خطأ في جلب التفاصيل من $url: $e');
        continue;
      }
    }
    // إذا لم يتم الحصول على تفاصيل من status.cgi أو غيره، جرب جلب الصفحة الرئيسية مباشرة بعد تسجيل الدخول
    if (!gotDetails) {
      try {
        debugPrint('[NETWORK] محاولة جلب الصفحة الرئيسية بعد تسجيل الدخول');
        final mainUri = Uri.parse('https://$ip/index.cgi');
        final mainResponse = await ioClient
            .get(
              mainUri,
              headers: {
                'Accept':
                    'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br, zstd',
                'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
                'Connection': 'keep-alive',
                'Cookie': cookies,
                'Host': ip,
                'Referer': 'https://$ip/login.cgi?uri=/index.cgi',
                'Sec-Ch-Ua':
                    '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
              },
            )
            .timeout(const Duration(seconds: 10));
        debugPrint('[NETWORK] MAIN RESPONSE: ${mainResponse.statusCode}');
        debugPrint('[NETWORK] MAIN HEADERS: ${mainResponse.headers}');
        debugPrint(
          '[NETWORK] MAIN BODY: ${mainResponse.body.length > 500 ? mainResponse.body.substring(0, 500) + "..." : mainResponse.body}',
        );
        // محاولة استخراج بيانات الجهاز من HTML
        final htmlData = mainResponse.body;
        final details = _parseTextResponse(htmlData);
        debugPrint('[NETWORK] PARSED DETAILS FROM HTML: $details');
        return details;
      } catch (e) {
        debugPrint('[NETWORK] خطأ في جلب الصفحة الرئيسية بعد تسجيل الدخول: $e');
      }
    }

    debugPrint('[NETWORK] فشل في جلب التفاصيل من جميع المحاولات');

    // المحاولة الأخيرة: جرب POST request مع form data
    debugPrint('[NETWORK] المحاولة الأخيرة: POST request');
    try {
      final postUrl = 'https://$ip/status.cgi';
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // تحضير الكوكيز النهائية
      String finalCookies = '';
      if (cookies.contains('AIROS_')) {
        final cookieParts = cookies.split(';');
        final airosCookie = cookieParts.firstWhere(
          (part) => part.trim().startsWith('AIROS_'),
          orElse: () => '',
        );
        if (airosCookie.isNotEmpty) {
          finalCookies =
              '${airosCookie.trim()}; ui_language=en_US; last_check=$timestamp';
        }
      }

      debugPrint('[NETWORK] POST محاولة: $postUrl');
      debugPrint('[NETWORK] POST COOKIES: $finalCookies');

      final postResponse = await ioClient
          .post(
            Uri.parse(postUrl),
            headers: {
              'Accept': 'application/json, text/javascript, */*; q=0.01',
              'Accept-Encoding': 'gzip, deflate, br, zstd',
              'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
              'Connection': 'keep-alive',
              'Content-Type':
                  'application/x-www-form-urlencoded; charset=UTF-8',
              'Cookie': finalCookies,
              'Host': ip,
              'Origin': 'https://$ip',
              'Referer': 'https://$ip/index.cgi',
              'Sec-Ch-Ua':
                  '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
              'Sec-Ch-Ua-Mobile': '?0',
              'Sec-Ch-Ua-Platform': '"Windows"',
              'Sec-Fetch-Dest': 'empty',
              'Sec-Fetch-Mode': 'cors',
              'Sec-Fetch-Site': 'same-origin',
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
              'X-Requested-With': 'XMLHttpRequest',
            },
            body: '_=$timestamp',
          )
          .timeout(const Duration(seconds: 10));

      debugPrint('[NETWORK] POST RESPONSE: ${postResponse.statusCode}');

      if (postResponse.statusCode == 200 && postResponse.body.isNotEmpty) {
        final data = postResponse.body;
        debugPrint(
          '[NETWORK] POST SUCCESS BODY: ${data.substring(0, data.length > 500 ? 500 : data.length)}...',
        );

        try {
          final json = jsonDecode(data);
          if (json is Map && json.containsKey('host')) {
            final host = json['host'] as Map<String, dynamic>;
            final wireless = json['wireless'] as Map<String, dynamic>?;
            final interfaces = json['interfaces'] as List?;

            debugPrint('[NETWORK] 🎉 POST نجح! تم جلب التفاصيل');
            return {
              'Device Model': host['devmodel'] ?? 'UBNT Device',
              'Device Name': host['hostname'] ?? 'Unknown Device',
              'Version':
                  'v${host['fwversion'] ?? 'Unknown'}${host['fwprefix'] != null ? ' (${host['fwprefix']})' : ''}',
              'Uptime': _formatUptime(host['uptime']),
              'LAN0': interfaces?.isNotEmpty == true
                  ? interfaces![0]['hwaddr'] ?? '-'
                  : '-',
              'Signal Strength': wireless?['signal']?.toString() ?? '-',
              'Transmit CCQ': wireless?['ccq']?.toString() ?? '-',
              'ESSID': wireless?['essid']?.toString() ?? '-',
              'Channel': wireless?['channel']?.toString() ?? '-',
              'Frequency': wireless?['frequency']?.toString() ?? '-',
              'AP MAC': wireless?['apmac']?.toString() ?? '-',
              'TX Rate': wireless?['txrate']?.toString() ?? '-',
              'RX Rate': wireless?['rxrate']?.toString() ?? '-',
              'Security': wireless?['security']?.toString() ?? '-',
              'CPU Load': host['cpuload']?.toString() ?? '-',
              'Free RAM': host['freeram']?.toString() ?? '-',
              'Total RAM': host['totalram']?.toString() ?? '-',
            };
          }
        } catch (e) {
          debugPrint('[NETWORK] خطأ في فك JSON من POST: $e');
        }
      }
    } catch (e) {
      debugPrint('[NETWORK] خطأ في POST request: $e');
    }

    // المحاولة الأخيرة: تهيئة الجلسة ثم جلب البيانات
    debugPrint('[NETWORK] المحاولة الأخيرة: تهيئة الجلسة');
    try {
      // تحضير الكوكيز للجلسة
      String sessionCookies = '';
      if (cookies.contains('AIROS_')) {
        final cookieParts = cookies.split(';');
        final airosCookie = cookieParts.firstWhere(
          (part) => part.trim().startsWith('AIROS_'),
          orElse: () => '',
        );
        if (airosCookie.isNotEmpty) {
          sessionCookies = '${airosCookie.trim()}; ui_language=en_US';
        }
      }

      // 1. جلب الصفحة الرئيسية لتهيئة الجلسة
      final indexResponse = await ioClient
          .get(
            Uri.parse('https://$ip/index.cgi'),
            headers: {
              'Accept':
                  'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
              'Cookie': sessionCookies,
              'User-Agent':
                  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            },
          )
          .timeout(const Duration(seconds: 5));

      debugPrint('[NETWORK] INDEX RESPONSE: ${indexResponse.statusCode}');

      if (indexResponse.statusCode == 200) {
        // 2. جمع الكوكيز الجديدة من الصفحة الرئيسية
        final newCookies = indexResponse.headers['set-cookie'];
        String updatedCookies = sessionCookies;

        if (newCookies != null) {
          List<String> cookieList;
          if (newCookies is List) {
            cookieList = (newCookies as List).map((e) => e.toString()).toList();
          } else {
            cookieList = [newCookies.toString()];
          }

          for (final cookie in cookieList) {
            final cookieName = cookie.split('=')[0];
            if (!updatedCookies.contains(cookieName)) {
              updatedCookies += '; ${cookie.split(';')[0]}';
            }
          }
        }

        debugPrint('[NETWORK] UPDATED COOKIES: $updatedCookies');

        // 3. الآن جرب جلب البيانات مع الجلسة المهيأة
        final finalStatusResponse = await ioClient
            .get(
              Uri.parse(
                'https://$ip/status.cgi?_=${DateTime.now().millisecondsSinceEpoch}',
              ),
              headers: {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Cookie': updatedCookies,
                'Referer': 'https://$ip/index.cgi',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'X-Requested-With': 'XMLHttpRequest',
              },
            )
            .timeout(const Duration(seconds: 5));

        debugPrint(
          '[NETWORK] FINAL STATUS RESPONSE: ${finalStatusResponse.statusCode}',
        );

        if (finalStatusResponse.statusCode == 200 &&
            finalStatusResponse.body.isNotEmpty) {
          final data = finalStatusResponse.body;
          debugPrint(
            '[NETWORK] FINAL SUCCESS BODY: ${data.substring(0, data.length > 500 ? 500 : data.length)}...',
          );

          try {
            final json = jsonDecode(data);
            if (json is Map && json.containsKey('host')) {
              final host = json['host'] as Map<String, dynamic>;
              final wireless = json['wireless'] as Map<String, dynamic>?;
              final interfaces = json['interfaces'] as List?;

              debugPrint(
                '[NETWORK] 🎉 FINAL SUCCESS! تم جلب التفاصيل بعد تهيئة الجلسة',
              );
              return {
                'Device Model': host['devmodel'] ?? 'UBNT Device',
                'Device Name': host['hostname'] ?? 'Unknown Device',
                'Version':
                    'v${host['fwversion'] ?? 'Unknown'}${host['fwprefix'] != null ? ' (${host['fwprefix']})' : ''}',
                'Uptime': _formatUptime(host['uptime']),
                'LAN0': interfaces?.isNotEmpty == true
                    ? interfaces![0]['hwaddr'] ?? '-'
                    : '-',
                'Signal Strength': wireless?['signal']?.toString() ?? '-',
                'Transmit CCQ': wireless?['ccq']?.toString() ?? '-',
                'ESSID': wireless?['essid']?.toString() ?? '-',
                'Channel': wireless?['channel']?.toString() ?? '-',
                'Frequency': wireless?['frequency']?.toString() ?? '-',
                'AP MAC': wireless?['apmac']?.toString() ?? '-',
                'TX Rate': wireless?['txrate']?.toString() ?? '-',
                'RX Rate': wireless?['rxrate']?.toString() ?? '-',
                'Security': wireless?['security']?.toString() ?? '-',
                'CPU Load': host['cpuload']?.toString() ?? '-',
                'Free RAM': host['freeram']?.toString() ?? '-',
                'Total RAM': host['totalram']?.toString() ?? '-',
              };
            }
          } catch (e) {
            debugPrint('[NETWORK] خطأ في فك JSON النهائي: $e');
          }
        }
      }
    } catch (e) {
      debugPrint('[NETWORK] خطأ في تهيئة الجلسة: $e');
    }

    // إذا فشل كل شيء، نرجع معلومات أساسية
    debugPrint('[NETWORK] فشل في جميع المحاولات - إرجاع معلومات أساسية');
    return {
      'Device Model': 'UBNT Device',
      'Device Name': 'Connected Device',
      'Version': 'Unknown',
      'Uptime': 'Unknown',
      'LAN0': 'Unknown',
      'Signal Strength': 'Unknown',
      'Transmit CCQ': 'Unknown',
      'info': 'الجهاز متصل لكن تعذر جلب التفاصيل الكاملة',
    };
  }

  // تنسيق وقت التشغيل
  String _formatUptime(dynamic uptime) {
    if (uptime == null) return '-';

    try {
      final seconds = uptime is int ? uptime : int.tryParse(uptime.toString());
      if (seconds == null) return uptime.toString();

      final days = seconds ~/ 86400;
      final hours = (seconds % 86400) ~/ 3600;
      final minutes = (seconds % 3600) ~/ 60;

      if (days > 0) {
        return '${days}d ${hours}h ${minutes}m';
      } else if (hours > 0) {
        return '${hours}h ${minutes}m';
      } else {
        return '${minutes}m';
      }
    } catch (e) {
      return uptime.toString();
    }
  }

  // تحليل الاستجابة النصية لاستخراج معلومات الجهاز
  Map<String, dynamic> _parseTextResponse(String data) {
    final result = <String, dynamic>{
      'Device Model': '-',
      'Device Name': '-',
      'Version': '-',
      'Uptime': '-',
      'LAN0': '-',
      'Signal Strength': '-',
      'Transmit CCQ': '-',
    };

    // البحث عن الأنماط الشائعة في النص
    final patterns = {
      'Device Model': [
        r'Device Model[:\s]+([^\n\r<]+)',
        r'devmodel[:\s]+([^\n\r<]+)',
      ],
      'Device Name': [
        r'Device Name[:\s]+([^\n\r<]+)',
        r'hostname[:\s]+([^\n\r<]+)',
      ],
      'Version': [r'Version[:\s]+([^\n\r<]+)', r'fwversion[:\s]+([^\n\r<]+)'],
      'Uptime': [r'Uptime[:\s]+([^\n\r<]+)', r'uptime[:\s]+([^\n\r<]+)'],
      'LAN0': [r'LAN0[:\s]+([^\n\r<]+)', r'hwaddr[:\s]+([^\n\r<]+)'],
      'Signal Strength': [
        r'Signal Strength[:\s]+([^\n\r<]+)',
        r'signal[:\s]+([^\n\r<]+)',
      ],
      'Transmit CCQ': [
        r'Transmit CCQ[:\s]+([^\n\r<]+)',
        r'ccq[:\s]+([^\n\r<]+)',
      ],
    };

    for (final entry in patterns.entries) {
      for (final pattern in entry.value) {
        final regex = RegExp(pattern, caseSensitive: false);
        final match = regex.firstMatch(data);
        if (match != null && match.group(1) != null) {
          result[entry.key] = match.group(1)!.trim();
          break;
        }
      }
    }

    return result;
  }

  @override
  void initState() {
    super.initState();
    _loadDevices();
    _startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      if (mounted) {
        _loadDevices();
      }
    });
  }

  void _filterDevices() {
    setState(() {
      _filteredDevices = List.from(_devices);
    });
  }

  Future<void> _loadDevices() async {
    if (!mounted) return;

    setState(() {
      _loadingList = true;
    });

    try {
      await DBHelper.instance.createDevicesTableIfNotExists();
      final devices = await DBHelper.instance.getAllDevices();

      if (mounted) {
        setState(() {
          _devices = devices.map((d) {
            final map = Map<String, dynamic>.from(d); // Make mutable copy
            if (map['lastSeen'] != null && map['lastSeen'] is String) {
              map['lastSeen'] =
                  DateTime.tryParse(map['lastSeen']) ?? map['lastSeen'];
            }
            // معالجة details: إذا كانت String، حاول تحويلها إلى Map
            if (map['details'] != null && map['details'] is String) {
              try {
                map['details'] = jsonDecode(map['details']);
              } catch (e) {
                map['details'] = {'error': 'تعذر قراءة تفاصيل الجهاز'};
              }
            }
            return map;
          }).toList();
          _filteredDevices = List.from(_devices);
          _loadingList = false;
        });
      }
    } catch (e) {
      debugPrint('[DEVICES] خطأ في تحميل الأجهزة: $e');
      if (mounted) {
        setState(() {
          _loadingList = false;
        });
      }
    }
  }

  void _showAddDeviceDialog() {
    final nameController = TextEditingController();
    final ipController = TextEditingController();
    final userController = TextEditingController();
    final passController = TextEditingController();
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              colors: [
                colorScheme.primary.withValues(alpha: 0.1),
                colorScheme.surface,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // رأس الحوار
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.add,
                        color: colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'إضافة جهاز جديد',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // حقول الإدخال
                _buildModernTextField(
                  controller: nameController,
                  label: 'اسم الجهاز',
                  icon: Icons.devices,
                ),
                const SizedBox(height: 16),
                _buildModernTextField(
                  controller: ipController,
                  label: 'IP Address',
                  icon: Icons.language,
                  keyboardType: TextInputType.text,
                  textDirection: TextDirection.ltr,
                ),
                const SizedBox(height: 16),
                _buildModernTextField(
                  controller: userController,
                  label: 'اسم المستخدم',
                  icon: Icons.person,
                ),
                const SizedBox(height: 16),
                _buildModernTextField(
                  controller: passController,
                  label: 'كلمة المرور',
                  icon: Icons.lock,
                  obscureText: true,
                ),
                const SizedBox(height: 24),

                // أزرار الإجراء
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _handleAddDevice(
                          nameController.text.trim(),
                          ipController.text.trim(),
                          userController.text.trim(),
                          passController.text.trim(),
                        ),
                        icon: const Icon(Icons.add, size: 18),
                        label: const Text('إضافة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          foregroundColor: colorScheme.onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // معالجة إضافة الجهاز
  Future<void> _handleAddDevice(
    String name,
    String ip,
    String user,
    String pass,
  ) async {
    if (ip.isEmpty || user.isEmpty || pass.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول المطلوبة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.of(context).pop();
    setState(() => _loadingDevice = true);

    // التحقق من وجود الجهاز مسبقاً في قاعدة البيانات
    final existingDevice = await DBHelper.instance.getDeviceByIp(ip);

    if (existingDevice != null) {
      if (mounted) {
        setState(() => _loadingDevice = false);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('هذا الجهاز موجود مسبقاً'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    try {
      // إضافة الجهاز مباشرة بدون اختبار الاتصال
      Map<String, dynamic> dbDevice = {};
      dbDevice['custom_name'] = name.isNotEmpty
          ? name
          : null; // اسم مخصص من المستخدم
      dbDevice['ip'] = ip;
      dbDevice['user'] = user;
      dbDevice['pass'] = pass;
      dbDevice['lastSeen'] = DateTime.now().toIso8601String();
      dbDevice['status'] = 'لم يتم الاختبار';
      dbDevice['details'] = jsonEncode({'info': 'لم يتم اختبار الاتصال بعد'});

      debugPrint('[DB] إضافة جهاز جديد: $dbDevice');

      // حفظ الجهاز في قاعدة البيانات
      await DBHelper.instance.insertDevice(dbDevice);
      await _loadDevices();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Text('تم إضافة الجهاز بنجاح'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('[ERROR] خطأ في إضافة الجهاز: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة الجهاز: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loadingDevice = false);
      }
    }
  }

  // جلب بيانات جهاز UBNT: يحدد نوع الجهاز تلقائياً مع دعم منافذ متعددة
  Future<Map<String, dynamic>> _fetchUPNTDeviceData(
    String ip,
    String user,
    String pass,
  ) async {
    debugPrint('[NETWORK] بدء محاولة تسجيل الدخول للجهاز: $ip');
    final ioc = HttpClient()
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
    final ioClient = IOClient(ioc);

    // قائمة البورتات المختلفة للمحاولة
    final ports = ['443', '80', '8443', '8080', '9443'];

    // إذا كان IP يحتوي على بورت مسبقاً، استخدمه أولاً
    if (ip.contains(':')) {
      final parts = ip.split(':');
      final baseIp = parts[0];
      final specifiedPort = parts[1];

      // جرب البورت المحدد أولاً، ثم البورتات الأخرى
      final portsToTry = [
        specifiedPort,
        ...ports.where((p) => p != specifiedPort),
      ];

      for (final port in portsToTry) {
        final result = await _tryLoginWithPort(
          ioClient,
          baseIp,
          port,
          user,
          pass,
        );
        if (result['status'] == 'متصل') {
          return result;
        }
      }
    } else {
      // جرب جميع البورتات
      for (final port in ports) {
        final result = await _tryLoginWithPort(ioClient, ip, port, user, pass);
        if (result['status'] == 'متصل') {
          return result;
        }
      }
    }

    debugPrint('[NETWORK] فشل في الاتصال بالجهاز عبر جميع البورتات');
    return {
      'status': 'غير متصل',
      'lastSeen': DateTime.now(),
      'error': 'فشل في الاتصال بالجهاز عبر جميع البورتات المتاحة',
    };
  }

  // محاولة تسجيل الدخول عبر بورت محدد
  Future<Map<String, dynamic>> _tryLoginWithPort(
    IOClient ioClient,
    String ip,
    String port,
    String user,
    String pass,
  ) async {
    try {
      final ipWithPort = '$ip:$port';
      final protocol = (port == '80' || port == '8080') ? 'http' : 'https';

      debugPrint('[NETWORK] محاولة الاتصال عبر $protocol://$ipWithPort');

      // 1. جلب الصفحة الرئيسية أولاً للحصول على session id
      final indexUrl = '$protocol://$ipWithPort/index.cgi';
      debugPrint('[NETWORK] محاولة جلب الصفحة الرئيسية: $indexUrl');

      String sessionCookies = '';
      try {
        final indexResponse = await ioClient
            .get(
              Uri.parse(indexUrl),
              headers: {
                'Accept':
                    'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Referer': '$protocol://$ipWithPort/login.cgi',
                'Origin': '$protocol://$ipWithPort',
                'Connection': 'keep-alive',
                'Host': ipWithPort,
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Ch-Ua':
                    '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
              },
            )
            .timeout(const Duration(seconds: 8));

        debugPrint(
          '[NETWORK] INDEX RESPONSE [$port]: ${indexResponse.statusCode}',
        );

        final setCookieHeaders = indexResponse.headers['set-cookie'];
        if (setCookieHeaders != null) {
          if (setCookieHeaders is List) {
            final cookieList = setCookieHeaders as List;
            sessionCookies = cookieList
                .map((cookie) => cookie.toString().split(';')[0])
                .join('; ');
          } else {
            sessionCookies = setCookieHeaders.toString().split(';')[0];
          }
        }
        debugPrint('[NETWORK] SESSION COOKIES [$port]: $sessionCookies');
      } catch (e) {
        debugPrint('[NETWORK] فشل في جلب الصفحة الرئيسية للبورت $port: $e');
        return {
          'status': 'غير متصل',
          'lastSeen': DateTime.now(),
          'error': 'فشل في جلب الصفحة الرئيسية للبورت $port',
        };
      }

      // 2. استخدام الكوكيز في طلب تسجيل الدخول
      final loginUrl = '$protocol://$ipWithPort/login.cgi';
      final loginUri = Uri.parse(loginUrl);
      final headers = <String, String>{
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept':
            'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': sessionCookies,
        'Host': ipWithPort,
        'Origin': '$protocol://$ipWithPort',
        'Referer': '$protocol://$ipWithPort/login.cgi',
      };
      final body = 'username=$user&password=$pass&uri=/index.cgi';

      try {
        final response = await ioClient
            .post(loginUri, headers: headers, body: body)
            .timeout(const Duration(seconds: 12));

        debugPrint('[NETWORK] LOGIN RESPONSE [$port]: ${response.statusCode}');
        debugPrint(
          '[NETWORK] LOGIN LOCATION [$port]: ${response.headers['location']}',
        );

        final setCookieHeaders = response.headers['set-cookie'];
        final location = response.headers['location'] ?? '';

        // جمع جميع الكوكيز
        String allCookies = '';
        if (setCookieHeaders != null) {
          if (setCookieHeaders is List) {
            final cookieList = setCookieHeaders as List;
            allCookies = cookieList
                .map((cookie) => cookie.toString().split(';')[0])
                .join('; ');
          } else {
            allCookies = setCookieHeaders.toString().split(';')[0];
          }
        }

        // فحص نجاح تسجيل الدخول
        if ((response.statusCode == 302 && location.contains('index.cgi')) ||
            (response.statusCode == 200 && allCookies.isNotEmpty) ||
            (response.statusCode == 302 && allCookies.isNotEmpty)) {
          debugPrint('[NETWORK] ✅ نجح تسجيل الدخول عبر البورت $port');
          return {
            'status': 'متصل',
            'cookies': allCookies,
            'lastSeen': DateTime.now(),
            'connectionMethod': loginUrl,
            'port': port,
            'protocol': protocol,
          };
        }
      } catch (e) {
        debugPrint('[NETWORK] فشل في تسجيل الدخول عبر البورت $port: $e');
      }
    } catch (e) {
      debugPrint('[NETWORK] خطأ عام في البورت $port: $e');
    }

    return {
      'status': 'غير متصل',
      'lastSeen': DateTime.now(),
      'error': 'فشل في الاتصال عبر البورت $port',
    };
  }

  Future<void> _rebootDevice(String ip) async {
    debugPrint('[NETWORK] إرسال أمر إعادة تشغيل للجهاز: $ip');

    // البحث عن الجهاز في القائمة للحصول على بيانات المصادقة
    final device = _devices.firstWhere(
      (d) => d['ip'] == ip,
      orElse: () => <String, dynamic>{},
    );

    if (device.isEmpty) {
      debugPrint('[NETWORK] لم يتم العثور على بيانات الجهاز');
      return;
    }

    final user = device['user'] ?? '';
    final pass = device['pass'] ?? '';

    if (user.isEmpty || pass.isEmpty) {
      debugPrint('[NETWORK] بيانات المصادقة مفقودة');
      return;
    }

    final ioc = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    final ioClient = IOClient(ioc);

    try {
      // أولاً: تسجيل الدخول للحصول على الكوكيز
      final loginData = await _fetchUPNTDeviceData(ip, user, pass);

      if (loginData['status'] != 'متصل' || loginData['cookies'] == null) {
        debugPrint('[NETWORK] فشل في تسجيل الدخول للجهاز');
        return;
      }

      final cookies = loginData['cookies'] as String;
      final detectedPort = loginData['port'] ?? '443';
      final detectedProtocol = loginData['protocol'] ?? 'https';
      final baseIp = ip.contains(':') ? ip.split(':')[0] : ip;
      final ipWithPort = '$baseIp:$detectedPort';

      // قائمة المحاولات لإرسال أمر إعادة التشغيل باستخدام البروتوكول والبورت المكتشف
      final rebootAttempts = [
        '$detectedProtocol://$ipWithPort/reboot.cgi',
        '$detectedProtocol://$ipWithPort/api/reboot',
        '$detectedProtocol://$ipWithPort/api/system/reboot',
        '$detectedProtocol://$ipWithPort/cgi-bin/reboot.cgi',
        // محاولات إضافية بالبروتوكولات الأخرى
        'https://$baseIp:443/reboot.cgi',
        'http://$baseIp:80/reboot.cgi',
        'https://$baseIp:8443/reboot.cgi',
        'http://$baseIp:8080/reboot.cgi',
      ];

      for (final url in rebootAttempts) {
        try {
          debugPrint('[NETWORK] محاولة إعادة التشغيل من: $url');

          final response = await ioClient
              .post(
                Uri.parse(url),
                headers: {
                  'Cookie': cookies,
                  'Content-Type': 'application/x-www-form-urlencoded',
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                  'Accept': 'application/json, text/javascript, */*; q=0.01',
                  'X-Requested-With': 'XMLHttpRequest',
                  'Referer': '$detectedProtocol://$ipWithPort/index.cgi',
                  'Host': ipWithPort,
                  'Origin': '$detectedProtocol://$ipWithPort',
                },
                body: 'reboot=1',
              )
              .timeout(const Duration(seconds: 10));

          debugPrint(
            '[NETWORK] REBOOT RESPONSE [$url]: ${response.statusCode}',
          );

          if (response.statusCode == 200 || response.statusCode == 204) {
            debugPrint('[NETWORK] تم إرسال أمر إعادة التشغيل بنجاح');

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: const Row(
                    children: [
                      Icon(Icons.restart_alt, color: Colors.white),
                      SizedBox(width: 12),
                      Text('تم إرسال أمر إعادة التشغيل بنجاح'),
                    ],
                  ),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              );
            }
            return;
          }
        } catch (e) {
          debugPrint('[NETWORK] خطأ في إرسال أمر إعادة التشغيل من $url: $e');
          continue;
        }
      }

      debugPrint('[NETWORK] فشل في إرسال أمر إعادة التشغيل من جميع المحاولات');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 12),
                Text('فشل في إرسال أمر إعادة التشغيل'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('[NETWORK] خطأ عام في إعادة التشغيل: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Text('خطأ في إعادة التشغيل: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } finally {
      ioClient.close();
    }
  }

  // اختبار الاتصال بالجهاز
  Future<void> _testDeviceConnection(Map<String, dynamic> device) async {
    final deviceId = device['id'] as int;
    final ip = device['ip'] as String;
    final user = device['user'] as String;
    final pass = device['pass'] as String;

    // تحديث حالة الجهاز إلى "جاري الاختبار"
    await DBHelper.instance.updateDevice(deviceId, {
      'status': 'جاري الاختبار...',
      'lastSeen': DateTime.now().toIso8601String(),
    });
    await _loadDevices();

    try {
      // محاولة الاتصال بالجهاز
      final deviceData = await _fetchUPNTDeviceData(ip, user, pass);
      final status = deviceData['status'] ?? 'غير متصل';

      Map<String, dynamic> updateData = {
        'status': status,
        'lastSeen': DateTime.now().toIso8601String(),
      };

      // جلب التفاصيل إذا نجح الاتصال
      if (status == 'متصل' && deviceData['cookies'] != null) {
        try {
          final details = await fetchDeviceDetails(
            ip,
            deviceData['cookies'],
            port: deviceData['port'],
            protocol: deviceData['protocol'],
          );
          updateData['details'] = jsonEncode(details);

          // تحديث اسم الجهاز والموديل إذا كان متوفراً
          if (details['Device Name'] != null && details['Device Name'] != '-') {
            updateData['name'] = details['Device Name'];
          }
          if (details['Device Model'] != null &&
              details['Device Model'] != '-') {
            updateData['model'] = details['Device Model'];
          }
          if (details['Version'] != null && details['Version'] != '-') {
            updateData['version'] = details['Version'];
          }
        } catch (e) {
          debugPrint('[NETWORK] خطأ في جلب التفاصيل: $e');
          updateData['details'] = jsonEncode({
            'error': 'تعذر جلب تفاصيل الجهاز',
          });
        }
      } else {
        // حفظ سبب فشل الاتصال
        final errorMessage = deviceData['error'] ?? 'تعذر الاتصال بالجهاز';
        updateData['details'] = jsonEncode({'error': errorMessage});
      }

      // تحديث بيانات الجهاز في قاعدة البيانات
      await DBHelper.instance.updateDevice(deviceId, updateData);
      await _loadDevices();

      if (mounted) {
        final isConnected = status == 'متصل';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  isConnected ? Icons.check_circle : Icons.error,
                  color: Colors.white,
                ),
                const SizedBox(width: 12),
                Text(
                  isConnected
                      ? 'تم الاتصال بالجهاز بنجاح'
                      : 'فشل في الاتصال بالجهاز',
                ),
              ],
            ),
            backgroundColor: isConnected ? Colors.green : Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('[ERROR] خطأ في اختبار الاتصال: $e');

      // تحديث حالة الجهاز إلى خطأ
      await DBHelper.instance.updateDevice(deviceId, {
        'status': 'خطأ في الاتصال',
        'lastSeen': DateTime.now().toIso8601String(),
        'details': jsonEncode({'error': 'خطأ في اختبار الاتصال: $e'}),
      });
      await _loadDevices();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Text('خطأ في اختبار الاتصال: $e'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }

  Future<void> _deleteDevice(int id) async {
    print('[DB] حذف جهاز id=$id');
    await DBHelper.instance.deleteDevice(id);
    await _loadDevices();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // محتوى الشاشة
                  _loadingList
                      ? _buildLoadingState(colorScheme)
                      : _filteredDevices.isEmpty
                      ? _buildEmptyState(colorScheme, isDark)
                      : _buildDevicesList(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
      child: Column(
        children: [
          // أزرار الإجراءات في العمود الأيسر
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // العمود الأيسر للأزرار
              Column(
                children: [
                  // زر الإضافة (الوحيد)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.add, color: colorScheme.onPrimary),
                      tooltip: 'إضافة جهاز',
                      onPressed: _showAddDeviceDialog,
                    ),
                  ),
                ],
              ),

              // مساحة فارغة للتوازن
              const SizedBox(width: 48),
            ],
          ),
          const SizedBox(height: 0),

          // الأيقونة الرئيسية في الوسط
          Container(
            margin: const EdgeInsets.only(bottom: 18),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.18),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withValues(
                alpha: isDark ? 0.08 : 0.18,
              ),
              child: Icon(Icons.wifi, color: colorScheme.primary, size: 54),
            ),
          ),

          // العنوان والوصف
          Text(
            'الأجهزة',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
              letterSpacing: 1,
              shadows: [
                Shadow(
                  color: colorScheme.shadow.withValues(alpha: 0.13),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إدارة أجهزة الشبكة والراوترات',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onPrimary.withValues(alpha: 0.92),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء حالة عدم وجود أجهزة
  Widget _buildEmptyState(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.wifi,
              size: 64,
              color: colorScheme.primary.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد أجهزة مضافة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'أضف جهاز جديد للبدء في إدارة الشبكة والاتصالات',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // بناء حالة التحميل
  Widget _buildLoadingState(ColorScheme colorScheme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل الأجهزة...',
            style: TextStyle(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  // بناء قائمة الأجهزة
  Widget _buildDevicesList(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        for (int i = 0; i < _filteredDevices.length; i++)
          _buildDeviceCard(_filteredDevices[i], i, colorScheme, isDark),
      ],
    );
  }

  // بناء بطاقة جهاز واحد
  Widget _buildDeviceCard(
    Map<String, dynamic> device,
    int index,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    final details = device['details'] as Map<String, dynamic>?;
    final isConnected = device['status'] == 'متصل';

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 0,
        color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  Container(
                    width: 56,
                    height: 56,
                    decoration: BoxDecoration(
                      color: isConnected
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.router,
                      size: 28,
                      color: isConnected ? Colors.green : Colors.red,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          // أولوية العرض: الاسم المخصص، ثم اسم الجهاز من التفاصيل، ثم IP
                          device['custom_name']?.isNotEmpty == true
                              ? device['custom_name']
                              : (details != null &&
                                        details['Device Name'] != null &&
                                        details['Device Name'] != '-'
                                    ? details['Device Name']
                                    : device['ip'] ?? ''),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        // عرض IP الجهاز عندما يكون هناك اسم مخصص
                        if (device['custom_name']?.isNotEmpty == true)
                          Text(
                            device['ip'] ?? '',
                            style: TextStyle(
                              fontSize: 14,
                              color: colorScheme.onSurface.withValues(
                                alpha: 0.7,
                              ),
                              fontFamily: 'monospace',
                            ),
                          ),
                        if (device['custom_name']?.isNotEmpty == true)
                          const SizedBox(height: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: isConnected
                                ? Colors.green.withValues(alpha: 0.1)
                                : Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            device['status'] ?? 'غير معروف',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isConnected ? Colors.green : Colors.red,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // تفاصيل الجهاز
              _buildDeviceDetails(device, details, colorScheme),

              const SizedBox(height: 16),

              // أزرار الإجراءات
              _buildActionButtons(device, colorScheme),
            ],
          ),
        ),
      ),
    );
  }

  // بناء تفاصيل الجهاز
  Widget _buildDeviceDetails(
    Map<String, dynamic> device,
    Map<String, dynamic>? details,
    ColorScheme colorScheme,
  ) {
    if (details != null && details['error'] == null) {
      final keys = [
        'Device Model',
        'Device Name',
        'Version',
        'Uptime',
        'LAN0',
        'Signal Strength',
        'Transmit CCQ',
        'ESSID',
        'Channel',
        'Frequency',
        'AP MAC',
        'TX Rate',
        'RX Rate',
        'Security',
        'CPU Load',
        'Free RAM',
        'Total RAM',
      ];
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: colorScheme.surface.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (final k in keys)
              if (details[k] != null && details[k].toString() != '-')
                _buildDetailRow(k, details[k].toString(), colorScheme),
            _buildDetailRow(
              'آخر ظهور',
              _formatLastSeen(device['lastSeen']),
              colorScheme,
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow(
              'IP',
              device['ip'] ?? '',
              colorScheme,
              isError: true,
            ),
            _buildDetailRow(
              'الحالة',
              device['status'] ?? '-',
              colorScheme,
              isError: true,
            ),
            _buildDetailRow(
              'آخر ظهور',
              device['lastSeen'] != null
                  ? _formatLastSeen(device['lastSeen'])
                  : '-',
              colorScheme,
              isError: true,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.error, color: Colors.red, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    details?['error'] ?? 'تعذر جلب بيانات الجهاز',
                    style: const TextStyle(color: Colors.red, fontSize: 12),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }
  }

  // بناء صف تفاصيل
  Widget _buildDetailRow(
    String label,
    String value,
    ColorScheme colorScheme, {
    bool isError = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: isError
                    ? Colors.red
                    : colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: isError ? Colors.red : colorScheme.onSurface,
                fontFamily: label == 'IP' || label.contains('LAN')
                    ? 'monospace'
                    : null,
              ),
              textDirection: label == 'IP' || label.contains('LAN')
                  ? TextDirection.ltr
                  : TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  // بناء أزرار الإجراءات
  Widget _buildActionButtons(
    Map<String, dynamic> device,
    ColorScheme colorScheme,
  ) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showEditDeviceDialog(device),
                icon: const Icon(Icons.edit, size: 18),
                label: const Text('تعديل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  foregroundColor: Colors.blue,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showDeviceInfoDialog(device),
                icon: const Icon(Icons.info, size: 18),
                label: const Text('معلومات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple.withValues(alpha: 0.1),
                  foregroundColor: Colors.purple,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            // زر اختبار الاتصال
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _testDeviceConnection(device),
                icon: const Icon(Icons.wifi_find, size: 18),
                label: const Text('اتصال'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  foregroundColor: Colors.blue,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // زر إعادة التشغيل
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showRebootDialog(device),
                icon: const Icon(Icons.restart_alt, size: 18),
                label: const Text('إعادة تشغيل'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.withValues(alpha: 0.1),
                  foregroundColor: Colors.orange,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _showDeleteDialog(device),
                icon: const Icon(Icons.delete, size: 18),
                label: const Text('حذف'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.withValues(alpha: 0.1),
                  foregroundColor: Colors.red,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // عرض حوار تعديل الجهاز
  void _showEditDeviceDialog(Map<String, dynamic> device) {
    final nameController = TextEditingController(
      text: device['custom_name'] ?? '',
    );
    final ipController = TextEditingController(text: device['ip'] ?? '');
    final userController = TextEditingController(text: device['user'] ?? '');
    final passController = TextEditingController(text: device['pass'] ?? '');
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              colors: [
                colorScheme.primary.withValues(alpha: 0.1),
                colorScheme.surface,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // رأس الحوار
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.edit,
                        color: Colors.blue,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'تعديل جهاز',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // حقول الإدخال
                _buildModernTextField(
                  controller: nameController,
                  label: 'اسم الجهاز',
                  icon: Icons.devices,
                ),
                const SizedBox(height: 16),
                _buildModernTextField(
                  controller: ipController,
                  label: 'IP Address',
                  icon: Icons.language,
                  keyboardType: TextInputType.text,
                  textDirection: TextDirection.ltr,
                ),
                const SizedBox(height: 16),
                _buildModernTextField(
                  controller: userController,
                  label: 'اسم المستخدم',
                  icon: Icons.person,
                ),
                const SizedBox(height: 16),
                _buildModernTextField(
                  controller: passController,
                  label: 'كلمة المرور',
                  icon: Icons.lock,
                  obscureText: true,
                ),
                const SizedBox(height: 24),

                // أزرار الإجراء
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('إلغاء'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _handleEditDevice(
                          device,
                          nameController.text.trim(),
                          ipController.text.trim(),
                          userController.text.trim(),
                          passController.text.trim(),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: const Text('حفظ'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // معالجة تعديل الجهاز
  Future<void> _handleEditDevice(
    Map<String, dynamic> device,
    String name,
    String ip,
    String user,
    String pass,
  ) async {
    if (ip.isEmpty || user.isEmpty || pass.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    Navigator.of(context).pop();
    setState(() => _loadingDevice = true);

    try {
      // تحديث البيانات الأساسية فقط بدون اختبار الاتصال
      final updateData = <String, dynamic>{
        'custom_name': name.isNotEmpty ? name : null, // اسم مخصص من المستخدم
        'ip': ip,
        'user': user,
        'pass': pass,
        'lastSeen': DateTime.now().toIso8601String(),
      };

      final id = device['id'];
      if (id != null) {
        // تحويل id إلى int إذا كان string
        final deviceId = id is int ? id : int.tryParse(id.toString());
        if (deviceId != null) {
          await DBHelper.instance.updateDevice(deviceId, updateData);
          await _loadDevices();

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 12),
                    Text('تم تحديث الجهاز بنجاح'),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            );
          }
        } else {
          throw Exception('معرف الجهاز غير صالح');
        }
      } else {
        throw Exception('معرف الجهاز غير موجود');
      }
    } catch (e) {
      print('[ERROR] خطأ في تحديث الجهاز: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(child: Text('خطأ في تحديث الجهاز: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loadingDevice = false);
      }
    }
  }

  // بناء حقل إدخال عصري
  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    TextDirection? textDirection,
    bool obscureText = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: Icon(icon),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
        ),
        keyboardType: keyboardType,
        textDirection: textDirection,
        obscureText: obscureText,
      ),
    );
  }

  // عرض حوار إعادة التشغيل
  void _showRebootDialog(Map<String, dynamic> device) {
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              colors: [
                Colors.orange.withValues(alpha: 0.1),
                colorScheme.surface,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس الحوار
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.restart_alt,
                  color: Colors.orange,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              Text(
                'تأكيد إعادة التشغيل',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    Text(
                      'هل أنت متأكد أنك تريد إعادة تشغيل الجهاز؟',
                      style: TextStyle(
                        fontSize: 16,
                        color: colorScheme.onSurface,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'IP: ${device['ip']}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                        fontFamily: 'monospace',
                      ),
                      textDirection: TextDirection.ltr,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // أزرار الإجراء
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey[600],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _handleRebootDevice(device),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('إعادة تشغيل'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // معالجة إعادة تشغيل الجهاز
  Future<void> _handleRebootDevice(Map<String, dynamic> device) async {
    Navigator.of(context).pop();

    try {
      await _rebootDevice(device['ip']);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 12),
                Text('تم إرسال أمر إعادة التشغيل للجهاز'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة تشغيل الجهاز: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // عرض حوار حذف الجهاز
  void _showDeleteDialog(Map<String, dynamic> device) {
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              colors: [Colors.red.withValues(alpha: 0.1), colorScheme.surface],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // رأس الحوار
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.delete_forever,
                  color: Colors.red,
                  size: 40,
                ),
              ),
              const SizedBox(height: 20),

              Text(
                'تأكيد الحذف',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),

              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.warning, color: Colors.red, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'تحذير',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'هل أنت متأكد أنك تريد حذف هذا الجهاز؟\nلا يمكن التراجع عن هذه العملية.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.red.shade700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'IP: ${device['ip']}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                        fontFamily: 'monospace',
                      ),
                      textDirection: TextDirection.ltr,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),

              // أزرار الإجراء
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey[600],
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _handleDeleteDevice(device),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('حذف نهائياً'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // معالجة حذف الجهاز
  Future<void> _handleDeleteDevice(Map<String, dynamic> device) async {
    Navigator.of(context).pop();

    try {
      final id = device['id'];
      if (id != null) {
        // تحويل id إلى int إذا كان string
        final deviceId = id is int ? id : int.tryParse(id.toString());
        if (deviceId != null) {
          await _deleteDevice(deviceId);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.white),
                    SizedBox(width: 12),
                    Text('تم حذف الجهاز بنجاح'),
                  ],
                ),
                backgroundColor: Colors.green,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            );
          }
        } else {
          throw Exception('معرف الجهاز غير صالح');
        }
      } else {
        throw Exception('معرف الجهاز غير موجود');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الجهاز: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // عرض حوار معلومات الجهاز التشخيصية
  void _showDeviceInfoDialog(Map<String, dynamic> device) {
    final colorScheme = Theme.of(context).colorScheme;
    final details = device['details'] as Map<String, dynamic>?;
    final ip = device['ip'] ?? 'غير محدد';

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            gradient: LinearGradient(
              colors: [
                Colors.purple.withValues(alpha: 0.1),
                colorScheme.surface,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // رأس الحوار
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.purple.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.info,
                        color: Colors.purple,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'معلومات الجهاز التشخيصية',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // معلومات الاتصال
                _buildInfoSection('معلومات الاتصال', Icons.network_check, [
                  _buildInfoRow('عنوان IP', ip, isMonospace: true),
                  _buildInfoRow('اسم المستخدم', device['user'] ?? 'غير محدد'),
                  _buildInfoRow('الحالة', device['status'] ?? 'غير محدد'),
                  _buildInfoRow(
                    'آخر ظهور',
                    device['lastSeen'] != null
                        ? (device['lastSeen'] as DateTime)
                              .toLocal()
                              .toString()
                              .substring(0, 19)
                        : 'غير محدد',
                  ),
                ], colorScheme),
                const SizedBox(height: 16),

                // معلومات الجهاز
                if (details != null && details['error'] == null) ...[
                  _buildInfoSection('معلومات الجهاز', Icons.router, [
                    _buildInfoRow(
                      'اسم الجهاز',
                      details['Device Name'] ?? 'غير محدد',
                    ),
                    _buildInfoRow(
                      'الموديل',
                      details['Device Model'] ?? 'غير محدد',
                    ),
                    _buildInfoRow('الإصدار', details['Version'] ?? 'غير محدد'),
                    _buildInfoRow(
                      'وقت التشغيل',
                      details['Uptime'] ?? 'غير محدد',
                    ),
                    _buildInfoRow(
                      'LAN0',
                      details['LAN0'] ?? 'غير محدد',
                      isMonospace: true,
                    ),
                    _buildInfoRow(
                      'قوة الإشارة',
                      details['Signal Strength'] ?? 'غير محدد',
                    ),
                    _buildInfoRow(
                      'Transmit CCQ',
                      details['Transmit CCQ'] ?? 'غير محدد',
                    ),
                  ], colorScheme),
                  const SizedBox(height: 16),
                ],

                // نصائح استكشاف الأخطاء
                _buildTroubleshootingSection(ip, colorScheme),
                const SizedBox(height: 20),

                // زر الإغلاق
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('إغلاق'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء قسم معلومات
  Widget _buildInfoSection(
    String title,
    IconData icon,
    List<Widget> children,
    ColorScheme colorScheme,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: Colors.purple, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...children,
        ],
      ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(String label, String value, {bool isMonospace = false}) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                color: colorScheme.onSurface,
                fontFamily: isMonospace ? 'monospace' : null,
              ),
              textDirection: isMonospace
                  ? TextDirection.ltr
                  : TextDirection.rtl,
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم نصائح استكشاف الأخطاء
  Widget _buildTroubleshootingSection(String ip, ColorScheme colorScheme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.troubleshoot, color: Colors.orange, size: 20),
              const SizedBox(width: 8),
              Text(
                'نصائح استكشاف الأخطاء',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // نصائح الشبكة
          _buildTroubleshootingItem(
            Icons.network_check,
            'فحص الاتصال بالشبكة',
            'تأكد من أن الجهاز متصل بنفس الشبكة',
            colorScheme,
          ),

          _buildTroubleshootingItem(
            Icons.network_ping,
            'اختبار Ping',
            'جرب ping $ip من الطرفية للتأكد من الوصول',
            colorScheme,
          ),

          _buildTroubleshootingItem(
            Icons.router,
            'إعدادات الراوتر',
            'تحقق من إعدادات الجدار الناري والمنافذ',
            colorScheme,
          ),

          _buildTroubleshootingItem(
            Icons.power,
            'حالة الجهاز',
            'تأكد من أن الجهاز مشغل ويعمل بشكل طبيعي',
            colorScheme,
          ),

          _buildTroubleshootingItem(
            Icons.settings_ethernet,
            'كابل الشبكة',
            'فحص كابل الإيثرنت والاتصالات الفيزيائية',
            colorScheme,
          ),

          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.info, color: Colors.blue, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'المنفذ المستخدم: 45004 (HTTP)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // بناء عنصر نصيحة استكشاف الأخطاء
  Widget _buildTroubleshootingItem(
    IconData icon,
    String title,
    String description,
    ColorScheme colorScheme,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: Colors.orange, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 11,
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
