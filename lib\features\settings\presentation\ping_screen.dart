import 'package:dart_ping/dart_ping.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';

class PingScreen extends StatefulWidget {
  const PingScreen({Key? key}) : super(key: key);

  @override
  State<PingScreen> createState() => _PingScreenState();
}

class _PingScreenState extends State<PingScreen> {
  final TextEditingController _ipController = TextEditingController();
  String? _result;
  bool _loading = false;
  String? _error;
  StreamSubscription? _pingSubscription;
  final List<String> _recentIps = [];
  static const String _recentIpsKey = 'recent_ping_ips';

  // تحميل الخوادم المحفوظة من SharedPreferences
  Future<void> _loadRecentIps() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedIps = prefs.getStringList(_recentIpsKey) ?? [];
      setState(() {
        _recentIps.clear();
        _recentIps.addAll(savedIps);
      });
    } catch (e) {
      debugPrint('خطأ في تحميل الخوادم المحفوظة: $e');
    }
  }

  // حفظ الخوادم في SharedPreferences
  Future<void> _saveRecentIps() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(_recentIpsKey, _recentIps);
    } catch (e) {
      debugPrint('خطأ في حفظ الخوادم: $e');
    }
  }

  Future<void> _doPing() async {
    final ip = _ipController.text.trim();
    if (ip.isEmpty) {
      setState(() {
        _error = 'يرجى إدخال عنوان IP أو اسم النطاق أولاً';
        _result = null;
      });
      return;
    }
    // حفظ الإدخال في القائمة وحفظه في SharedPreferences
    if (!_recentIps.contains(ip)) {
      setState(() {
        _recentIps.insert(0, ip);
        if (_recentIps.length > 5) _recentIps.removeLast();
      });
      // حفظ القائمة المحدثة
      await _saveRecentIps();
    } else {
      // إذا كان موجود، انقله إلى المقدمة
      setState(() {
        _recentIps.remove(ip);
        _recentIps.insert(0, ip);
      });
      await _saveRecentIps();
    }
    setState(() {
      _loading = true;
      _result = null;
      _error = null;
    });
    try {
      final ping = Ping(ip, count: 4, timeout: 2);
      final buffer = StringBuffer();
      _pingSubscription = ping.stream.listen(
        (event) {
          if (event.error != null) {
            buffer.writeln('خطأ: ${event.error}');
          } else if (event.response != null) {
            buffer.writeln(
              'Reply from ${event.response!.ip}: time=${event.response!.time?.inMilliseconds ?? '?'}ms',
            );
          } else if (event.summary != null) {
            buffer.writeln('\n--- Ping summary ---');
            final summary = event.summary!;
            buffer.writeln(
              'Sent: ${summary.transmitted}, Received: ${summary.received}',
            );
          }
          setState(() {
            _result = buffer.toString();
          });
        },
        onDone: () {
          setState(() {
            _loading = false;
          });
          _pingSubscription = null;
        },
        onError: (e) {
          setState(() {
            _error = 'حدث خطأ أثناء تنفيذ ping: $e';
            _loading = false;
          });
          _pingSubscription = null;
        },
      );
    } catch (e) {
      setState(() {
        _error = 'حدث خطأ أثناء تنفيذ ping: $e';
        _loading = false;
      });
      _pingSubscription = null;
    }
  }

  void _stopPing() {
    _pingSubscription?.cancel();
    _pingSubscription = null;
    setState(() {
      _loading = false;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadRecentIps();
  }

  @override
  void dispose() {
    _pingSubscription?.cancel();
    _ipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // بطاقة الإدخال
                  _buildInputCard(colorScheme, isDark),
                  const SizedBox(height: 24),

                  // بطاقة النتائج
                  _buildResultsCard(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.18),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withValues(
              alpha: isDark ? 0.08 : 0.18,
            ),
            child: Icon(
              Icons.wifi_tethering,
              color: colorScheme.primary,
              size: 54,
            ),
          ),
        ),
        // عنوان الشاشة
        Text(
          'اختبار الاتصال',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'اختبر سرعة الاتصال مع الخوادم',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء بطاقة الإدخال
  Widget _buildInputCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.dns, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'عنوان الخادم',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // حقل الإدخال العصري
            Container(
              decoration: BoxDecoration(
                color: colorScheme.surface.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                ),
              ),
              child: TextField(
                controller: _ipController,
                keyboardType: TextInputType.text,
                textDirection: TextDirection.ltr,
                style: TextStyle(fontSize: 16, color: colorScheme.onSurface),
                decoration: InputDecoration(
                  labelText: 'أدخل عنوان IP أو اسم النطاق',
                  labelStyle: TextStyle(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  prefixIcon: Icon(Icons.language, color: colorScheme.primary),
                  suffixIcon: _ipController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear, color: colorScheme.outline),
                          onPressed: () {
                            setState(() {
                              _ipController.clear();
                            });
                          },
                        )
                      : null,
                ),
                onSubmitted: (_) => _doPing(),
              ),
            ),

            const SizedBox(height: 20),

            // قسم الخوادم المستخدمة
            if (_recentIps.isNotEmpty) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'خوادم مستخدمة:',
                    style: TextStyle(
                      fontSize: 14,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      setState(() {
                        _recentIps.clear();
                      });
                      await _saveRecentIps();
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: colorScheme.error.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colorScheme.error.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.clear_all,
                            size: 12,
                            color: colorScheme.error,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'مسح الكل',
                            style: TextStyle(
                              fontSize: 10,
                              color: colorScheme.error,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _recentIps
                    .take(2)
                    .map((ip) => _buildRecentServerChip(ip, colorScheme))
                    .toList(),
              ),
              const SizedBox(height: 20),
            ],

            Text(
              'خوادم شائعة:',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildQuickServerChip('*******', 'Google DNS', colorScheme),
                _buildQuickServerChip('*******', 'Cloudflare', colorScheme),
                _buildQuickServerChip('google.com', 'Google', colorScheme),
                _buildQuickServerChip('facebook.com', 'Facebook', colorScheme),
              ],
            ),

            const SizedBox(height: 24),

            // زر البدء/الإيقاف
            SizedBox(
              width: double.infinity,
              child: _loading
                  ? ElevatedButton.icon(
                      icon: const Icon(Icons.stop, size: 20),
                      label: const Text('إيقاف الاختبار'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      onPressed: _stopPing,
                    )
                  : ElevatedButton.icon(
                      icon: const Icon(Icons.wifi_tethering, size: 20),
                      label: const Text('بدء الاختبار'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                      ),
                      onPressed: _ipController.text.trim().isNotEmpty
                          ? _doPing
                          : null,
                    ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء شريحة خادم سريع
  Widget _buildQuickServerChip(
    String server,
    String label,
    ColorScheme colorScheme,
  ) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _ipController.text = server;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              server,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
                fontFamily: 'monospace',
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // حذف خادم من القائمة المحفوظة
  Future<void> _removeRecentIp(String ip) async {
    setState(() {
      _recentIps.remove(ip);
    });
    await _saveRecentIps();
  }

  // بناء شريحة خادم مستخدم مؤخراً
  Widget _buildRecentServerChip(String server, ColorScheme colorScheme) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _ipController.text = server;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: colorScheme.secondary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colorScheme.secondary.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.history, size: 16, color: colorScheme.secondary),
            const SizedBox(width: 6),
            Text(
              server,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: colorScheme.secondary,
                fontFamily: 'monospace',
              ),
            ),
            const SizedBox(width: 6),
            GestureDetector(
              onTap: () => _removeRecentIp(server),
              child: Icon(
                Icons.close,
                size: 14,
                color: colorScheme.secondary.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة النتائج
  Widget _buildResultsCard(ColorScheme colorScheme, bool isDark) {
    if (_error == null && _result == null) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _error != null ? Icons.error : Icons.check_circle,
                  color: _error != null ? Colors.red : Colors.green,
                ),
                const SizedBox(width: 12),
                Text(
                  _error != null ? 'خطأ في الاختبار' : 'نتائج الاختبار',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            if (_error != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Text(
                  _error!,
                  style: const TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),

            if (_result != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.surface.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result!,
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                      color: colorScheme.onSurface,
                    ),
                    textDirection: TextDirection.ltr,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
