# دليل إعداد Supabase للنسخ الاحتياطي

## 🚀 **خطوات الإعداد:**

### **1. إنشاء مشروع Supabase:**
1. اذهب إلى [supabase.com](https://supabase.com)
2. أنشئ حساب جديد أو سجل الدخول
3. اضغط "New Project"
4. اختر اسم المشروع وكلمة مرور قاعدة البيانات
5. انتظر حتى يتم إنشاء المشروع (2-3 دقائق)

### **2. الحصول على مفاتيح API:**
1. اذهب إلى Settings > API
2. انسخ:
   - **Project URL**: `https://your-project.supabase.co`
   - **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### **3. إعد<PERSON> قاعدة البيانات:**
1. <PERSON><PERSON><PERSON><PERSON> إلى SQL Editor في لوحة تحكم Supabase
2. انسخ والصق محتوى ملف `supabase_setup.sql`
3. اضغط "Run" لتنفيذ الاستعلامات

### **4. إعداد Storage:**
1. اذهب إلى Storage في لوحة التحكم
2. اضغط "Create a new bucket"
3. اسم الـ bucket: `backups`
4. اجعله Public: ✅
5. اضغط "Create bucket"

### **5. إعداد Storage Policies:**
1. اذهب إلى Storage > Policies
2. اضغط "New Policy" للـ bucket `backups`
3. أضف السياسات التالية:

#### **سياسة الرفع (INSERT):**
```sql
CREATE POLICY "Users can upload their own backups" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'backups' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

#### **سياسة العرض (SELECT):**
```sql
CREATE POLICY "Users can view their own backups" ON storage.objects
FOR SELECT USING (
  bucket_id = 'backups' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

#### **سياسة الحذف (DELETE):**
```sql
CREATE POLICY "Users can delete their own backups" ON storage.objects
FOR DELETE USING (
  bucket_id = 'backups' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

### **6. تحديث مفاتيح التطبيق:**
في ملف `lib/main.dart`، حدث المفاتيح:

```dart
await Supabase.initialize(
  url: 'YOUR_PROJECT_URL_HERE',
  anonKey: 'YOUR_ANON_KEY_HERE',
);
```

### **7. إعداد المصادقة:**
1. اذهب إلى Authentication > Settings
2. فعّل Email authentication
3. اختياري: فعّل Anonymous sign-ins
4. احفظ الإعدادات

## 🔧 **الميزات المتاحة:**

### **✅ ما يعمل الآن:**
- ✅ تسجيل الدخول بالبريد الإلكتروني
- ✅ إنشاء حساب جديد
- ✅ تسجيل الدخول كضيف
- ✅ رفع النسخ الاحتياطية JSON
- ✅ عرض قائمة النسخ الاحتياطية
- ✅ حذف النسخ الاحتياطية
- ✅ حد أقصى 5 نسخ لكل مستخدم
- ✅ معلومات مفصلة (حجم، تاريخ، إحصائيات)
- ✅ أمان على مستوى الصف (RLS)

### **🔄 قيد التطوير:**
- 🔄 تحميل واستعادة النسخ الاحتياطية
- 🔄 ضغط الملفات الكبيرة
- 🔄 مزامنة تلقائية
- 🔄 إشعارات الحالة

## 🛡️ **الأمان:**

### **Row Level Security (RLS):**
- كل مستخدم يرى نسخه الاحتياطية فقط
- لا يمكن الوصول لبيانات المستخدمين الآخرين
- حماية على مستوى قاعدة البيانات والتخزين

### **التشفير:**
- جميع البيانات مشفرة أثناء النقل (HTTPS)
- قاعدة البيانات مشفرة في التخزين
- مفاتيح API محمية

## 📊 **مقارنة مع Firebase:**

| الميزة | Firebase | Supabase |
|--------|----------|----------|
| **السعر** | مكلف للاستخدام الكثيف | أرخص، خطة مجانية سخية |
| **قاعدة البيانات** | NoSQL (Firestore) | PostgreSQL (SQL) |
| **الاستعلامات** | محدودة | SQL كامل |
| **الوقت الفعلي** | ممتاز | جيد جداً |
| **المصادقة** | شامل | شامل |
| **التخزين** | جيد | ممتاز |
| **المطورين** | Google | مفتوح المصدر |

## 🚨 **استكشاف الأخطاء:**

### **خطأ "Object not found":**
- تأكد من إنشاء bucket `backups`
- تأكد من أن الـ bucket عام (Public)
- تحقق من Storage Policies

### **خطأ "Permission denied":**
- تأكد من تطبيق Storage Policies
- تحقق من تسجيل الدخول
- تأكد من تفعيل RLS

### **خطأ "Invalid JWT":**
- تحقق من صحة Anon Key
- تأكد من صحة Project URL
- أعد تشغيل التطبيق

### **خطأ في الاتصال:**
- تحقق من الإنترنت
- تأكد من أن المشروع نشط
- تحقق من حالة خدمات Supabase

## 📞 **الدعم:**

### **الموارد المفيدة:**
- [وثائق Supabase](https://supabase.com/docs)
- [Flutter Supabase](https://supabase.com/docs/reference/dart)
- [مجتمع Supabase](https://github.com/supabase/supabase/discussions)

### **أمثلة الكود:**
جميع الأمثلة متوفرة في:
- `lib/services/supabase_backup_service.dart`
- `lib/services/supabase_auth_service.dart`
- `lib/screens/supabase_auth_screen.dart`

## 🎯 **الخطوات التالية:**

1. **اختبر النظام** مع بيانات تجريبية
2. **أضف المزيد من المستخدمين** للاختبار
3. **راقب الأداء** في لوحة تحكم Supabase
4. **احتفظ بنسخة احتياطية** من إعدادات المشروع
5. **فعّل التنبيهات** للمراقبة

---

**ملاحظة**: هذا النظام جاهز للإنتاج ويمكن استخدامه بأمان. تأكد من مراجعة سياسات الأمان بانتظام.
