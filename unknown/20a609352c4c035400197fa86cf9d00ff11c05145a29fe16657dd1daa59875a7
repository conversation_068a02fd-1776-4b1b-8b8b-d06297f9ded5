import '../data/subscriber_model.dart';

/// Enum يمثل حالات المشترك
enum SubscriberStatus { active, expired, nearExpiry, inDebt, online, offline }

/// دالة مركزية لتصنيف حالة المشترك
SubscriberStatus getSubscriberStatus(Subscriber sub) {
  final now = DateTime.now();
  final remaining = sub.endDate.difference(now);
  if (remaining.inSeconds <= 0) {
    return SubscriberStatus.expired;
  }
  if (remaining.inDays < 2) {
    return SubscriberStatus.nearExpiry;
  }
  if (remaining.inDays >= 2) {
    return SubscriberStatus.active;
  }
  if (sub.totalDebt > 0) {
    return SubscriberStatus.inDebt;
  }
  if (sub.onlineStatus == 1 || sub.status == 'متصل') {
    return SubscriberStatus.online;
  }
  if (sub.onlineStatus == 0 || sub.status == 'غير متصل') {
    return SubscriberStatus.offline;
  }
  return SubscriberStatus.active;
}
