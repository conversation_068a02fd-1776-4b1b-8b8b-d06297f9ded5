-- إصلاح قاعدة بيانات Supabase للتطبيق
-- يجب تشغيل هذا السكريبت في Supabase SQL Editor

-- 1. إنشاء جدول user_accounts إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.user_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    display_name TEXT,
    account_status TEXT DEFAULT 'trial' CHECK (account_status IN ('trial', 'active', 'expired', 'banned', 'suspended', 'locked')),
    subscription_type TEXT,
    trial_start TIMESTAMPTZ DEFAULT NOW(),
    trial_end TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
    subscription_start TIMESTAMPTZ,
    subscription_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- فهرس فريد على user_id
    UNIQUE(user_id)
);

-- 2. إنشاء جدول user_devices إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.user_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    device_name TEXT,
    device_type TEXT DEFAULT 'unknown',
    platform TEXT DEFAULT 'unknown',
    is_primary BOOLEAN DEFAULT FALSE,
    last_seen TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- فهرس فريد على user_id + device_id
    UNIQUE(user_id, device_id)
);

-- 3. إنشاء جدول active_sessions إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.active_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    device_id TEXT NOT NULL,
    session_token TEXT,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- إضافة foreign key constraint للربط مع user_devices
    FOREIGN KEY (user_id, device_id) REFERENCES public.user_devices(user_id, device_id) ON DELETE CASCADE
);

-- 4. إنشاء جدول daily_stats إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.daily_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    logins_count INTEGER DEFAULT 0,
    sync_count INTEGER DEFAULT 0,
    data_usage_mb DECIMAL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- فهرس فريد على user_id + date
    UNIQUE(user_id, date)
);

-- 5. إنشاء جدول app_updates إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.app_updates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    version_name TEXT NOT NULL,
    version_code INTEGER NOT NULL,
    release_notes TEXT,
    download_url TEXT,
    is_forced BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    min_supported_version INTEGER,
    database_migration_required BOOLEAN DEFAULT FALSE,
    features TEXT[],
    bug_fixes TEXT[],
    security_updates TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- فهرس فريد على version_code
    UNIQUE(version_code)
);

-- 6. إضافة الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON public.user_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_accounts_status ON public.user_accounts(account_status);
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON public.user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON public.user_devices(device_id);
CREATE INDEX IF NOT EXISTS idx_active_sessions_user_id ON public.active_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_active_sessions_expires ON public.active_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_daily_stats_user_date ON public.daily_stats(user_id, date);

-- 7. إنشاء triggers لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- تطبيق trigger على جميع الجداول
DROP TRIGGER IF EXISTS update_user_accounts_updated_at ON public.user_accounts;
CREATE TRIGGER update_user_accounts_updated_at
    BEFORE UPDATE ON public.user_accounts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_devices_updated_at ON public.user_devices;
CREATE TRIGGER update_user_devices_updated_at
    BEFORE UPDATE ON public.user_devices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_active_sessions_updated_at ON public.active_sessions;
CREATE TRIGGER update_active_sessions_updated_at
    BEFORE UPDATE ON public.active_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_daily_stats_updated_at ON public.daily_stats;
CREATE TRIGGER update_daily_stats_updated_at
    BEFORE UPDATE ON public.daily_stats
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 8. إعداد Row Level Security (RLS)
ALTER TABLE public.user_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.active_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_stats ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان - المستخدم يمكنه الوصول لبياناته فقط
DROP POLICY IF EXISTS "Users can view own account" ON public.user_accounts;
CREATE POLICY "Users can view own account" ON public.user_accounts
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage own devices" ON public.user_devices;
CREATE POLICY "Users can manage own devices" ON public.user_devices
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage own sessions" ON public.active_sessions;
CREATE POLICY "Users can manage own sessions" ON public.active_sessions
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view own stats" ON public.daily_stats;
CREATE POLICY "Users can view own stats" ON public.daily_stats
    FOR ALL USING (auth.uid() = user_id);

-- 9. إنشاء functions مساعدة
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.active_sessions 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_server_time()
RETURNS JSON AS $$
BEGIN
    RETURN json_build_object('server_time', NOW()::TEXT);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. منح الصلاحيات
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;

-- تأكيد إنجاز الإعداد
SELECT 'تم إعداد قاعدة البيانات بنجاح!' as status;
