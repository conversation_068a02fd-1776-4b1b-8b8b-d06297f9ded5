import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة الإحصائيات المتقدمة - متوافقة مع النظام الجديد
class StatsService {
  /// جلب الإحصائيات الشخصية للمستخدم (آمن)
  static Future<Map<String, dynamic>?> getMyPersonalStats() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('⚠️ [STATS] لا يوجد مستخدم مسجل دخول');
        return <String, dynamic>{};
      }

      debugPrint('📊 [STATS] جلب الإحصائيات الشخصية للمستخدم: ${user.id}');

      // جلب الإحصائيات الشخصية فقط
      final response = await Supabase.instance.client.rpc(
        'get_user_personal_stats',
        params: {'user_id': user.id},
      );

      if (response != null) {
        debugPrint('✅ [STATS] تم جلب الإحصائيات الشخصية بنجاح');
        return Map<String, dynamic>.from(response as Map);
      }

      // إرجاع إحصائيات فارغة إذا لم توجد بيانات
      return <String, dynamic>{
        'my_sessions_today': 0,
        'my_total_sessions': 0,
        'my_devices_count': 0,
        'my_account_age_days': 0,
      };
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب الإحصائيات الشخصية: $e');

      return <String, dynamic>{
        'error_occurred': true,
        'error_message': e.toString(),
      };
    }
  }

  /// جلب الإحصائيات الفورية (محدود للاستخدام الآمن)
  @Deprecated('استخدم getMyPersonalStats() بدلاً من ذلك للأمان')
  static Future<Map<String, dynamic>?> getLiveStats() async {
    // إعادة توجيه للدالة الآمنة
    return await getMyPersonalStats();
  }

  /// جلب الإحصائيات اليومية
  static Future<List<Map<String, dynamic>>> getDailyStats({
    int days = 30,
  }) async {
    try {
      debugPrint('📊 [STATS] جلب الإحصائيات اليومية لآخر $days يوم...');

      final response = await Supabase.instance.client
          .from('daily_stats')
          .select('*')
          .gte(
            'date',
            DateTime.now()
                .subtract(Duration(days: days))
                .toIso8601String()
                .split('T')[0],
          )
          .order('date', ascending: false);

      debugPrint('✅ [STATS] تم جلب ${response.length} سجل إحصائيات يومية');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب الإحصائيات اليومية: $e');
      return [];
    }
  }

  /// تحديث الإحصائيات اليومية
  static Future<bool> updateDailyStats() async {
    try {
      debugPrint('🔄 [STATS] تحديث الإحصائيات اليومية...');

      await Supabase.instance.client.rpc('update_daily_stats');

      debugPrint('✅ [STATS] تم تحديث الإحصائيات اليومية بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في تحديث الإحصائيات اليومية: $e');
      return false;
    }
  }

  /// جلب إحصائيات المستخدمين حسب الحالة
  static Future<Map<String, int>> getUserStatusStats() async {
    try {
      debugPrint('👥 [STATS] جلب إحصائيات حالة المستخدمين...');

      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('account_status')
          .not('account_status', 'is', null);

      final stats = <String, int>{};
      for (final row in response) {
        final status = row['account_status'] as String;
        stats[status] = (stats[status] ?? 0) + 1;
      }

      debugPrint('✅ [STATS] تم جلب إحصائيات حالة المستخدمين: $stats');
      return stats;
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب إحصائيات حالة المستخدمين: $e');
      return {};
    }
  }

  /// جلب إحصائيات الاشتراكات
  static Future<Map<String, int>> getSubscriptionStats() async {
    try {
      debugPrint('💳 [STATS] جلب إحصائيات الاشتراكات...');

      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('subscription_type')
          .not('subscription_type', 'is', null);

      final stats = <String, int>{};
      for (final row in response) {
        final type = row['subscription_type'] as String;
        stats[type] = (stats[type] ?? 0) + 1;
      }

      debugPrint('✅ [STATS] تم جلب إحصائيات الاشتراكات: $stats');
      return stats;
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب إحصائيات الاشتراكات: $e');
      return {};
    }
  }

  /// جلب إحصائيات الأجهزة
  static Future<Map<String, dynamic>> getDeviceStats() async {
    try {
      debugPrint('📱 [STATS] جلب إحصائيات الأجهزة...');

      // إحصائيات أنواع الأجهزة
      final deviceTypesResponse = await Supabase.instance.client
          .from('user_devices')
          .select('device_type')
          .eq('device_status', 'active');

      final deviceTypes = <String, int>{};
      for (final row in deviceTypesResponse) {
        final type = row['device_type'] as String;
        deviceTypes[type] = (deviceTypes[type] ?? 0) + 1;
      }

      // إحصائيات حالة الأجهزة
      final deviceStatusResponse = await Supabase.instance.client
          .from('user_devices')
          .select('device_status');

      final deviceStatus = <String, int>{};
      for (final row in deviceStatusResponse) {
        final status = row['device_status'] as String;
        deviceStatus[status] = (deviceStatus[status] ?? 0) + 1;
      }

      final stats = {
        'device_types': deviceTypes,
        'device_status': deviceStatus,
        'total_devices':
            deviceTypesResponse.length +
            (deviceStatusResponse.length - deviceTypesResponse.length),
      };

      debugPrint('✅ [STATS] تم جلب إحصائيات الأجهزة: $stats');
      return stats;
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب إحصائيات الأجهزة: $e');
      return {};
    }
  }

  /// جلب إحصائيات أكواد التفعيل
  static Future<Map<String, dynamic>> getActivationCodeStats() async {
    try {
      debugPrint('🎫 [STATS] جلب إحصائيات أكواد التفعيل...');

      // إحصائيات أنواع الأكواد
      final codeTypesResponse = await Supabase.instance.client
          .from('activation_codes')
          .select('code_type, code_status');

      final codeTypes = <String, int>{};
      final codeStatus = <String, int>{};

      for (final row in codeTypesResponse) {
        final type = row['code_type'] as String;
        final status = row['code_status'] as String;

        codeTypes[type] = (codeTypes[type] ?? 0) + 1;
        codeStatus[status] = (codeStatus[status] ?? 0) + 1;
      }

      final stats = {
        'code_types': codeTypes,
        'code_status': codeStatus,
        'total_codes': codeTypesResponse.length,
      };

      debugPrint('✅ [STATS] تم جلب إحصائيات أكواد التفعيل: $stats');
      return stats;
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب إحصائيات أكواد التفعيل: $e');
      return {};
    }
  }

  /// جلب إحصائيات الجلسات النشطة
  static Future<Map<String, dynamic>> getActiveSessionStats() async {
    try {
      debugPrint('🔄 [STATS] جلب إحصائيات الجلسات النشطة...');

      final now = DateTime.now();
      final fiveMinutesAgo = now.subtract(const Duration(minutes: 5));

      // الجلسات النشطة (آخر heartbeat خلال 5 دقائق)
      final activeSessionsResponse = await Supabase.instance.client
          .from('active_sessions')
          .select('user_id, device_id, last_heartbeat')
          .eq('is_active', true)
          .gte('last_heartbeat', fiveMinutesAgo.toIso8601String());

      // إجمالي الجلسات اليوم
      final todaySessionsResponse = await Supabase.instance.client
          .from('active_sessions')
          .select('id')
          .gte('session_start', now.toIso8601String().split('T')[0]);

      final stats = {
        'active_sessions': activeSessionsResponse.length,
        'unique_active_users': activeSessionsResponse
            .map((s) => s['user_id'])
            .toSet()
            .length,
        'sessions_today': todaySessionsResponse.length,
      };

      debugPrint('✅ [STATS] تم جلب إحصائيات الجلسات النشطة: $stats');
      return stats;
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب إحصائيات الجلسات النشطة: $e');
      return {};
    }
  }

  /// جلب إحصائيات شاملة
  static Future<Map<String, dynamic>> getComprehensiveStats() async {
    try {
      debugPrint('📈 [STATS] جلب الإحصائيات الشاملة...');

      final results = await Future.wait([
        getMyPersonalStats(),
        getUserStatusStats(),
        getSubscriptionStats(),
        getDeviceStats(),
        getActivationCodeStats(),
        getActiveSessionStats(),
      ]);

      final comprehensiveStats = {
        'live_stats': results[0],
        'user_status_stats': results[1],
        'subscription_stats': results[2],
        'device_stats': results[3],
        'activation_code_stats': results[4],
        'session_stats': results[5],
        'last_updated': DateTime.now().toIso8601String(),
      };

      debugPrint('✅ [STATS] تم جلب الإحصائيات الشاملة بنجاح');
      return comprehensiveStats;
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب الإحصائيات الشاملة: $e');
      return {};
    }
  }

  /// جلب تقرير النشاط للمستخدم
  static Future<List<Map<String, dynamic>>> getUserActivityReport(
    String userId, {
    int days = 7,
  }) async {
    try {
      debugPrint('📋 [STATS] جلب تقرير النشاط للمستخدم: $userId');

      final response = await Supabase.instance.client
          .from('activity_log')
          .select('*')
          .eq('user_id', userId)
          .gte(
            'created_at',
            DateTime.now().subtract(Duration(days: days)).toIso8601String(),
          )
          .order('created_at', ascending: false);

      debugPrint('✅ [STATS] تم جلب ${response.length} سجل نشاط');
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في جلب تقرير النشاط: $e');
      return [];
    }
  }

  /// تصدير الإحصائيات كـ CSV
  static String exportStatsToCSV(Map<String, dynamic> stats) {
    try {
      final buffer = StringBuffer();

      // رأس الملف
      buffer.writeln('Metric,Value,Timestamp');

      // إضافة البيانات
      final timestamp = DateTime.now().toIso8601String();

      void addStatsToCSV(Map<String, dynamic> data, String prefix) {
        data.forEach((key, value) {
          if (value is Map) {
            addStatsToCSV(Map<String, dynamic>.from(value), '$prefix$key.');
          } else {
            buffer.writeln('$prefix$key,$value,$timestamp');
          }
        });
      }

      addStatsToCSV(stats, '');

      debugPrint('✅ [STATS] تم تصدير الإحصائيات كـ CSV');
      return buffer.toString();
    } catch (e) {
      debugPrint('❌ [STATS] خطأ في تصدير الإحصائيات: $e');
      return '';
    }
  }
}
