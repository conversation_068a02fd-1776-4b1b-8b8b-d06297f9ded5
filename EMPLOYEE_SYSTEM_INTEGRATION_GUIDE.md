# دليل تكامل نظام الموظفين - iTower

## نظرة عامة

تم تطوير نظام شامل لإدارة الموظفين والصلاحيات في تطبيق iTower. النظام يدعم دورين فقط:
- **المدير**: له كامل الصلاحيات ويدير الموظفين
- **الموظف**: صلاحيات محددة من قبل المدير

## الملفات المُنشأة والمُحدثة

### 1. النماذج (Models)
- `lib/models/employee_models.dart` - نماذج البيانات للموظفين والصلاحيات

### 2. الخدمات (Services)
- `lib/services/employee_service.dart` - خدمة إدارة الموظفين والمصادقة
- `lib/services/activity_logger.dart` - خدمة تسجيل نشاط الموظفين

### 3. الشاشات (Screens)
- `lib/screens/employee_login_screen.dart` - شاشة تسجيل دخول الموظفين
- `lib/screens/employee_management_screen.dart` - شاشة إدارة الموظفين (للمدير)
- `lib/screens/activity_log_screen.dart` - شاشة عرض سجل النشاط

### 4. المكونات (Widgets)
- `lib/widgets/permission_guard.dart` - مكونات حماية الصلاحيات

### 5. قاعدة البيانات
- `lib/db_helper.dart` - تم تحديثها لدعم جداول الموظفين (الإصدار 4)

### 6. أمثلة التكامل
- `lib/examples/employee_integration_example.dart` - أمثلة على كيفية الاستخدام

## كيفية التكامل مع التطبيق الحالي

### 1. إضافة قائمة إدارة الموظفين للمدير

في الشاشة الرئيسية أو قائمة الإعدادات، أضف:

```dart
// في الشاشة الرئيسية
PermissionGuard(
  permission: PermissionType.manageEmployees,
  child: ListTile(
    leading: Icon(Icons.people),
    title: Text('إدارة الموظفين'),
    onTap: () {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => EmployeeManagementScreen(),
        ),
      );
    },
  ),
),
```

### 2. إضافة خيار تسجيل دخول الموظفين

في شاشة تسجيل الدخول الرئيسية:

```dart
TextButton(
  onPressed: () {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EmployeeLoginScreen(),
      ),
    );
  },
  child: Text('تسجيل دخول الموظف'),
),
```

### 3. حماية العمليات بالصلاحيات

لحماية أي عملية، استخدم `PermissionGuard`:

```dart
PermissionGuard(
  permission: PermissionType.addSubscribers,
  child: FloatingActionButton(
    onPressed: _addSubscriber,
    child: Icon(Icons.add),
  ),
),
```

أو استخدم `PermissionButton` للأزرار:

```dart
PermissionButton(
  permission: PermissionType.exportData,
  onPressed: _exportData,
  icon: Icons.download,
  label: 'تصدير البيانات',
),
```

### 4. تسجيل نشاط الموظفين

في كل عملية يقوم بها الموظف، أضف تسجيل النشاط:

```dart
// عند إضافة مشترك
await ActivityLogger.logAddSubscriber(
  subscriberName: subscriberName,
  subscriberId: subscriberId,
);

// عند تجديد اشتراك
await ActivityLogger.logRenewSubscription(
  subscriberName: subscriberName,
  packageName: packageName,
  amount: amount,
);

// عند تسديد دين
await ActivityLogger.logPayDebt(
  subscriberName: subscriberName,
  amount: amount,
  remainingDebt: remainingDebt,
);
```

### 5. عرض معلومات الموظف الحالي

في أي شاشة، يمكن عرض معلومات الموظف:

```dart
AppBar(
  title: Text('اسم الشاشة'),
  actions: [
    CurrentEmployeeInfo(),
    SizedBox(width: 16),
  ],
),
```

### 6. إضافة سجل النشاط للقوائم

```dart
ListTile(
  leading: Icon(Icons.history),
  title: Text('سجل النشاط'),
  onTap: () {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ActivityLogScreen(),
      ),
    );
  },
),
```

## الصلاحيات المتاحة

```dart
enum PermissionType {
  // إدارة المشتركين
  viewSubscribers,      // عرض المشتركين
  addSubscribers,       // إضافة مشتركين
  editSubscribers,      // تعديل المشتركين
  deleteSubscribers,    // حذف المشتركين
  
  // إدارة المعاملات
  viewTransactions,     // عرض المعاملات
  addTransactions,      // إضافة معاملات
  editTransactions,     // تعديل المعاملات
  deleteTransactions,   // حذف المعاملات
  
  // العمليات المالية
  renewSubscriptions,   // تجديد الاشتراكات
  payDebts,            // تسديد الديون
  
  // إدارة الأجهزة
  viewDevices,         // عرض الأجهزة
  manageDevices,       // إدارة الأجهزة
  
  // التقارير والبيانات
  viewReports,         // عرض التقارير
  exportData,          // تصدير البيانات
  createBackup,        // إنشاء نسخ احتياطية
  
  // إدارة النظام
  manageEmployees,     // إدارة الموظفين (للمدير فقط)
  viewSettings,        // عرض الإعدادات
  editSettings,        // تعديل الإعدادات
}
```

## خطوات التفعيل

### 1. تحديث قاعدة البيانات
عند تشغيل التطبيق لأول مرة بعد التحديث، ستتم ترقية قاعدة البيانات تلقائياً من الإصدار 3 إلى 4.

### 2. إنشاء أول موظف
المدير يمكنه إنشاء حسابات الموظفين من خلال شاشة إدارة الموظفين.

### 3. تسجيل دخول الموظفين
الموظفون يسجلون الدخول باستخدام البريد الإلكتروني وكلمة المرور.

### 4. تتبع النشاط
جميع عمليات الموظفين تُسجل تلقائياً في سجل النشاط.

## الأمان

- كلمات المرور مُشفرة باستخدام SHA-256
- الصلاحيات محفوظة بشكل آمن في قاعدة البيانات
- جلسات الموظفين محمية ومؤقتة
- تسجيل شامل لجميع العمليات

## ملاحظات مهمة

1. **النسخ الاحتياطية**: نظام المزامنة الحالي لا يتأثر بنظام الموظفين
2. **البيانات الحالية**: جميع البيانات الموجودة محفوظة ولن تتأثر
3. **الترقية التلقائية**: قاعدة البيانات تترقى تلقائياً عند التشغيل
4. **المرونة**: يمكن إضافة صلاحيات جديدة بسهولة في المستقبل

## استكشاف الأخطاء

### مشكلة: لا تظهر شاشة إدارة الموظفين
**الحل**: تأكد من أن المستخدم في وضع المدير وله صلاحية `manageEmployees`

### مشكلة: لا يمكن تسجيل دخول الموظف
**الحل**: تأكد من صحة البريد الإلكتروني وكلمة المرور، وأن الحساب مُفعل

### مشكلة: لا تظهر الأزرار المحمية بالصلاحيات
**الحل**: تأكد من أن الموظف له الصلاحية المطلوبة

## الدعم المستقبلي

النظام مُصمم ليكون قابلاً للتوسع:
- إضافة صلاحيات جديدة
- تطوير أدوار إضافية
- تحسين واجهة المستخدم
- إضافة ميزات أمان متقدمة
