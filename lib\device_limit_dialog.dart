import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'features/main_home_screen.dart';
import 'supabase_login_screen.dart';

class DeviceLimitDialog extends StatelessWidget {
  final String existingUserId;

  const DeviceLimitDialog({Key? key, required this.existingUserId})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return AlertDialog(
      backgroundColor: colorScheme.surface,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 28),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'حساب موجود مسبقاً',
              style: TextStyle(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'يوجد حساب آخر مرتبط بهذا الجهاز.',
                style: TextStyle(color: colorScheme.onSurface, fontSize: 16),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.orange,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'سياسة الجهاز الواحد',
                          style: TextStyle(
                            color: Colors.orange,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'لأسباب أمنية، يُسمح بحساب واحد فقط لكل جهاز.',
                      style: TextStyle(
                        color: colorScheme.onSurface.withValues(alpha: 0.8),
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'الخيارات المتاحة:',
                style: TextStyle(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 8),
              _buildClickableOption(
                context,
                Icons.login,
                'تسجيل الدخول للحساب الموجود',
                'استخدم بيانات الحساب المرتبط بهذا الجهاز',
                colorScheme,
                () => _handleLoginOption(context),
              ),
              const SizedBox(height: 8),
              _buildClickableOption(
                context,
                Icons.phone_android,
                'استخدام جهاز آخر',
                'أنشئ الحساب الجديد من جهاز مختلف',
                colorScheme,
                () => _handleUseAnotherDevice(context),
              ),
              const SizedBox(height: 8),
              _buildClickableOption(
                context,
                Icons.support_agent,
                'التواصل مع الدعم',
                'للحصول على مساعدة إضافية',
                colorScheme,
                () => _handleContactSupport(context),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => _handleContinueAnyway(context),
          child: Text(
            'استمرار',
            style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold),
          ),
        ),
      ],
    );
  }

  /// بناء خيار قابل للنقر
  Widget _buildClickableOption(
    BuildContext context,
    IconData icon,
    String title,
    String subtitle,
    ColorScheme colorScheme,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
        child: Row(
          children: [
            Icon(icon, color: colorScheme.primary, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: colorScheme.onSurface.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// معالج تسجيل الدخول
  void _handleLoginOption(BuildContext context) {
    Navigator.of(context).pop();

    // تسجيل خروج المستخدم الحالي
    Supabase.instance.client.auth.signOut();

    // الانتقال لشاشة تسجيل الدخول مع إزالة جميع الشاشات السابقة
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const SupabaseLoginScreen()),
      (route) => false,
    );
  }

  /// معالج استخدام جهاز آخر
  void _handleUseAnotherDevice(BuildContext context) {
    Navigator.of(context).pop();

    // إظهار إشعار وإغلاق التطبيق
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('يرجى استخدام جهاز آخر لإنشاء حساب جديد'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 3),
      ),
    );

    // إغلاق التطبيق بعد ثانيتين
    Future.delayed(const Duration(seconds: 2), () {
      SystemNavigator.pop();
    });
  }

  /// معالج التواصل مع الدعم
  void _handleContactSupport(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التواصل مع الدعم'),
        content: const Text('اختر طريقة التواصل:'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _makePhoneCall(context);
            },
            child: const Text('اتصال'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _sendWhatsAppMessage(context);
            },
            child: const Text('إرسال رسالة'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  /// إجراء مكالمة هاتفية
  void _makePhoneCall(BuildContext context) async {
    try {
      final supportPhone = MainHomeScreen.supportPhone;
      final phoneUri = Uri.parse('tel:$supportPhone');
      final launched = await launchUrl(phoneUri);

      if (!launched) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تعذر فتح تطبيق الهاتف'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الاتصال: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إرسال رسالة واتساب
  void _sendWhatsAppMessage(BuildContext context) async {
    try {
      final supportPhone = MainHomeScreen.supportPhone;
      final msg =
          'مرحباً، أحتاج إلى دعم فني في تطبيق iTower بخصوص مشكلة في الحساب.';
      final whatsappUrl = Uri.parse(
        'https://wa.me/$supportPhone?text=${Uri.encodeComponent(msg)}',
      );

      final launched = await launchUrl(
        whatsappUrl,
        mode: LaunchMode.externalApplication,
      );

      if (!launched) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تعذر فتح واتساب. تأكد من وجود التطبيق وصحة الرقم.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تعذر إرسال رسالة واتساب: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// معالج الاستمرار رغم التحذير
  void _handleContinueAnyway(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير'),
        content: const Text('الاستمرار يخرق قواعد التطبيق'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // إغلاق حوار التحذير
              Navigator.of(context).pop(); // إغلاق الحوار الأساسي
              _showAccountSuspendedDialog(context);
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// إظهار شاشة تحذيرية بإيقاف الحساب - تصميم عدواني
  void _showAccountSuspendedDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false, // منع الإغلاق بزر الرجوع
        child: Dialog(
          backgroundColor: Colors.transparent,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.85,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.red.shade900,
                    Colors.red.shade700,
                    Colors.black87,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.red.shade400, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.withValues(alpha: 0.5),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.8),
                    blurRadius: 40,
                    spreadRadius: 10,
                  ),
                ],
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة تحذيرية متحركة
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade800,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.red.shade300,
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withValues(alpha: 0.6),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.security,
                        color: Colors.white,
                        size: 36,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // عنوان عدواني
                    Text(
                      '⚠️ انتهاك أمني مكتشف ⚠️',
                      style: TextStyle(
                        color: Colors.red.shade100,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.0,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 12),

                    // رسالة التحذير الرئيسية
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.red.shade400,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'تم ايقاف الحساب بسبب الدمج 😈',
                            style: TextStyle(
                              color: Colors.red.shade200,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 12),

                    // تفاصيل الانتهاك
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.red.shade900.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Colors.red.shade500,
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.gavel,
                                color: Colors.red.shade300,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'انتهاك سياسة الجهاز الواحد',
                                style: TextStyle(
                                  color: Colors.red.shade200,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Row(
                            children: [
                              Icon(
                                Icons.schedule,
                                color: Colors.orange.shade300,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'تاريخ الانتهاك: ${DateTime.now().toString().substring(0, 19)}',
                                style: TextStyle(
                                  color: Colors.orange.shade200,
                                  fontSize: 11,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 6),
                          Row(
                            children: [
                              Icon(
                                Icons.warning,
                                color: Colors.yellow.shade400,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'الإجراء: إيقاف فوري',
                                style: TextStyle(
                                  color: Colors.yellow.shade300,
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 16),

                    // زر الإغلاق العدواني
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).pop();
                          SystemNavigator.pop(); // إغلاق التطبيق
                        },
                        icon: const Icon(
                          Icons.exit_to_app,
                          color: Colors.white,
                        ),
                        label: const Text(
                          'إغلاق التطبيق',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red.shade800,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: Colors.red.shade400,
                              width: 2,
                            ),
                          ),
                          elevation: 8,
                          shadowColor: Colors.red.withValues(alpha: 0.5),
                        ),
                      ),
                    ),

                    const SizedBox(height: 8),

                    // رسالة تحذيرية إضافية
                    Text(
                      'تم تسجيل هذا الانتهاك في سجلات الأمان',
                      style: TextStyle(
                        color: Colors.red.shade300,
                        fontSize: 10,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
