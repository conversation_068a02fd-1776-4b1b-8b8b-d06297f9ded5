import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'server_model.dart';

class ServerStorage {
  Future<List<ServerModel>> loadServers() async {
    final prefs = await SharedPreferences.getInstance();
    final serversJson = prefs.getStringList('servers') ?? [];
    return serversJson.map((e) => ServerModel.fromJson(jsonDecode(e))).toList();
  }

  Future<void> saveServers(List<ServerModel> servers) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> serversJson = servers
        .map((server) => jsonEncode(server.toJson()))
        .toList();
    await prefs.setStringList('servers', serversJson);
  }
}
