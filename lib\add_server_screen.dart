import 'package:flutter/material.dart';

class AddServerScreen extends StatefulWidget {
  final Map<String, dynamic>? initialServer;
  const AddServerScreen({super.key, this.initialServer});
  @override
  State<AddServerScreen> createState() => _AddServerScreenState();
}

class _AddServerScreenState extends State<AddServerScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController ipController = TextEditingController();
  final TextEditingController userController = TextEditingController();
  final TextEditingController passController = TextEditingController();
  bool passVisible = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialServer != null) {
      nameController.text = widget.initialServer!['name'] ?? '';
      ipController.text = widget.initialServer!['ip'] ?? '';
      userController.text = widget.initialServer!['user'] ?? '';
      passController.text = widget.initialServer!['pass'] ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEdit = widget.initialServer != null;
    return Scaffold(
      appBar: AppBar(
        title: Text(isEdit ? 'تعديل السيرفر' : 'إضافة سيرفر جديد'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              controller: nameController,
              textDirection: TextDirection.ltr,
              decoration: InputDecoration(
                labelText: 'اسم السيرفر',
                prefixIcon: const Icon(Icons.label),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.text,
            ),
            const SizedBox(height: 18),
            TextField(
              controller: ipController,
              textDirection: TextDirection.ltr,
              decoration: InputDecoration(
                labelText: 'IP السيرفر',
                prefixIcon: const Icon(Icons.dns),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.text,
            ),
            const SizedBox(height: 18),
            TextField(
              controller: userController,
              textDirection: TextDirection.ltr,
              decoration: InputDecoration(
                labelText: 'اسم المستخدم',
                prefixIcon: const Icon(Icons.person),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              keyboardType: TextInputType.text,
            ),
            const SizedBox(height: 18),
            TextField(
              controller: passController,
              textDirection: TextDirection.ltr,
              obscureText: !passVisible,
              decoration: InputDecoration(
                labelText: 'كلمة المرور',
                prefixIcon: const Icon(Icons.lock),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                suffixIcon: IconButton(
                  icon: Icon(
                    passVisible ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed: () => setState(() => passVisible = !passVisible),
                ),
              ),
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade700,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                onPressed: () {
                  if (nameController.text.isEmpty ||
                      ipController.text.isEmpty ||
                      userController.text.isEmpty ||
                      passController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('يرجى تعبئة جميع الحقول')),
                    );
                    return;
                  }
                  Navigator.of(context).pop({
                    'name': nameController.text.trim(),
                    'ip': ipController.text.trim(),
                    'user': userController.text.trim(),
                    'pass': passController.text.trim(),
                  });
                },
                child: Text(
                  isEdit ? 'حفظ التعديلات' : 'حفظ',
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
