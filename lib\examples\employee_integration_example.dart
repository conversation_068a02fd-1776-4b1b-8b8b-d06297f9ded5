// مثال على كيفية دمج نظام الموظفين مع العمليات الموجودة

import 'package:flutter/material.dart';
import '../widgets/permission_guard.dart';
import '../services/activity_logger.dart';
import '../models/employee_models.dart';

/// مثال على شاشة إدارة المشتركين مع نظام الصلاحيات
class SubscribersScreenExample extends StatefulWidget {
  const SubscribersScreenExample({super.key});

  @override
  State<SubscribersScreenExample> createState() =>
      _SubscribersScreenExampleState();
}

class _SubscribersScreenExampleState extends State<SubscribersScreenExample> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المشتركين'),
        actions: [
          // عرض معلومات الموظف الحالي
          const CurrentEmployeeInfo(),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // أزرار العمليات مع حماية الصلاحيات
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // زر إضافة مشترك - يحتاج صلاحية addSubscribers
                PermissionButton(
                  permission: PermissionType.addSubscribers,
                  onPressed: _addSubscriber,
                  icon: Icons.person_add,
                  label: 'إضافة مشترك',
                ),
                const SizedBox(width: 12),

                // زر تصدير البيانات - يحتاج صلاحية exportData
                PermissionButton(
                  permission: PermissionType.exportData,
                  onPressed: _exportData,
                  icon: Icons.download,
                  label: 'تصدير',
                ),
              ],
            ),
          ),

          // قائمة المشتركين
          Expanded(
            child: ListView.builder(
              itemCount: 10, // مثال
              itemBuilder: (context, index) {
                return _buildSubscriberCard(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriberCard(int index) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: const CircleAvatar(child: Icon(Icons.person)),
        title: Text('مشترك ${index + 1}'),
        subtitle: const Text('معلومات المشترك'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // زر التعديل - يحتاج صلاحية editSubscribers
            PermissionGuard(
              permission: PermissionType.editSubscribers,
              child: IconButton(
                onPressed: () => _editSubscriber(index),
                icon: const Icon(Icons.edit),
                tooltip: 'تعديل',
              ),
            ),

            // زر تجديد الاشتراك - يحتاج صلاحية renewSubscriptions
            PermissionGuard(
              permission: PermissionType.renewSubscriptions,
              child: IconButton(
                onPressed: () => _renewSubscription(index),
                icon: const Icon(Icons.refresh),
                tooltip: 'تجديد',
              ),
            ),

            // زر تسديد الدين - يحتاج صلاحية payDebts
            PermissionGuard(
              permission: PermissionType.payDebts,
              child: IconButton(
                onPressed: () => _payDebt(index),
                icon: const Icon(Icons.payment),
                tooltip: 'تسديد دين',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addSubscriber() async {
    // منطق إضافة مشترك
    try {
      // هنا يتم إضافة المشترك الجديد
      final subscriberName = 'مشترك جديد';
      final subscriberId = 'SUB_${DateTime.now().millisecondsSinceEpoch}';

      // تسجيل العملية في سجل النشاط
      await ActivityLogger.logAddSubscriber(
        subscriberName: subscriberName,
        subscriberId: subscriberId,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المشترك بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المشترك: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _editSubscriber(int index) async {
    // منطق تعديل مشترك
    try {
      final subscriberName = 'مشترك ${index + 1}';
      final subscriberId = 'SUB_$index';
      final changes = 'تم تحديث رقم الهاتف والعنوان';

      // تسجيل العملية في سجل النشاط
      await ActivityLogger.logEditSubscriber(
        subscriberName: subscriberName,
        subscriberId: subscriberId,
        changes: changes,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تعديل المشترك بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تعديل المشترك: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _renewSubscription(int index) async {
    // منطق تجديد اشتراك
    try {
      final subscriberName = 'مشترك ${index + 1}';
      const packageName = 'باقة شهرية';
      const amount = 50.0;

      // تسجيل العملية في سجل النشاط
      await ActivityLogger.logRenewSubscription(
        subscriberName: subscriberName,
        packageName: packageName,
        amount: amount,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تجديد الاشتراك بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تجديد الاشتراك: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _payDebt(int index) async {
    // منطق تسديد دين
    try {
      final subscriberName = 'مشترك ${index + 1}';
      const amount = 25.0;
      const remainingDebt = 0.0;

      // تسجيل العملية في سجل النشاط
      await ActivityLogger.logPayDebt(
        subscriberName: subscriberName,
        amount: amount,
        remainingDebt: remainingDebt,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تسديد الدين بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تسديد الدين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportData() async {
    // منطق تصدير البيانات
    try {
      const exportType = 'قائمة المشتركين';
      final fileName =
          'subscribers_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      // تسجيل العملية في سجل النشاط
      await ActivityLogger.logExportData(
        exportType: exportType,
        fileName: fileName,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تصدير البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// مثال على شاشة التقارير مع نظام الصلاحيات
class ReportsScreenExample extends StatelessWidget {
  const ReportsScreenExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقارير'),
        actions: [const CurrentEmployeeInfo(), const SizedBox(width: 16)],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // تقرير الإيرادات - يحتاج صلاحية viewReports
            PermissionListTile(
              permission: PermissionType.viewReports,
              leading: const Icon(Icons.monetization_on),
              title: const Text('تقرير الإيرادات'),
              subtitle: const Text('عرض إيرادات الفترة المحددة'),
              onTap: () => _viewRevenueReport(context),
            ),

            const SizedBox(height: 12),

            // تقرير المشتركين - يحتاج صلاحية viewSubscribers
            PermissionListTile(
              permission: PermissionType.viewSubscribers,
              leading: const Icon(Icons.people),
              title: const Text('تقرير المشتركين'),
              subtitle: const Text('إحصائيات المشتركين والاشتراكات'),
              onTap: () => _viewSubscribersReport(context),
            ),

            const SizedBox(height: 12),

            // تقرير الديون - يحتاج صلاحية viewTransactions
            PermissionListTile(
              permission: PermissionType.viewTransactions,
              leading: const Icon(Icons.account_balance),
              title: const Text('تقرير الديون'),
              subtitle: const Text('المبالغ المستحقة والمسددة'),
              onTap: () => _viewDebtsReport(context),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _viewRevenueReport(BuildContext context) async {
    try {
      const reportType = 'تقرير الإيرادات';
      const dateRange = 'الشهر الحالي';

      // تسجيل العملية في سجل النشاط
      await ActivityLogger.logViewReport(
        reportType: reportType,
        dateRange: dateRange,
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم عرض تقرير الإيرادات'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _viewSubscribersReport(BuildContext context) async {
    try {
      const reportType = 'تقرير المشتركين';
      const dateRange = 'جميع الفترات';

      await ActivityLogger.logViewReport(
        reportType: reportType,
        dateRange: dateRange,
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم عرض تقرير المشتركين'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _viewDebtsReport(BuildContext context) async {
    try {
      const reportType = 'تقرير الديون';
      const dateRange = 'آخر 3 أشهر';

      await ActivityLogger.logViewReport(
        reportType: reportType,
        dateRange: dateRange,
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم عرض تقرير الديون'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
