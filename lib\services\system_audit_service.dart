import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'legacy_cleanup_service.dart';
import 'account_service.dart';
import 'session_service.dart';
import 'stats_service.dart';

/// خدمة مراجعة شاملة للنظام للتأكد من التوافق مع القواعد الجديدة
class SystemAuditService {
  /// إجراء مراجعة شاملة للنظام
  static Future<Map<String, dynamic>> performFullAudit() async {
    final auditResults = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'overall_status': 'unknown',
      'legacy_cleanup': {},
      'database_compatibility': {},
      'services_status': {},
      'recommendations': <String>[],
      'errors': <String>[],
    };

    try {
      debugPrint('🔍 [AUDIT] بدء المراجعة الشاملة للنظام...');

      // 1. فحص البقايا القديمة
      auditResults['legacy_cleanup'] = await _auditLegacyCleanup();

      // 2. فحص توافق قاعدة البيانات
      auditResults['database_compatibility'] =
          await _auditDatabaseCompatibility();

      // 3. فحص حالة الخدمات
      auditResults['services_status'] = await _auditServicesStatus();

      // 4. تحديد الحالة العامة
      auditResults['overall_status'] = _determineOverallStatus(auditResults);

      // 5. إنشاء التوصيات
      auditResults['recommendations'] = _generateRecommendations(auditResults);

      debugPrint('✅ [AUDIT] تم الانتهاء من المراجعة الشاملة');
    } catch (e) {
      auditResults['errors'].add('خطأ عام في المراجعة: $e');
      auditResults['overall_status'] = 'error';
      debugPrint('❌ [AUDIT] خطأ في المراجعة الشاملة: $e');
    }

    return auditResults;
  }

  /// فحص البقايا القديمة
  static Future<Map<String, dynamic>> _auditLegacyCleanup() async {
    final results = <String, dynamic>{
      'status': 'unknown',
      'legacy_data_found': false,
      'cleanup_needed': false,
      'details': {},
    };

    try {
      debugPrint('🧹 [AUDIT] فحص البقايا القديمة...');

      final scanResults = await LegacyCleanupService.scanForLegacyData();

      results['legacy_data_found'] = scanResults['needs_cleanup'] ?? false;
      results['cleanup_needed'] = scanResults['needs_cleanup'] ?? false;
      results['details'] = scanResults;

      if (results['legacy_data_found']) {
        results['status'] = 'needs_cleanup';
      } else {
        results['status'] = 'clean';
      }
    } catch (e) {
      results['status'] = 'error';
      results['error'] = e.toString();
    }

    return results;
  }

  /// فحص توافق قاعدة البيانات
  static Future<Map<String, dynamic>> _auditDatabaseCompatibility() async {
    final results = <String, dynamic>{
      'status': 'unknown',
      'new_tables_accessible': false,
      'old_tables_present': false,
      'functions_working': false,
      'details': {},
    };

    try {
      debugPrint('🗄️ [AUDIT] فحص توافق قاعدة البيانات...');

      // فحص الجداول الجديدة
      final newTablesTest = await _testNewTables();
      results['new_tables_accessible'] = newTablesTest['success'] ?? false;
      results['details']['new_tables'] = newTablesTest;

      // فحص الدوال المخصصة
      final functionsTest = await _testCustomFunctions();
      results['functions_working'] = functionsTest['success'] ?? false;
      results['details']['functions'] = functionsTest;

      // تحديد الحالة العامة
      if (results['new_tables_accessible'] && results['functions_working']) {
        results['status'] = 'compatible';
      } else {
        results['status'] = 'incompatible';
      }
    } catch (e) {
      results['status'] = 'error';
      results['error'] = e.toString();
    }

    return results;
  }

  /// فحص حالة الخدمات
  static Future<Map<String, dynamic>> _auditServicesStatus() async {
    final results = <String, dynamic>{
      'status': 'unknown',
      'account_service': {},
      'session_service': {},
      'stats_service': {},
      'details': {},
    };

    try {
      debugPrint('⚙️ [AUDIT] فحص حالة الخدمات...');

      // فحص AccountService
      results['account_service'] = await _testAccountService();

      // فحص SessionService
      results['session_service'] = await _testSessionService();

      // فحص StatsService
      results['stats_service'] = await _testStatsService();

      // تحديد الحالة العامة
      final allServicesWorking = [
        results['account_service']['working'] ?? false,
        results['session_service']['working'] ?? false,
        results['stats_service']['working'] ?? false,
      ].every((working) => working);

      results['status'] = allServicesWorking ? 'working' : 'issues';
    } catch (e) {
      results['status'] = 'error';
      results['error'] = e.toString();
    }

    return results;
  }

  /// اختبار الجداول الجديدة
  static Future<Map<String, dynamic>> _testNewTables() async {
    final results = <String, dynamic>{
      'success': false,
      'accessible_tables': <String>[],
      'errors': <String>[],
    };

    final newTables = [
      'user_accounts',
      'user_devices',
      'active_sessions',
      'activation_codes',
      'activity_log',
      'daily_stats',
    ];

    for (final table in newTables) {
      try {
        await Supabase.instance.client
            .from(table)
            .select('*')
            .count(CountOption.exact);

        results['accessible_tables'].add(table);
      } catch (e) {
        results['errors'].add('خطأ في الوصول للجدول $table: $e');
      }
    }

    results['success'] =
        results['accessible_tables'].length == newTables.length;
    return results;
  }

  /// اختبار الدوال المخصصة
  static Future<Map<String, dynamic>> _testCustomFunctions() async {
    final results = <String, dynamic>{
      'success': false,
      'working_functions': <String>[],
      'errors': <String>[],
    };

    // اختبار get_user_personal_stats
    try {
      await StatsService.getMyPersonalStats();
      results['working_functions'].add('get_user_personal_stats');
    } catch (e) {
      results['errors'].add('خطأ في get_user_personal_stats: $e');
    }

    // اختبار update_daily_stats
    try {
      await StatsService.updateDailyStats();
      results['working_functions'].add('update_daily_stats');
    } catch (e) {
      results['errors'].add('خطأ في update_daily_stats: $e');
    }

    // اختبار cleanup_expired_sessions
    try {
      await SessionService.cleanupExpiredSessions();
      results['working_functions'].add('cleanup_expired_sessions');
    } catch (e) {
      results['errors'].add('خطأ في cleanup_expired_sessions: $e');
    }

    results['success'] = results['working_functions'].length >= 2;
    return results;
  }

  /// اختبار AccountService
  static Future<Map<String, dynamic>> _testAccountService() async {
    final results = <String, dynamic>{
      'working': false,
      'v2_functions_available': false,
      'errors': <String>[],
    };

    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user != null) {
        // اختبار getAccountDataV2
        await AccountService.getAccountDataV2(user.id);
        results['v2_functions_available'] = true;
      }

      results['working'] = true;
    } catch (e) {
      results['errors'].add('خطأ في AccountService: $e');
    }

    return results;
  }

  /// اختبار SessionService
  static Future<Map<String, dynamic>> _testSessionService() async {
    final results = <String, dynamic>{
      'working': false,
      'initialized': false,
      'errors': <String>[],
    };

    try {
      results['initialized'] = SessionService.isInitialized;
      results['working'] = true;
    } catch (e) {
      results['errors'].add('خطأ في SessionService: $e');
    }

    return results;
  }

  /// اختبار StatsService
  static Future<Map<String, dynamic>> _testStatsService() async {
    final results = <String, dynamic>{
      'working': false,
      'can_get_stats': false,
      'errors': <String>[],
    };

    try {
      final stats = await StatsService.getMyPersonalStats();
      results['can_get_stats'] = stats != null;
      results['working'] = true;
    } catch (e) {
      results['errors'].add('خطأ في StatsService: $e');
    }

    return results;
  }

  /// تحديد الحالة العامة
  static String _determineOverallStatus(Map<String, dynamic> auditResults) {
    final legacyStatus = auditResults['legacy_cleanup']['status'];
    final dbStatus = auditResults['database_compatibility']['status'];
    final servicesStatus = auditResults['services_status']['status'];

    if (legacyStatus == 'error' ||
        dbStatus == 'error' ||
        servicesStatus == 'error') {
      return 'error';
    }

    if (legacyStatus == 'needs_cleanup') {
      return 'needs_cleanup';
    }

    if (dbStatus == 'incompatible' || servicesStatus == 'issues') {
      return 'issues';
    }

    if (legacyStatus == 'clean' &&
        dbStatus == 'compatible' &&
        servicesStatus == 'working') {
      return 'excellent';
    }

    return 'good';
  }

  /// إنشاء التوصيات
  static List<String> _generateRecommendations(
    Map<String, dynamic> auditResults,
  ) {
    final recommendations = <String>[];

    final legacyCleanup = auditResults['legacy_cleanup'];
    final dbCompatibility = auditResults['database_compatibility'];
    final servicesStatus = auditResults['services_status'];

    if (legacyCleanup['cleanup_needed'] == true) {
      recommendations.add('🧹 يُنصح بتشغيل تنظيف البقايا القديمة');
    }

    if (dbCompatibility['new_tables_accessible'] == false) {
      recommendations.add(
        '🗄️ تحقق من إعدادات قاعدة البيانات والجداول الجديدة',
      );
    }

    if (dbCompatibility['functions_working'] == false) {
      recommendations.add('⚙️ تحقق من الدوال المخصصة في قاعدة البيانات');
    }

    if (servicesStatus['session_service']['initialized'] == false) {
      recommendations.add('🔄 قم بتهيئة خدمة الجلسات');
    }

    if (recommendations.isEmpty) {
      recommendations.add('✅ النظام يعمل بشكل ممتاز - لا توجد توصيات');
    }

    return recommendations;
  }

  /// إنشاء تقرير مفصل
  static String generateDetailedReport(Map<String, dynamic> auditResults) {
    final buffer = StringBuffer();

    buffer.writeln('📋 تقرير المراجعة الشاملة للنظام');
    buffer.writeln('=' * 50);
    buffer.writeln('التاريخ: ${auditResults['timestamp']}');
    buffer.writeln('الحالة العامة: ${auditResults['overall_status']}');
    buffer.writeln();

    // تفاصيل البقايا القديمة
    final legacyCleanup = auditResults['legacy_cleanup'];
    buffer.writeln('🧹 البقايا القديمة:');
    buffer.writeln('  الحالة: ${legacyCleanup['status']}');
    buffer.writeln('  يحتاج تنظيف: ${legacyCleanup['cleanup_needed']}');
    buffer.writeln();

    // تفاصيل قاعدة البيانات
    final dbCompatibility = auditResults['database_compatibility'];
    buffer.writeln('🗄️ توافق قاعدة البيانات:');
    buffer.writeln('  الحالة: ${dbCompatibility['status']}');
    buffer.writeln(
      '  الجداول الجديدة: ${dbCompatibility['new_tables_accessible']}',
    );
    buffer.writeln('  الدوال المخصصة: ${dbCompatibility['functions_working']}');
    buffer.writeln();

    // تفاصيل الخدمات
    final servicesStatus = auditResults['services_status'];
    buffer.writeln('⚙️ حالة الخدمات:');
    buffer.writeln('  الحالة العامة: ${servicesStatus['status']}');
    buffer.writeln(
      '  خدمة الحسابات: ${servicesStatus['account_service']['working']}',
    );
    buffer.writeln(
      '  خدمة الجلسات: ${servicesStatus['session_service']['working']}',
    );
    buffer.writeln(
      '  خدمة الإحصائيات: ${servicesStatus['stats_service']['working']}',
    );
    buffer.writeln();

    // التوصيات
    buffer.writeln('💡 التوصيات:');
    for (final recommendation in auditResults['recommendations']) {
      buffer.writeln('  - $recommendation');
    }
    buffer.writeln();

    // الأخطاء
    if (auditResults['errors'].isNotEmpty) {
      buffer.writeln('❌ الأخطاء:');
      for (final error in auditResults['errors']) {
        buffer.writeln('  - $error');
      }
    }

    return buffer.toString();
  }
}
