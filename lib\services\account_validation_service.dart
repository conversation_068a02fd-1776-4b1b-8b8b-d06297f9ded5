import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'account_service.dart';
import 'user_presence_service.dart';

/// خدمة التحقق من صحة الحساب ومعالجة الحسابات المحذوفة
class AccountValidationService {
  static const String _lastValidationKey = 'last_account_validation';
  static const Duration _validationInterval = Duration(minutes: 5);

  /// فحص صحة الحساب الحالي
  static Future<AccountValidationResult> validateCurrentAccount() async {
    try {
      debugPrint('🔍 [VALIDATION] بدء فحص صحة الحساب...');

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('ℹ️ [VALIDATION] لا يوجد مستخدم مسجل دخول');
        return AccountValidationResult(
          isValid: false,
          reason: AccountInvalidReason.noUser,
          action: ValidationAction.redirectToLogin,
        );
      }

      // فحص صحة الجلسة
      final sessionValid = await _validateSession();
      if (!sessionValid) {
        debugPrint('❌ [VALIDATION] الجلسة غير صالحة');
        return AccountValidationResult(
          isValid: false,
          reason: AccountInvalidReason.invalidSession,
          action: ValidationAction.refreshSessionOrLogout,
        );
      }

      // فحص وجود الحساب في قاعدة البيانات
      final accountExists = await _checkAccountExists(user.id);
      if (!accountExists) {
        debugPrint('❌ [VALIDATION] الحساب محذوف من قاعدة البيانات');
        return AccountValidationResult(
          isValid: false,
          reason: AccountInvalidReason.accountDeleted,
          action: ValidationAction.cleanupAndLogout,
        );
      }

      // فحص صلاحيات الوصول
      final hasAccess = await _checkAccountAccess(user.id);
      if (!hasAccess) {
        debugPrint('❌ [VALIDATION] الحساب محظور أو معطل');
        return AccountValidationResult(
          isValid: false,
          reason: AccountInvalidReason.accountDisabled,
          action: ValidationAction.showDisabledMessage,
        );
      }

      debugPrint('✅ [VALIDATION] الحساب صالح ومتاح');
      await _updateLastValidation();
      
      return AccountValidationResult(
        isValid: true,
        reason: AccountInvalidReason.none,
        action: ValidationAction.none,
      );

    } catch (e) {
      debugPrint('❌ [VALIDATION] خطأ في فحص صحة الحساب: $e');
      
      // في حالة خطأ الشبكة، نعتبر الحساب صالح مؤقت<|im_start|>
      if (_isNetworkError(e)) {
        debugPrint('⚠️ [VALIDATION] خطأ شبكة - اعتبار الحساب صالح مؤقت<|im_start|>');
        return AccountValidationResult(
          isValid: true,
          reason: AccountInvalidReason.networkError,
          action: ValidationAction.none,
        );
      }

      return AccountValidationResult(
        isValid: false,
        reason: AccountInvalidReason.validationError,
        action: ValidationAction.cleanupAndLogout,
      );
    }
  }

  /// فحص صحة الجلسة
  static Future<bool> _validateSession() async {
    try {
      final session = Supabase.instance.client.auth.currentSession;
      if (session == null) return false;

      // فحص انتهاء صلاحية التوكن
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
      final now = DateTime.now();
      
      if (expiresAt.isBefore(now)) {
        debugPrint('⚠️ [VALIDATION] الجلسة منتهية الصلاحية - محاولة التجديد');
        
        try {
          await Supabase.instance.client.auth.refreshSession();
          return true;
        } catch (e) {
          debugPrint('❌ [VALIDATION] فشل في تجديد الجلسة: $e');
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('❌ [VALIDATION] خطأ في فحص الجلسة: $e');
      return false;
    }
  }

  /// فحص وجود الحساب في قاعدة البيانات
  static Future<bool> _checkAccountExists(String userId) async {
    try {
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('user_id')
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      debugPrint('❌ [VALIDATION] خطأ في فحص وجود الحساب: $e');
      
      // إذا كان خطأ 404 أو عدم وجود، فالحساب محذوف
      if (e.toString().contains('404') || 
          e.toString().contains('PGRST116') ||
          e.toString().contains('No rows found')) {
        return false;
      }
      
      // للأخطاء الأخرى، نفترض أن الحساب موجود
      return true;
    }
  }

  /// فحص صلاحيات الوصول للحساب
  static Future<bool> _checkAccountAccess(String userId) async {
    try {
      // محاولة قراءة بيانات الحساب
      final accountData = await Supabase.instance.client
          .from('user_accounts')
          .select('is_trial, expiry_millis')
          .eq('user_id', userId)
          .single();

      // فحص إذا كان الحساب منتهي الصلاحية
      final expiryMillis = accountData['expiry_millis'] ?? 0;
      if (expiryMillis > 0) {
        final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryMillis);
        final isExpired = expiryDate.isBefore(DateTime.now());
        
        if (isExpired) {
          debugPrint('⚠️ [VALIDATION] الحساب منتهي الصلاحية');
          // يمكن السماح بالوصول مع تحذير
          return true;
        }
      }

      return true;
    } catch (e) {
      debugPrint('❌ [VALIDATION] خطأ في فحص صلاحيات الوصول: $e');
      
      // إذا كان خطأ في الصلاحيات، فالحساب محظور
      if (e.toString().contains('403') || 
          e.toString().contains('Forbidden') ||
          e.toString().contains('insufficient_privilege')) {
        return false;
      }
      
      return true;
    }
  }

  /// تنظيف البيانات المحلية والخروج
  static Future<void> cleanupAndLogout() async {
    try {
      debugPrint('🧹 [VALIDATION] بدء تنظيف البيانات المحلية...');

      // إيقاف خدمة تتبع الحالة
      await UserPresenceService.disconnect();

      // تنظيف البيانات المحلية
      final prefs = await SharedPreferences.getInstance();
      final keysToRemove = prefs.getKeys().where((key) => 
        key.startsWith('user_') || 
        key.startsWith('cached_') ||
        key.startsWith('account_') ||
        key.contains('device_id') ||
        key.contains('session')
      ).toList();

      for (final key in keysToRemove) {
        await prefs.remove(key);
        debugPrint('🗑️ [VALIDATION] تم حذف: $key');
      }

      // تنظيف كاش الحساب
      await AccountService.signOutAndCleanup();

      // تسجيل الخروج من Supabase
      await Supabase.instance.client.auth.signOut();

      debugPrint('✅ [VALIDATION] تم تنظيف البيانات وتسجيل الخروج');

    } catch (e) {
      debugPrint('❌ [VALIDATION] خطأ في تنظيف البيانات: $e');
    }
  }

  /// فحص إذا كان الفحص مطلوب
  static Future<bool> isValidationNeeded() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastValidation = prefs.getInt(_lastValidationKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      
      return (now - lastValidation) > _validationInterval.inMilliseconds;
    } catch (e) {
      return true; // في حالة الخطأ، قم بالفحص
    }
  }

  /// تحديث وقت آخر فحص
  static Future<void> _updateLastValidation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastValidationKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      debugPrint('❌ [VALIDATION] خطأ في تحديث وقت الفحص: $e');
    }
  }

  /// فحص إذا كان الخطأ متعلق بالشبكة
  static bool _isNetworkError(dynamic error) {
    final errorStr = error.toString().toLowerCase();
    return errorStr.contains('network') ||
           errorStr.contains('connection') ||
           errorStr.contains('timeout') ||
           errorStr.contains('socket') ||
           errorStr.contains('unreachable');
  }
}

/// نتيجة فحص صحة الحساب
class AccountValidationResult {
  final bool isValid;
  final AccountInvalidReason reason;
  final ValidationAction action;

  AccountValidationResult({
    required this.isValid,
    required this.reason,
    required this.action,
  });
}

/// أسباب عدم صحة الحساب
enum AccountInvalidReason {
  none,
  noUser,
  invalidSession,
  accountDeleted,
  accountDisabled,
  networkError,
  validationError,
}

/// الإجراءات المطلوبة
enum ValidationAction {
  none,
  redirectToLogin,
  refreshSessionOrLogout,
  cleanupAndLogout,
  showDisabledMessage,
}
