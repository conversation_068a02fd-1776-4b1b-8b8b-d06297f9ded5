import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../db_helper.dart';
import '../data/transaction_model.dart';

class TransactionsLogScreen extends StatefulWidget {
  final int? initialSubscriberId;

  const TransactionsLogScreen({super.key, this.initialSubscriberId});

  @override
  State<TransactionsLogScreen> createState() => _TransactionsLogScreenState();
}

class _TransactionsLogScreenState extends State<TransactionsLogScreen> {
  List<Transaction> _allTransactions = [];
  List<Transaction> _filteredTransactions = [];
  TransactionType? _selectedType;
  DateTime? _startDate;
  DateTime? _endDate;
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  bool _showSearch = false;

  // خريطة لحفظ أسماء المشتركين لتجنب الاستعلامات المتكررة
  final Map<int, String> _subscriberNames = {};

  @override
  void initState() {
    super.initState();
    _loadTransactions();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadTransactions() async {
    setState(() => _isLoading = true);
    try {
      final transactions = await DBHelper.instance.getAllTransactions();

      // تحميل أسماء المشتركين
      await _loadSubscriberNames(transactions);

      setState(() {
        _allTransactions = transactions;
        // تطبيق التصفية الأولية إذا تم تحديد مشترك معين
        if (widget.initialSubscriberId != null) {
          _filteredTransactions = transactions
              .where((t) => t.subscriberId == widget.initialSubscriberId)
              .toList();
        } else {
          _filteredTransactions = transactions;
        }
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _loadSubscriberNames(List<Transaction> transactions) async {
    final subscriberIds = transactions
        .where((t) => t.subscriberId != null)
        .map((t) => t.subscriberId!)
        .toSet();

    for (final id in subscriberIds) {
      if (!_subscriberNames.containsKey(id)) {
        try {
          final subscriber = await DBHelper.instance.getSubscriberById(id);
          if (subscriber != null) {
            _subscriberNames[id] = subscriber.name;
          } else {
            _subscriberNames[id] = 'مشترك محذوف';
          }
        } catch (e) {
          _subscriberNames[id] = 'غير معروف';
        }
      }
    }
  }

  String _getSubscriberDisplayName(int? subscriberId) {
    if (subscriberId == null) return 'غير محدد';
    return _subscriberNames[subscriberId] ?? 'جاري التحميل...';
  }

  void _filterTransactions() {
    setState(() {
      _filteredTransactions = _allTransactions.where((transaction) {
        // فلترة حسب النوع
        if (_selectedType != null && transaction.type != _selectedType) {
          return false;
        }

        // فلترة حسب التاريخ
        if (_startDate != null && transaction.date.isBefore(_startDate!)) {
          return false;
        }
        if (_endDate != null &&
            transaction.date.isAfter(_endDate!.add(const Duration(days: 1)))) {
          return false;
        }

        // فلترة حسب البحث
        final searchText = _searchController.text.toLowerCase();
        if (searchText.isNotEmpty) {
          final subscriberName = transaction.subscriberId != null
              ? _getSubscriberDisplayName(
                  transaction.subscriberId,
                ).toLowerCase()
              : '';

          return transaction.description.toLowerCase().contains(searchText) ||
              (transaction.subscriberId?.toString().contains(searchText) ??
                  false) ||
              subscriberName.contains(searchText);
        }

        return true;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),

                  // شريط البحث
                  if (_showSearch) ...[
                    const SizedBox(height: 16),
                    _buildSearchBar(colorScheme, isDark),
                  ],

                  const SizedBox(height: 32),

                  // محتوى الشاشة
                  _isLoading
                      ? _buildLoadingState(colorScheme)
                      : _filteredTransactions.isEmpty
                      ? _buildEmptyState(colorScheme, isDark)
                      : _buildTransactionsContent(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// الحصول على عنوان الشاشة
  String _getScreenTitle() {
    if (widget.initialSubscriberId != null) {
      final subscriberName = _subscriberNames[widget.initialSubscriberId];
      return subscriberName != null
          ? 'عمليات $subscriberName'
          : 'عمليات المشترك';
    }
    return 'سجل العمليات';
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
      child: Column(
        children: [
          // أزرار الإجراءات في العمود الأيسر
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // العمود الأيسر للأزرار
              Column(
                children: [
                  // زر البحث (الأول في الأعلى)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      icon: Icon(
                        _showSearch ? Icons.close : Icons.search,
                        color: colorScheme.onPrimary,
                      ),
                      tooltip: _showSearch ? 'إغلاق البحث' : 'البحث',
                      onPressed: () {
                        setState(() {
                          _showSearch = !_showSearch;
                          if (!_showSearch) {
                            _searchController.clear();
                            _filterTransactions();
                          }
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 12),

                  // زر التصفية (الثاني)
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: isDark ? 0.1 : 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        color: colorScheme.onPrimary,
                      ),
                      tooltip: 'تصفية العمليات',
                      onPressed: _showFilterDialog,
                    ),
                  ),
                ],
              ),

              // مساحة فارغة للتوازن
              const SizedBox(width: 48),
            ],
          ),
          const SizedBox(height: 0),

          // الأيقونة الرئيسية في الوسط
          Container(
            margin: const EdgeInsets.only(bottom: 18),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.18),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withValues(
                alpha: isDark ? 0.08 : 0.18,
              ),
              child: Icon(Icons.history, color: colorScheme.primary, size: 54),
            ),
          ),

          // العنوان والوصف
          Text(
            _getScreenTitle(),
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
              letterSpacing: 1,
              shadows: [
                Shadow(
                  color: colorScheme.shadow.withValues(alpha: 0.13),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.initialSubscriberId != null
                ? 'عرض العمليات الخاصة بالمشترك'
                : 'تتبع جميع العمليات والأنشطة',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onPrimary.withValues(alpha: 0.92),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),

          // زر الرجوع إذا كان عرض مشترك معين
          if (widget.initialSubscriberId != null) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back),
              label: const Text('رجوع'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                foregroundColor: colorScheme.onPrimary,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // بناء شريط البحث
  Widget _buildSearchBar(ColorScheme colorScheme, bool isDark) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      margin: const EdgeInsets.symmetric(horizontal: 0),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onChanged: (value) => _filterTransactions(),
        style: TextStyle(color: colorScheme.onSurface, fontSize: 16),
        decoration: InputDecoration(
          hintText: 'البحث في العمليات أو أسماء المشتركين...',
          hintStyle: TextStyle(
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          prefixIcon: Icon(
            Icons.search,
            color: colorScheme.onSurface.withValues(alpha: 0.6),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
    );
  }

  // بناء شريط البحث والفلترة
  Widget _buildSearchAndFilter(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // شريط البحث
            TextField(
              controller: _searchController,
              style: TextStyle(color: colorScheme.onSurface, fontSize: 16),
              decoration: InputDecoration(
                hintText: 'البحث في العمليات أو أسماء المشتركين...',
                prefixIcon: Icon(Icons.search, color: colorScheme.primary),
                filled: true,
                fillColor: colorScheme.primary.withValues(alpha: 0.05),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(
                    color: colorScheme.primary.withValues(alpha: 0.2),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(
                    color: colorScheme.primary.withValues(alpha: 0.2),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(16),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
              onChanged: (_) => _filterTransactions(),
            ),

            const SizedBox(height: 16),

            // أزرار الفلترة والتحديث
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _showFilterDialog,
                    icon: const Icon(Icons.filter_list, size: 18),
                    label: const Text('فلترة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary.withValues(
                        alpha: 0.1,
                      ),
                      foregroundColor: colorScheme.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _loadTransactions,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: const Text('تحديث'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.withValues(alpha: 0.1),
                      foregroundColor: Colors.green,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final totalOperations = _filteredTransactions.length;
    final todayOperations = _filteredTransactions.where((t) {
      final today = DateTime.now();
      return t.date.year == today.year &&
          t.date.month == today.month &&
          t.date.day == today.day;
    }).length;

    final typeGroups = <TransactionType, int>{};
    for (final transaction in _filteredTransactions) {
      typeGroups[transaction.type] = (typeGroups[transaction.type] ?? 0) + 1;
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                'إحصائيات سريعة',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                'إجمالي العمليات',
                totalOperations.toString(),
                Icons.list_alt,
              ),
              _buildStatItem(
                'عمليات اليوم',
                todayOperations.toString(),
                Icons.today,
              ),
              _buildStatItem(
                'أنواع العمليات',
                typeGroups.length.toString(),
                Icons.category,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        Icon(icon, color: colorScheme.primary, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.primary,
          ),
        ),
        Text(label, style: textTheme.bodySmall, textAlign: TextAlign.center),
      ],
    );
  }

  Widget _buildTransactionCard(Transaction transaction) {
    final textTheme = Theme.of(context).textTheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getTypeColor(
                    transaction.type,
                  ).withValues(alpha: 0.1),
                  child: Icon(
                    _getTypeIcon(transaction.type),
                    color: _getTypeColor(transaction.type),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        transaction.description,
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getTypeDisplayName(transaction.type),
                        style: textTheme.bodySmall?.copyWith(
                          color: _getTypeColor(transaction.type),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getTypeColor(
                      transaction.type,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    DateFormat('HH:mm').format(transaction.date),
                    style: textTheme.bodySmall?.copyWith(
                      color: _getTypeColor(transaction.type),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  DateFormat('yyyy/MM/dd').format(transaction.date),
                  style: textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
                if (transaction.subscriberId != null) ...[
                  const SizedBox(width: 16),
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _getSubscriberDisplayName(transaction.subscriberId),
                    style: textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(TransactionType type) {
    switch (type) {
      case TransactionType.renewal:
        return Colors.green;
      case TransactionType.payDebt:
        return Colors.blue;
      case TransactionType.addDebt:
        return Colors.orange;
      case TransactionType.addCredit:
        return Colors.purple;
    }
  }

  IconData _getTypeIcon(TransactionType type) {
    switch (type) {
      case TransactionType.renewal:
        return Icons.refresh;
      case TransactionType.payDebt:
        return Icons.payment;
      case TransactionType.addDebt:
        return Icons.add_circle;
      case TransactionType.addCredit:
        return Icons.account_balance_wallet;
    }
  }

  String _getTypeDisplayName(TransactionType type) {
    switch (type) {
      case TransactionType.renewal:
        return 'تجديد اشتراك';
      case TransactionType.payDebt:
        return 'تسديد دين';
      case TransactionType.addDebt:
        return 'إضافة دين';
      case TransactionType.addCredit:
        return 'إضافة رصيد';
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة العمليات'),
        content: StatefulBuilder(
          builder: (context, setDialogState) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // فلترة حسب النوع
              DropdownButtonFormField<TransactionType?>(
                value: _selectedType,
                decoration: const InputDecoration(
                  labelText: 'نوع العملية',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem<TransactionType?>(
                    value: null,
                    child: Text('جميع الأنواع'),
                  ),
                  ...TransactionType.values.map(
                    (type) => DropdownMenuItem(
                      value: type,
                      child: Text(_getTypeDisplayName(type)),
                    ),
                  ),
                ],
                onChanged: (value) =>
                    setDialogState(() => _selectedType = value),
              ),
              const SizedBox(height: 16),

              // فلترة حسب التاريخ
              Row(
                children: [
                  Expanded(
                    child: TextButton.icon(
                      icon: const Icon(Icons.date_range),
                      label: Text(
                        _startDate != null
                            ? DateFormat('yyyy/MM/dd').format(_startDate!)
                            : 'من تاريخ',
                      ),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _startDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setDialogState(() => _startDate = date);
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextButton.icon(
                      icon: const Icon(Icons.date_range),
                      label: Text(
                        _endDate != null
                            ? DateFormat('yyyy/MM/dd').format(_endDate!)
                            : 'إلى تاريخ',
                      ),
                      onPressed: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: _endDate ?? DateTime.now(),
                          firstDate: DateTime(2020),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setDialogState(() => _endDate = date);
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedType = null;
                _startDate = null;
                _endDate = null;
              });
              _filterTransactions();
              Navigator.pop(context);
            },
            child: const Text('إعادة تعيين'),
          ),
          ElevatedButton(
            onPressed: () {
              _filterTransactions();
              Navigator.pop(context);
            },
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  // بناء حالة التحميل
  Widget _buildLoadingState(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: 0.7),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: const Padding(
        padding: EdgeInsets.all(64),
        child: Center(child: CircularProgressIndicator()),
      ),
    );
  }

  // بناء حالة عدم وجود عمليات
  Widget _buildEmptyState(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.history_outlined,
              size: 64,
              color: colorScheme.primary.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد عمليات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'لم يتم تسجيل أي عمليات بعد',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // بناء محتوى العمليات
  Widget _buildTransactionsContent(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // إحصائيات سريعة
        _buildModernQuickStats(colorScheme, isDark),
        const SizedBox(height: 24),

        // قائمة العمليات
        Column(
          children: [
            for (int i = 0; i < _filteredTransactions.length; i++)
              _buildModernTransactionCard(
                _filteredTransactions[i],
                colorScheme,
                isDark,
              ),
          ],
        ),
      ],
    );
  }

  // بناء إحصائيات سريعة عصرية
  Widget _buildModernQuickStats(ColorScheme colorScheme, bool isDark) {
    final totalOperations = _filteredTransactions.length;
    final todayOperations = _filteredTransactions.where((t) {
      final today = DateTime.now();
      return t.date.year == today.year &&
          t.date.month == today.month &&
          t.date.day == today.day;
    }).length;

    // تجميع العمليات حسب النوع
    final typeGroups = <TransactionType, int>{};
    for (final transaction in _filteredTransactions) {
      typeGroups[transaction.type] = (typeGroups[transaction.type] ?? 0) + 1;
    }

    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'إحصائيات سريعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildModernStatItem(
                  'إجمالي العمليات',
                  totalOperations.toString(),
                  Icons.list_alt,
                  colorScheme,
                ),
                _buildModernStatItem(
                  'عمليات اليوم',
                  todayOperations.toString(),
                  Icons.today,
                  colorScheme,
                ),
                _buildModernStatItem(
                  'أنواع العمليات',
                  typeGroups.length.toString(),
                  Icons.category,
                  colorScheme,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء عنصر إحصائية عصري
  Widget _buildModernStatItem(
    String label,
    String value,
    IconData icon,
    ColorScheme colorScheme,
  ) {
    return Expanded(
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, color: colorScheme.primary, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء بطاقة عملية عصرية
  Widget _buildModernTransactionCard(
    Transaction transaction,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    final subscriberName =
        _subscriberNames[transaction.subscriberId] ?? 'غير محدد';
    final typeInfo = _getTransactionTypeInfo(transaction.type);

    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 16),
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                // أيقونة نوع العملية
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: typeInfo['color'].withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    typeInfo['icon'],
                    color: typeInfo['color'],
                    size: 24,
                  ),
                ),

                const SizedBox(width: 16),

                // معلومات العملية
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        typeInfo['title'],
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subscriberName,
                        style: TextStyle(
                          fontSize: 14,
                          color: colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),

                // تاريخ العملية
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(transaction.date),
                    style: TextStyle(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // وصف العملية
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                ),
              ),
              child: Text(
                transaction.description,
                style: TextStyle(
                  fontSize: 14,
                  color: colorScheme.onSurface.withValues(alpha: 0.8),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // معلومات إضافية
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                const SizedBox(width: 6),
                Text(
                  DateFormat('HH:mm').format(transaction.date),
                  style: TextStyle(
                    fontSize: 12,
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: typeInfo['color'].withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: typeInfo['color'].withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    typeInfo['label'],
                    style: TextStyle(
                      color: typeInfo['color'],
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // الحصول على معلومات نوع العملية
  Map<String, dynamic> _getTransactionTypeInfo(TransactionType type) {
    switch (type) {
      case TransactionType.renewal:
        return {
          'title': 'تجديد اشتراك',
          'label': 'تجديد',
          'icon': Icons.refresh,
          'color': Colors.green,
        };
      case TransactionType.payDebt:
        return {
          'title': 'تسديد دين',
          'label': 'تسديد',
          'icon': Icons.payment,
          'color': Colors.blue,
        };
      case TransactionType.addDebt:
        return {
          'title': 'إضافة دين',
          'label': 'إضافة دين',
          'icon': Icons.add_circle,
          'color': Colors.orange,
        };
      case TransactionType.addCredit:
        return {
          'title': 'إضافة رصيد',
          'label': 'إضافة رصيد',
          'icon': Icons.account_balance_wallet,
          'color': Colors.blue,
        };
    }
  }
}
