import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/app_update_service.dart';
import 'package:url_launcher/url_launcher.dart';

class AppUpdateScreen extends StatefulWidget {
  final UpdateInfo updateInfo;
  final bool canSkip;

  const AppUpdateScreen({
    Key? key,
    required this.updateInfo,
    this.canSkip = true,
  }) : super(key: key);

  @override
  State<AppUpdateScreen> createState() => _AppUpdateScreenState();
}

class _AppUpdateScreenState extends State<AppUpdateScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isUpdating = false;
  bool _backupCreated = false;
  double _progress = 0.0;
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutCubic,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: !widget.updateInfo.isForced && !_isUpdating,
      child: Scaffold(
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark
                  ? [
                      colorScheme.primary.withValues(alpha: 0.2),
                      colorScheme.surface,
                    ]
                  : [colorScheme.primary.withValues(alpha: 0.1), Colors.white],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: SafeArea(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: _buildContent(colorScheme, isDark),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Header
          _buildHeader(colorScheme),

          const SizedBox(height: 32),

          // Update Info Card
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildUpdateInfoCard(colorScheme, isDark),

                  if (widget.updateInfo.features.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildFeaturesCard(colorScheme, isDark),
                  ],

                  if (widget.updateInfo.bugFixes.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildBugFixesCard(colorScheme, isDark),
                  ],

                  if (widget.updateInfo.securityUpdates.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    _buildSecurityCard(colorScheme, isDark),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Progress Section
          if (_isUpdating) _buildProgressSection(colorScheme),

          // Action Buttons
          if (!_isUpdating) _buildActionButtons(colorScheme),
        ],
      ),
    );
  }

  Widget _buildHeader(ColorScheme colorScheme) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: widget.updateInfo.isForced
                ? Colors.red.withValues(alpha: 0.1)
                : colorScheme.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            widget.updateInfo.isForced
                ? Icons.system_update_alt
                : Icons.system_update,
            size: 48,
            color: widget.updateInfo.isForced
                ? Colors.red
                : colorScheme.primary,
          ),
        ),

        const SizedBox(height: 16),

        Text(
          widget.updateInfo.isForced ? 'تحديث مطلوب' : 'تحديث متاح',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: widget.updateInfo.isForced
                ? Colors.red
                : colorScheme.primary,
          ),
        ),

        const SizedBox(height: 8),

        Text(
          'الإصدار ${widget.updateInfo.latestVersion}',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildUpdateInfoCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'معلومات التحديث',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),

            const SizedBox(height: 16),

            _buildInfoRow('الإصدار الحالي', widget.updateInfo.currentVersion),
            _buildInfoRow('الإصدار الجديد', widget.updateInfo.latestVersion),

            if (widget.updateInfo.isForced)
              _buildInfoRow('النوع', 'تحديث إجباري', isWarning: true),

            if (widget.updateInfo.databaseMigrationRequired)
              _buildInfoRow('قاعدة البيانات', 'يتطلب ترحيل', isWarning: true),

            if (!widget.updateInfo.isCompatible)
              _buildInfoRow('التوافق', 'غير متوافق', isError: true),

            if (widget.updateInfo.releaseNotes.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'ملاحظات الإصدار:',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                widget.updateInfo.releaseNotes,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    bool isWarning = false,
    bool isError = false,
  }) {
    Color? textColor;
    if (isError) textColor = Colors.red;
    if (isWarning) textColor = Colors.orange;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: textColor,
              fontWeight: isWarning || isError ? FontWeight.bold : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.new_releases, color: Colors.green, size: 20),
                const SizedBox(width: 8),
                Text(
                  'الميزات الجديدة',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...widget.updateInfo.features.map(
              (feature) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(color: Colors.green)),
                    Expanded(child: Text(feature)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBugFixesCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.bug_report, color: Colors.blue, size: 20),
                const SizedBox(width: 8),
                Text(
                  'إصلاحات الأخطاء',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...widget.updateInfo.bugFixes.map(
              (fix) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(color: Colors.blue)),
                    Expanded(child: Text(fix)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: Colors.red, size: 20),
                const SizedBox(width: 8),
                Text(
                  'التحديثات الأمنية',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...widget.updateInfo.securityUpdates.map(
              (update) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(color: Colors.red)),
                    Expanded(child: Text(update)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressSection(ColorScheme colorScheme) {
    return Column(
      children: [
        LinearProgressIndicator(
          value: _progress,
          backgroundColor: colorScheme.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
        ),
        const SizedBox(height: 8),
        Text(
          _statusMessage,
          style: Theme.of(context).textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildActionButtons(ColorScheme colorScheme) {
    return Column(
      children: [
        // Update Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            icon: Icon(
              widget.updateInfo.isForced ? Icons.download : Icons.system_update,
            ),
            label: Text(widget.updateInfo.isForced ? 'تحديث الآن' : 'تحديث'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.updateInfo.isForced
                  ? Colors.red
                  : colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: widget.updateInfo.isCompatible ? _startUpdate : null,
          ),
        ),

        // Skip Button (if allowed)
        if (widget.canSkip && !widget.updateInfo.isForced) ...[
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: _skipUpdate,
              child: const Text('تخطي هذا الإصدار'),
            ),
          ),
        ],

        // Later Button (if allowed)
        if (!widget.updateInfo.isForced) ...[
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لاحقاً'),
          ),
        ],
      ],
    );
  }

  Future<void> _startUpdate() async {
    if (!widget.updateInfo.isCompatible) {
      _showErrorDialog('هذا التحديث غير متوافق مع إصدار التطبيق الحالي');
      return;
    }

    setState(() {
      _isUpdating = true;
      _progress = 0.0;
      _statusMessage = 'جاري التحضير للتحديث...';
    });

    try {
      // إنشاء نسخة احتياطية
      setState(() {
        _progress = 0.2;
        _statusMessage = 'إنشاء نسخة احتياطية من البيانات...';
      });

      final backup = await AppUpdateService.createDataBackup();
      if (backup != null && backup['success'] == true) {
        setState(() {
          _backupCreated = true;
          _progress = 0.4;
          _statusMessage = 'تم إنشاء النسخة الاحتياطية بنجاح';
        });
      } else {
        // في حالة فشل النسخة الاحتياطية، نعرض تحذير
        final shouldContinue = await _showBackupFailureDialog(
          backup?['error'] ?? 'خطأ غير معروف',
        );
        if (!shouldContinue) {
          setState(() {
            _isUpdating = false;
            _statusMessage = 'تم إلغاء التحديث';
          });
          return;
        }
      }

      // محاكاة التحديث
      setState(() {
        _progress = 0.6;
        _statusMessage = 'تحميل التحديث...';
      });

      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _progress = 0.8;
        _statusMessage = 'تثبيت التحديث...';
      });

      await Future.delayed(const Duration(seconds: 2));

      // فتح رابط التحديث
      if (widget.updateInfo.downloadUrl.isNotEmpty) {
        final uri = Uri.parse(widget.updateInfo.downloadUrl);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        }
      }

      setState(() {
        _progress = 1.0;
        _statusMessage = 'تم التحديث بنجاح!';
      });
    } catch (e) {
      setState(() {
        _isUpdating = false;
        _statusMessage = 'خطأ في التحديث: $e';
      });

      _showErrorDialog('حدث خطأ أثناء التحديث: $e');
    }
  }

  Future<void> _skipUpdate() async {
    await AppUpdateService.skipVersion(widget.updateInfo.latestVersion);
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<bool> _showBackupFailureDialog(String error) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فشل في إنشاء النسخة الاحتياطية'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('حدث خطأ أثناء إنشاء النسخة الاحتياطية:'),
            const SizedBox(height: 8),
            Text(error, style: const TextStyle(color: Colors.red)),
            const SizedBox(height: 16),
            const Text(
              'هل تريد المتابعة بدون نسخة احتياطية؟ (غير مستحسن)',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء التحديث'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('متابعة بدون نسخة احتياطية'),
          ),
        ],
      ),
    );

    return result ?? false;
  }
}
