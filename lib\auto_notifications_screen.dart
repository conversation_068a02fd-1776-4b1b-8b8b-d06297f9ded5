import 'package:flutter/material.dart';
import 'package:sqflite/sqflite.dart';
import 'db_helper.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

class AutoNotificationsScreen extends StatefulWidget {
  const AutoNotificationsScreen({Key? key}) : super(key: key);

  @override
  State<AutoNotificationsScreen> createState() =>
      _AutoNotificationsScreenState();
}

/// دالة مساعدة لاستبدال متغيرات التاريخ في نص الرسالة تلقائياً (متاحة على مستوى الملف)
Future<String> fillMessageVars(
  String template, {
  String? name,
  String? phone,
  String? type,
  String? price,
  String? startDate,
  DateTime? endDate,
  String? debt,
  String? paid,
}) async {
  String msg = template;
  if (name != null) msg = msg.replaceAll('{الاسم}', name);
  if (phone != null) msg = msg.replaceAll('{رقم_الهاتف}', phone);
  if (type != null) msg = msg.replaceAll('{نوع_الاشتراك}', type);
  if (price != null) msg = msg.replaceAll('{سعر_الاشتراك}', price);
  if (debt != null) msg = msg.replaceAll('{الدين}', debt);
  if (paid != null) msg = msg.replaceAll('{المبلغ_المسدد}', paid);
  // لا تذكر وقت البدء إلا إذا كان موجوداً في القالب
  if (startDate != null && template.contains('{تاريخ_البدء}')) {
    msg = msg.replaceAll('{تاريخ_البدء}', startDate);
  }
  if (endDate != null) {
    final formatted = await formatDateForMessage(endDate);
    msg = msg.replaceAll('{تاريخ_الانتهاء}', formatted);
  }
  return msg;
}

/// دالة مساعدة لتنسيق التاريخ حسب إعداد وقت الاشتراك (dateType) (متاحة على مستوى الملف)
Future<String> formatDateForMessage(DateTime date) async {
  int dateType = 1;
  try {
    final db = await DBHelper.instance.database;
    final result = await db.query(
      'settings',
      where: 'key = ?',
      whereArgs: ['dateType'],
      limit: 1,
    );
    if (result.isNotEmpty) {
      dateType = int.tryParse(result.first['value'].toString()) ?? 1;
    }
  } catch (_) {}
  if (dateType == 0) {
    // تاريخ مع وقت: yyyy/MM/dd (h:mm ص/م) بالعربية
    final y = date.year.toString().padLeft(4, '0');
    final m = date.month.toString().padLeft(2, '0');
    final d = date.day.toString().padLeft(2, '0');
    int hour = date.hour % 12 == 0 ? 12 : date.hour % 12;
    String ampm = date.hour >= 12 ? 'م' : 'ص';
    String min = date.minute.toString().padLeft(2, '0');
    return '$y/$m/$d ($hour:$min $ampm)';
  } else {
    // تاريخ فقط
    return date.toString().split(' ').first;
  }
}

class _AutoNotificationsScreenState extends State<AutoNotificationsScreen> {
  /// إنشاء جدول me_debt_notifications إذا لم يكن موجوداً
  Future<void> _ensureMeDebtNotificationsTable() async {
    final db = await DBHelper.instance.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS me_debt_notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        debt TEXT,
        sent_at TEXT
      )
    ''');
  }

  /// إنشاء جدول me_near_end_notifications إذا لم يكن موجوداً
  Future<void> _ensureMeNearEndNotificationsTable() async {
    final db = await DBHelper.instance.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS me_near_end_notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        end_date TEXT,
        sent_at TEXT
      )
    ''');
  }

  /// إنشاء جدول me_end_notifications إذا لم يكن موجوداً
  Future<void> _ensureMeEndNotificationsTable() async {
    final db = await DBHelper.instance.database;
    await db.execute('''
      CREATE TABLE IF NOT EXISTS me_end_notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT,
        end_date TEXT,
        sent_at TEXT
      )
    ''');
  }

  // حالات الخيارات
  bool notifyStart = true;
  bool notifyNearEnd = true;
  bool notifyEnd = true;
  bool notifyPayment = true;
  bool notifyDebt = true;
  bool notifyMeEnd = true;

  // متغيرات الرسائل
  final TextEditingController _startMsgController = TextEditingController();
  final TextEditingController _nearEndMsgController = TextEditingController();
  final TextEditingController _payMsgController = TextEditingController();
  final TextEditingController _endMsgController = TextEditingController();
  final TextEditingController _remMsgController = TextEditingController();

  final FocusNode _startFocus = FocusNode();
  final FocusNode _nearEndFocus = FocusNode();
  final FocusNode _payFocus = FocusNode();
  final FocusNode _endFocus = FocusNode();
  final FocusNode _remFocus = FocusNode();

  bool _expandStart = false;
  bool _expandNearEnd = false;
  bool _expandPay = false;
  bool _expandEnd = false;
  bool _expandRem = false;

  final List<String> _allVars = [
    '{الاسم}',
    '{رقم_الهاتف}',
    '{نوع_الاشتراك}',
    '{سعر_الاشتراك}',
    '{تاريخ_البدء}',
    '{تاريخ_الانتهاء}',
    '{الدين}',
    '{المبلغ_المسدد}',
  ];

  FlutterLocalNotificationsPlugin? _localNotifications;

  // تهيئة timezone
  void _initTimezone() {
    tz_data.initializeTimeZones();
    // تعيين المنطقة الزمنية للعراق
    tz.setLocalLocation(tz.getLocation('Asia/Baghdad'));
  }

  // فحص الإشعارات المفقودة عند فتح التطبيق
  Future<void> _checkMissedNotifications() async {
    try {
      final db = await DBHelper.instance.database;
      final now = DateTime.now();

      // البحث عن الاشتراكات المنتهية التي لم يتم إرسال إشعار لها
      final expiredSubscriptions = await db.rawQuery(
        '''
        SELECT s.*, sub.name
        FROM subscriptions s
        LEFT JOIN subscribers sub ON s.subscriber_id = sub.id
        WHERE s.end_date <= ?
        AND s.subscriber_id NOT IN (
          SELECT DISTINCT CAST(SUBSTR(name, INSTR(name, '_') + 1) AS INTEGER)
          FROM me_end_notifications
          WHERE name LIKE 'sub_%'
        )
      ''',
        [now.toIso8601String()],
      );

      // إرسال إشعارات للاشتراكات المنتهية
      for (final sub in expiredSubscriptions) {
        final subscriberName = sub['name'] ?? 'مشترك غير معروف';
        final endDateStr = sub['end_date']?.toString() ?? '';
        if (endDateStr.isNotEmpty) {
          final endDate = DateTime.parse(endDateStr);
          await notifyUserIfMeEnd(subscriberName.toString(), endDate);
        }
      }

      debugPrint('تم فحص ${expiredSubscriptions.length} اشتراك منتهي');
    } catch (e) {
      debugPrint('خطأ في فحص الإشعارات المفقودة: $e');
    }
  }

  // جدولة إشعار مسبق لانتهاء اشتراك
  Future<void> scheduleEndNotification({
    required int subscriberId,
    required String subscriberName,
    required DateTime endDate,
  }) async {
    if (!notifyMeEnd) return;

    try {
      // إلغاء أي إشعار مجدول مسبقاً لنفس المشترك
      await _localNotifications?.cancel(subscriberId);

      // جدولة إشعار جديد
      final scheduledDate = tz.TZDateTime.from(endDate, tz.local);

      // التأكد من أن التاريخ في المستقبل
      if (scheduledDate.isAfter(tz.TZDateTime.now(tz.local))) {
        await _localNotifications?.zonedSchedule(
          subscriberId,
          'انتهاء اشتراك مشترك',
          'انتهى اشتراك $subscriberName اليوم',
          scheduledDate,
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'end_channel',
              'تنبيهات انتهاء الاشتراك',
              channelDescription: 'تنبيه بانتهاء اشتراك مشترك',
              importance: Importance.max,
              priority: Priority.high,
            ),
          ),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        );

        debugPrint('تم جدولة إشعار انتهاء اشتراك $subscriberName في $endDate');
      } else {
        debugPrint(
          'تاريخ انتهاء اشتراك $subscriberName في الماضي - لن يتم جدولة إشعار',
        );
      }
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار انتهاء الاشتراك: $e');
    }
  }

  // إلغاء إشعار مجدول لمشترك
  Future<void> cancelScheduledNotification(int subscriberId) async {
    try {
      await _localNotifications?.cancel(subscriberId);
      debugPrint('تم إلغاء الإشعار المجدول للمشترك $subscriberId');
    } catch (e) {
      debugPrint('خطأ في إلغاء الإشعار المجدول: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    _initTimezone();
    _initLocalNotifications();
    _loadNotificationOptions();
    _ensureMeEndNotificationsTable();
    _ensureMeDebtNotificationsTable();
    _ensureMeNearEndNotificationsTable();
    _checkMissedNotifications();
    _initializeDefaultMessages();
    _startFocus.addListener(() {
      setState(() {
        _expandStart = _startFocus.hasFocus;
      });
    });
    _nearEndFocus.addListener(() {
      setState(() {
        _expandNearEnd = _nearEndFocus.hasFocus;
      });
    });
    _payFocus.addListener(() {
      setState(() {
        _expandPay = _payFocus.hasFocus;
      });
    });
    _endFocus.addListener(() {
      setState(() {
        _expandEnd = _endFocus.hasFocus;
      });
    });
    _remFocus.addListener(() {
      setState(() {
        _expandRem = _remFocus.hasFocus;
      });
    });
  }

  Future<void> _initLocalNotifications() async {
    _localNotifications = FlutterLocalNotificationsPlugin();
    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
    );
    await _localNotifications!.initialize(initSettings);
  }

  Future<void> _showEndNotification(
    String subscriberName,
    DateTime endDate,
  ) async {
    if (_localNotifications == null) return;
    // final formattedDate = await formatDateForMessage(endDate); // لم يعد هناك حاجة لهذا المتغير
    String body = await fillMessageVars(
      'انتهى اشتراك {الاسم} بتاريخ {تاريخ_الانتهاء}',
      name: subscriberName,
      endDate: endDate,
    );
    await _localNotifications!.show(
      1001,
      'انتهاء اشتراك مشترك',
      body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'end_channel',
          'تنبيهات انتهاء الاشتراك',
          channelDescription: 'تنبيه بانتهاء اشتراك مشترك',
          importance: Importance.max,
          priority: Priority.high,
        ),
      ),
    );
  }

  // دالة يمكن استدعاؤها من أي مكان في التطبيق عند انتهاء اشتراك مشترك
  Future<void> notifyUserIfMeEnd(
    String subscriberName,
    DateTime endDate,
  ) async {
    if (!notifyMeEnd) return;
    final db = await DBHelper.instance.database;
    final endDateStr = endDate.toIso8601String();
    // تحقق هل تم إرسال الإشعار مسبقاً لهذا المشترك ولنفس تاريخ الانتهاء
    final result = await db.query(
      'me_end_notifications',
      where: 'name = ? AND end_date = ?',
      whereArgs: [subscriberName, endDateStr],
      limit: 1,
    );
    if (result.isNotEmpty) {
      // تم إرسال الإشعار مسبقاً، لا ترسل مرة أخرى
      return;
    }
    // أرسل الإشعار
    await _showEndNotification(subscriberName, endDate);
    // سجل أنه تم الإرسال
    await db.insert('me_end_notifications', {
      'name': subscriberName,
      'end_date': endDateStr,
      'sent_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// دالة لإشعار تذكير الديون (مرة واحدة لكل مشترك ولكل قيمة دين)
  Future<void> notifyUserIfMeDebt(
    String subscriberName,
    String debtValue,
    String message,
  ) async {
    if (!notifyDebt) return;
    final db = await DBHelper.instance.database;
    // تحقق هل تم إرسال الإشعار مسبقاً لهذا المشترك ولنفس قيمة الدين
    final result = await db.query(
      'me_debt_notifications',
      where: 'name = ? AND debt = ?',
      whereArgs: [subscriberName, debtValue],
      limit: 1,
    );
    if (result.isNotEmpty) {
      // تم إرسال الإشعار مسبقاً، لا ترسل مرة أخرى
      return;
    }
    // أرسل الإشعار (يمكنك تخصيص طريقة الإشعار هنا)
    await _localNotifications?.show(
      1002,
      'تذكير دين',
      message,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'debt_channel',
          'تنبيهات تذكير الديون',
          channelDescription: 'تنبيه بتذكير دين',
          importance: Importance.max,
          priority: Priority.high,
        ),
      ),
    );
    // سجل أنه تم الإرسال
    await db.insert('me_debt_notifications', {
      'name': subscriberName,
      'debt': debtValue,
      'sent_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  /// دالة لإشعار قرب انتهاء الاشتراك (مرة واحدة لكل مشترك ولكل تاريخ انتهاء)
  Future<void> notifyUserIfMeNearEnd(
    String subscriberName,
    DateTime endDate,
    String message,
  ) async {
    if (!notifyNearEnd) return;
    final db = await DBHelper.instance.database;
    final endDateStr = endDate.toIso8601String();
    // تحقق هل تم إرسال الإشعار مسبقاً لهذا المشترك ولنفس تاريخ الانتهاء
    final result = await db.query(
      'me_near_end_notifications',
      where: 'name = ? AND end_date = ?',
      whereArgs: [subscriberName, endDateStr],
      limit: 1,
    );
    if (result.isNotEmpty) {
      // تم إرسال الإشعار مسبقاً، لا ترسل مرة أخرى
      return;
    }
    // أرسل الإشعار (يمكنك تخصيص طريقة الإشعار هنا)
    await _localNotifications?.show(
      1003,
      'قرب انتهاء الاشتراك',
      message,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'near_end_channel',
          'تنبيهات قرب انتهاء الاشتراك',
          channelDescription: 'تنبيه بقرب انتهاء الاشتراك',
          importance: Importance.max,
          priority: Priority.high,
        ),
      ),
    );
    // سجل أنه تم الإرسال
    await db.insert('me_near_end_notifications', {
      'name': subscriberName,
      'end_date': endDateStr,
      'sent_at': DateTime.now().toIso8601String(),
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  @override
  void dispose() {
    _startFocus.dispose();
    _nearEndFocus.dispose();
    _payFocus.dispose();
    _endFocus.dispose();
    _remFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: FutureBuilder<List<String>>(
              future: _loadMessages(),
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return _buildLoadingState(colorScheme);
                }
                final messages = snapshot.data!;
                return SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 24,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // رأس الشاشة
                      _buildHeader(colorScheme, isDark),
                      const SizedBox(height: 32),

                      // محتوى الشاشة
                      _buildNotificationsContent(colorScheme, isDark, messages),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.18),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withValues(
              alpha: isDark ? 0.08 : 0.18,
            ),
            child: Icon(
              Icons.notifications_active,
              color: colorScheme.primary,
              size: 54,
            ),
          ),
        ),
        // عنوان الشاشة
        Text(
          'تنبيهات واتساب',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'إعداد التنبيهات التلقائية للمشتركين',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء حالة التحميل
  Widget _buildLoadingState(ColorScheme colorScheme) {
    return Center(
      child: Card(
        elevation: 0,
        color: colorScheme.surface.withValues(alpha: 0.7),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
        child: const Padding(
          padding: EdgeInsets.all(64),
          child: CircularProgressIndicator(),
        ),
      ),
    );
  }

  // بناء محتوى التنبيهات
  Widget _buildNotificationsContent(
    ColorScheme colorScheme,
    bool isDark,
    List<String> messages,
  ) {
    return Column(
      children: [
        // بطاقة المشترك
        _buildSubscriberNotificationsCard(colorScheme, isDark),
        const SizedBox(height: 24),

        // بطاقة لي
        _buildMyNotificationsCard(colorScheme, isDark),
        const SizedBox(height: 18),
        // القسم الثاني: رسائل التنبيهات
        _editableMessageCard(
          context,
          'رسالة بدء الاشتراك',
          messages[0],
          colorScheme.primaryContainer,
          _startMsgController,
          _allVars,
          'start_msg',
          _expandStart,
          _startFocus,
          () => setState(() => _expandStart = true),
        ),
        _editableMessageCard(
          context,
          'رسالة قرب انتهاء الاشتراك',
          messages[1],
          colorScheme.primaryContainer,
          _nearEndMsgController,
          _allVars,
          'near_end_msg',
          _expandNearEnd,
          _nearEndFocus,
          () => setState(() => _expandNearEnd = true),
        ),
        _editableMessageCard(
          context,
          'رسالة التسديد',
          messages[2],
          colorScheme.primaryContainer,
          _payMsgController,
          _allVars,
          'pay_msg',
          _expandPay,
          _payFocus,
          () {
            setState(() => _expandPay = true);
            FocusScope.of(context).requestFocus(_payFocus);
          },
        ),
        _editableMessageCard(
          context,
          'رسالة انتهاء الاشتراك',
          messages[3],
          colorScheme.primaryContainer,
          _endMsgController,
          _allVars,
          'end_msg',
          _expandEnd,
          _endFocus,
          () => setState(() => _expandEnd = true),
        ),
        _editableMessageCard(
          context,
          'رسالة تذكير الديون',
          messages[4],
          colorScheme.primaryContainer,
          _remMsgController,
          _allVars,
          'rem_msg',
          _expandRem,
          _remFocus,
          () {
            setState(() => _expandRem = true);
            FocusScope.of(context).requestFocus(_remFocus);
          },
        ),
      ],
    );
  }

  Future<List<String>> _loadMessages() async {
    final start = await DBHelper.instance.getMessage(
      'start_msg',
      'مرحباً {الاسم}، تم تجديد اشتراكك بنجاح. نوع الاشتراك: {نوع_الاشتراك}، السعر: {سعر_الاشتراك}، الدين الحالي: {الدين}، تاريخ البدء: {تاريخ_البدء}، تاريخ الانتهاء: {تاريخ_الانتهاء}. شكراً لاختيارك لنا.',
    );
    final nearEnd = await DBHelper.instance.getMessage(
      'near_end_msg',
      'عزيزي {الاسم}، اشتراكك سينتهي في {تاريخ_الانتهاء}. يرجى التجديد للحفاظ على الخدمة.',
    );
    final pay = await DBHelper.instance.getMessage(
      'pay_msg',
      'تم استلام دفعتك بقيمة {المبلغ_المسدد}. المتبقي عليك: {الدين}. شكراً لك {الاسم}.',
    );
    final end = await DBHelper.instance.getMessage(
      'end_msg',
      'عزيزي {الاسم}، انتهى اشتراكك في الانترنت بتاريخ {تاريخ_الانتهاء}.',
    );
    final rem = await DBHelper.instance.getMessage(
      'rem_msg',
      'عزيزي {الاسم}، يرجى تسديد مبلغ الديون  {الدين}.',
    );
    // تعيين النص فقط إذا كان مختلفاً لتجنب إعادة تعيين موضع المؤشر
    if (_startMsgController.text != start) {
      _startMsgController.text = start;
    }
    if (_nearEndMsgController.text != nearEnd) {
      _nearEndMsgController.text = nearEnd;
    }
    if (_payMsgController.text != pay) {
      _payMsgController.text = pay;
    }
    if (_endMsgController.text != end) {
      _endMsgController.text = end;
    }
    if (_remMsgController.text != rem) {
      _remMsgController.text = rem;
    }
    // ضمان إرجاع 5 عناصر دائمًا لتفادي RangeError
    final msgs = [start, nearEnd, pay, end, rem];
    return msgs.length >= 5
        ? msgs.sublist(0, 5)
        : (msgs + List.filled(5 - msgs.length, '')).sublist(0, 5);
  }

  // إضافة تحميل وحفظ خيارات التنبيهات من وإلى قاعدة البيانات
  Future<void> _loadNotificationOptions() async {
    final db = await DBHelper.instance.database;
    final keys = [
      'notifyStart',
      'notifyNearEnd',
      'notifyEnd',
      'notifyPayment',
      'notifyDebt',
      'notifyMeEnd',
    ];
    final results = await db.query(
      'settings',
      where: 'key IN (${List.filled(keys.length, '?').join(',')})',
      whereArgs: keys,
    );
    Map<String, dynamic> map = {};
    for (var row in results) {
      final key = row['key']?.toString() ?? '';
      map[key] = row['value'];
    }
    setState(() {
      notifyStart = map['notifyStart'] == '1' || map['notifyStart'] == true;
      notifyNearEnd =
          map['notifyNearEnd'] == '1' || map['notifyNearEnd'] == true;
      notifyEnd = map['notifyEnd'] == '1' || map['notifyEnd'] == true;
      notifyPayment =
          map['notifyPayment'] == '1' || map['notifyPayment'] == true;
      notifyDebt = map['notifyDebt'] == '1' || map['notifyDebt'] == true;
      notifyMeEnd = map['notifyMeEnd'] == '1' || map['notifyMeEnd'] == true;
    });
  }

  Widget _editableMessageCard(
    BuildContext context,
    String title,
    String initialText,
    Color color,
    TextEditingController controller,
    List<String> vars,
    String dbKey,
    bool expanded,
    FocusNode focusNode,
    VoidCallback onExpand,
  ) {
    // استخراج المتغيرات الموجودة في النص الحالي
    final Set<String> usedVars = {};
    for (final v in vars) {
      if (controller.text.contains(v)) usedVars.add(v);
    }
    return GestureDetector(
      onTap: onExpand,
      child: Container(
        margin: const EdgeInsets.only(bottom: 14),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(22),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                // زر استعادة النص الافتراضي العصري
                Container(
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.orange.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: IconButton(
                    icon: const Icon(
                      Icons.refresh,
                      color: Colors.orange,
                      size: 20,
                    ),
                    tooltip: 'استعادة النص الافتراضي',
                    onPressed: () async {
                      final confirm = await _showModernConfirmDialog(
                        context,
                        'استعادة النص الافتراضي',
                        'هل أنت متأكد أنك تريد استعادة النص الافتراضي؟\nسيتم فقدان التعديلات الحالية.',
                        'استعادة',
                        Colors.orange,
                      );
                      if (confirm == true) {
                        final defaultText = await DBHelper.instance
                            .getOriginalDefaultMessage(dbKey);
                        setState(() {
                          controller.text = defaultText;
                        });
                        await DBHelper.instance.setMessage(dbKey, defaultText);
                        if (mounted) {
                          FocusScope.of(context).unfocus();
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Row(
                                children: [
                                  Icon(Icons.check_circle, color: Colors.white),
                                  SizedBox(width: 12),
                                  Text('تم استعادة النص الافتراضي'),
                                ],
                              ),
                              backgroundColor: Colors.green,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          );
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (!expanded) ...[
              _coloredVariablesText(controller.text, _allVars),
            ] else ...[
              TextField(
                controller: controller,
                focusNode: focusNode,
                maxLines: null,
                readOnly: false,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  filled: false,
                ),
                style: const TextStyle(fontSize: 15),
                // إزالة onTap لتجنب تداخل السلوك مع التحرير
                buildCounter:
                    (
                      context, {
                      required currentLength,
                      required isFocused,
                      maxLength,
                    }) => null,
              ),
            ],
            if (expanded) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: vars
                    .map(
                      (v) => OutlinedButton(
                        onPressed: () {
                          final text = controller.text;
                          final selection = controller.selection;
                          final newText = text.replaceRange(
                            selection.start,
                            selection.end,
                            v,
                          );
                          controller.text = newText;
                          controller.selection = TextSelection.collapsed(
                            offset: selection.start + v.length,
                          );
                        },
                        child: Text(v, style: const TextStyle(fontSize: 14)),
                      ),
                    )
                    .toList(),
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // زر حفظ كافتراضي جديد
                  TextButton.icon(
                    onPressed: () async {
                      final confirm = await _showModernConfirmDialog(
                        context,
                        'حفظ كنص افتراضي',
                        'هل تريد حفظ النص الحالي كنص افتراضي جديد؟\nسيتم استبدال النص الافتراضي السابق.',
                        'حفظ',
                        Colors.purple,
                      );
                      if (confirm == true) {
                        await DBHelper.instance.setDefaultMessage(
                          dbKey,
                          controller.text,
                        );
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: const Row(
                                children: [
                                  Icon(Icons.bookmark, color: Colors.white),
                                  SizedBox(width: 12),
                                  Text('تم حفظ النص كافتراضي جديد'),
                                ],
                              ),
                              backgroundColor: Colors.purple,
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          );
                        }
                      }
                    },
                    icon: const Icon(Icons.bookmark_add, size: 16),
                    label: const Text('حفظ كافتراضي'),
                    style: TextButton.styleFrom(foregroundColor: Colors.purple),
                  ),

                  // زر التحديث
                  ElevatedButton.icon(
                    onPressed: () async {
                      await DBHelper.instance.setMessage(
                        dbKey,
                        controller.text,
                      );
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: const Row(
                              children: [
                                Icon(Icons.save, color: Colors.white),
                                SizedBox(width: 12),
                                Text('تم تحديث الرسالة'),
                              ],
                            ),
                            backgroundColor: Colors.green,
                            behavior: SnackBarBehavior.floating,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        );
                        // بعد الحفظ: إزالة التركيز ليتم طي البطاقة
                        focusNode.unfocus();
                      }
                    },
                    icon: const Icon(Icons.save, size: 18),
                    label: const Text('تحديث'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _coloredVariablesText(String text, List<String> vars) {
    final spans = <TextSpan>[];
    int i = 0;
    while (i < text.length) {
      bool matched = false;
      for (final v in vars) {
        if (text.startsWith(v, i)) {
          spans.add(
            TextSpan(
              text: v,
              style: const TextStyle(color: Colors.blue),
            ),
          );
          i += v.length;
          matched = true;
          break;
        }
      }
      if (!matched) {
        spans.add(TextSpan(text: text[i]));
        i++;
      }
    }
    return RichText(
      text: TextSpan(
        style: const TextStyle(color: Colors.black, fontSize: 15),
        children: spans,
      ),
    );
  }

  // بناء صف خيار عصري
  Widget _buildModernOptionRow(
    String title,
    IconData icon,
    bool value,
    Function(bool) onChanged,
    ColorScheme colorScheme, {
    required String dbKey,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: value
            ? colorScheme.primary.withValues(alpha: 0.1)
            : colorScheme.surface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: value
              ? colorScheme.primary.withValues(alpha: 0.3)
              : colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: value
                  ? colorScheme.primary.withValues(alpha: 0.2)
                  : colorScheme.outline.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: value ? colorScheme.primary : colorScheme.outline,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: (newValue) {
              onChanged(newValue);
              _saveOption(dbKey, newValue);
            },
            activeColor: colorScheme.primary,
          ),
        ],
      ),
    );
  }

  // بناء بطاقة تنبيهات المشترك العصرية
  Widget _buildSubscriberNotificationsCard(
    ColorScheme colorScheme,
    bool isDark,
  ) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'تنبيهات المشترك',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildModernOptionRow(
              'بدء الاشتراك',
              Icons.calendar_today,
              notifyStart,
              (v) => setState(() => notifyStart = v),
              colorScheme,
              dbKey: 'notifyStart',
            ),
            const SizedBox(height: 16),
            _buildModernOptionRow(
              'قرب انتهاء الاشتراك قبل 48 ساعة',
              Icons.timer,
              notifyNearEnd,
              (v) => setState(() => notifyNearEnd = v),
              colorScheme,
              dbKey: 'notifyNearEnd',
            ),
            const SizedBox(height: 16),
            _buildModernOptionRow(
              'انتهاء الاشتراك',
              Icons.event,
              notifyEnd,
              (v) => setState(() => notifyEnd = v),
              colorScheme,
              dbKey: 'notifyEnd',
            ),
            const SizedBox(height: 16),
            _buildModernOptionRow(
              'التسديد',
              Icons.account_balance_wallet,
              notifyPayment,
              (v) => setState(() => notifyPayment = v),
              colorScheme,
              dbKey: 'notifyPayment',
            ),
            const SizedBox(height: 16),
            _buildModernOptionRow(
              'تذكير الديون',
              Icons.warning_amber_rounded,
              notifyDebt,
              (v) => setState(() => notifyDebt = v),
              colorScheme,
              dbKey: 'notifyDebt',
            ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة تنبيهاتي العصرية
  Widget _buildMyNotificationsCard(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.notifications_active, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'تنبيهاتي',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildModernOptionRow(
              'إنتهاء الاشتراك',
              Icons.event,
              notifyMeEnd,
              (v) => setState(() => notifyMeEnd = v),
              colorScheme,
              dbKey: 'notifyMeEnd',
            ),
          ],
        ),
      ),
    );
  }

  // حفظ خيار في قاعدة البيانات
  Future<void> _saveOption(String key, bool value) async {
    final db = await DBHelper.instance.database;
    await db.insert('settings', {
      'key': key,
      'value': value ? 1 : 0,
    }, conflictAlgorithm: ConflictAlgorithm.replace);
  }

  // تهيئة النصوص الافتراضية في قاعدة البيانات
  Future<void> _initializeDefaultMessages() async {
    final messageKeys = [
      'start_msg',
      'near_end_msg',
      'pay_msg',
      'end_msg',
      'rem_msg',
    ];

    for (final key in messageKeys) {
      // هذا سيضمن وجود النص الافتراضي في قاعدة البيانات
      await DBHelper.instance.getOriginalDefaultMessage(key);
    }
  }

  // عرض حوار تأكيد عصري
  Future<bool?> _showModernConfirmDialog(
    BuildContext context,
    String title,
    String content,
    String confirmText,
    Color color,
  ) async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(Icons.warning_amber_rounded, color: color, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Text(content, style: const TextStyle(fontSize: 16)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey[600],
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: color,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
}
