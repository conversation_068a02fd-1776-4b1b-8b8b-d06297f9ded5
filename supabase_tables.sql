-- ملف إنشاء الجداول المطلوبة في Supabase
-- يجب تنفيذ هذا الكود في SQL Editor في Supabase

-- 1. جدول حسابات المستخدمين
CREATE TABLE IF NOT EXISTS user_accounts (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  is_trial BOOLEAN DEFAULT true,
  expiry_millis BIGINT DEFAULT 0,
  creation_millis BIGINT DEFAULT 0,
  activation_millis BIGINT DEFAULT 0,
  active_package TEXT DEFAULT '',
  display_name TEXT DEFAULT '',
  trial_days INTEGER DEFAULT 15,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. جدول ربط المستخدمين بالأجهزة
CREATE TABLE IF NOT EXISTS user_devices (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id),
  UNIQUE(device_id)
);

-- 3. دالة الحصول على الوقت الحقيقي من الخادم
CREATE OR REPLACE FUNCTION get_server_time()
RETURNS TABLE(server_time timestamptz)
LANGUAGE sql
AS $$
  SELECT now() as server_time;
$$;

-- 4. فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_accounts_user_id ON user_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_user_accounts_is_trial ON user_accounts(is_trial);
CREATE INDEX IF NOT EXISTS idx_user_accounts_expiry ON user_accounts(expiry_millis);
CREATE INDEX IF NOT EXISTS idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX IF NOT EXISTS idx_user_devices_device_id ON user_devices(device_id);

-- 5. تفعيل Row Level Security (RLS)
ALTER TABLE user_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_devices ENABLE ROW LEVEL SECURITY;

-- 6. سياسات الأمان للجدول user_accounts
-- المستخدمون يمكنهم قراءة وتعديل بياناتهم فقط
CREATE POLICY "Users can view own account data" ON user_accounts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own account data" ON user_accounts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own account data" ON user_accounts
  FOR UPDATE USING (auth.uid() = user_id);

-- 7. سياسات الأمان للجدول user_devices
-- المستخدمون يمكنهم قراءة وتعديل أجهزتهم فقط
CREATE POLICY "Users can view own devices" ON user_devices
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own devices" ON user_devices
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own devices" ON user_devices
  FOR UPDATE USING (auth.uid() = user_id);

-- 8. دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. تطبيق الدالة على جدول user_accounts
CREATE TRIGGER update_user_accounts_updated_at 
  BEFORE UPDATE ON user_accounts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. إدراج بيانات تجريبية (اختياري)
-- INSERT INTO user_accounts (user_id, display_name, is_trial, expiry_millis, creation_millis)
-- VALUES (
--   auth.uid(),
--   'مستخدم تجريبي',
--   true,
--   EXTRACT(EPOCH FROM (NOW() + INTERVAL '15 days')) * 1000,
--   EXTRACT(EPOCH FROM NOW()) * 1000
-- );

-- 11. جدول أكواد التفعيل
CREATE TABLE IF NOT EXISTS activation_codes (
  id SERIAL PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  days INTEGER NOT NULL,
  type TEXT NOT NULL,
  package_name TEXT NOT NULL,
  amount INTEGER NOT NULL,
  used BOOLEAN DEFAULT FALSE,
  used_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  used_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 12. فهارس لجدول أكواد التفعيل
CREATE INDEX IF NOT EXISTS idx_activation_codes_code ON activation_codes(code);
CREATE INDEX IF NOT EXISTS idx_activation_codes_used ON activation_codes(used);
CREATE INDEX IF NOT EXISTS idx_activation_codes_type ON activation_codes(type);

-- 13. تفعيل RLS لجدول أكواد التفعيل
ALTER TABLE activation_codes ENABLE ROW LEVEL SECURITY;

-- 14. سياسات الأمان لجدول أكواد التفعيل
-- جميع المستخدمين يمكنهم قراءة الأكواد غير المستخدمة
CREATE POLICY "Users can view unused activation codes" ON activation_codes
  FOR SELECT USING (used = false);

-- المستخدمون يمكنهم تحديث الأكواد لتصبح مستخدمة
CREATE POLICY "Users can mark codes as used" ON activation_codes
  FOR UPDATE USING (auth.uid() IS NOT NULL AND used = false);

-- 15. جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    timestamp BIGINT NOT NULL,
    public_url TEXT NOT NULL,
    checksum TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 16. فهارس لجدول النسخ الاحتياطية
CREATE INDEX IF NOT EXISTS idx_backups_user_id ON backups(user_id);
CREATE INDEX IF NOT EXISTS idx_backups_timestamp ON backups(timestamp);

-- 17. تفعيل RLS لجدول النسخ الاحتياطية
ALTER TABLE backups ENABLE ROW LEVEL SECURITY;

-- 18. سياسات الأمان لجدول النسخ الاحتياطية
CREATE POLICY "Users can view own backups" ON backups
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own backups" ON backups
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own backups" ON backups
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own backups" ON backups
  FOR DELETE USING (auth.uid() = user_id);

-- تم إنشاء جميع الجداول والدوال بنجاح!
-- الآن يمكن للتطبيق استخدام Supabase بالكامل بدلاً من Firebase
