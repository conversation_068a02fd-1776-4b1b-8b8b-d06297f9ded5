enum TransactionType {
  renewal, // تجديد
  payDebt, // تسديد دين
  addDebt, // إضافة دين
}

class Transaction {
  int? id;
  TransactionType type;
  String description;
  DateTime date;
  int? subscriberId;

  Transaction({
    this.id,
    required this.type,
    required this.description,
    required this.date,
    this.subscriberId,
  });

  factory Transaction.fromMap(Map<String, dynamic> map) {
    // تحويل القيم القديمة إلى الأنواع الجديدة
    TransactionType type;
    final typeIndex = map['type'] as int;

    switch (typeIndex) {
      case 0: // addSubscriber -> renewal
      case 1: // editSubscriber -> renewal
      case 4: // other -> renewal (للعمليات القديمة)
        type = TransactionType.renewal;
        break;
      case 2: // deleteSubscriber -> renewal (نادر)
        type = TransactionType.renewal;
        break;
      case 3: // payDebt -> payDebt
        type = TransactionType.payDebt;
        break;
      default:
        type = TransactionType.renewal; // القيمة الافتراضية
    }

    return Transaction(
      id: map['id'],
      type: type,
      description: map['description'],
      date: DateTime.parse(map['date']),
      subscriberId: map['subscriberId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.index,
      'description': description,
      'date': date.toIso8601String(),
      'subscriberId': subscriberId,
    };
  }
}
