-- إعداد قاعدة البيانات للأجهزة المتعددة
-- تشغيل هذا الملف في Supabase SQL Editor

-- 1. تعديل جدول user_devices لدعم الأجهزة المتعددة
DROP TABLE IF EXISTS user_devices CASCADE;

CREATE TABLE user_devices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  device_name TEXT,
  device_type TEXT CHECK (device_type IN ('android', 'ios', 'web')),
  is_primary BOOLEAN DEFAULT false,
  last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- فهرس فريد لضمان جهاز واحد = حساب واحد
  UNIQUE(device_id)
);

-- 2. إن<PERSON><PERSON><PERSON> جدول جلسات الأجهزة
CREATE TABLE device_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  session_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. إنشاء فهارس للأداء
CREATE INDEX idx_user_devices_user_id ON user_devices(user_id);
CREATE INDEX idx_user_devices_device_id ON user_devices(device_id);
CREATE INDEX idx_user_devices_last_active ON user_devices(last_active);
CREATE INDEX idx_device_sessions_user_id ON device_sessions(user_id);
CREATE INDEX idx_device_sessions_device_id ON device_sessions(device_id);
CREATE INDEX idx_device_sessions_expires_at ON device_sessions(expires_at);

-- 4. إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_devices_updated_at 
    BEFORE UPDATE ON user_devices 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_device_sessions_updated_at 
    BEFORE UPDATE ON device_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5. دالة للحصول على الحساب المرتبط بجهاز
CREATE OR REPLACE FUNCTION get_account_by_device(device_id_param TEXT)
RETURNS TABLE (
  user_id UUID,
  device_name TEXT,
  device_type TEXT,
  is_primary BOOLEAN,
  last_active TIMESTAMP WITH TIME ZONE,
  email TEXT,
  display_name TEXT,
  is_trial BOOLEAN,
  expiry_millis BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ud.user_id,
    ud.device_name,
    ud.device_type,
    ud.is_primary,
    ud.last_active,
    ua.email,
    ua.display_name,
    ua.is_trial,
    ua.expiry_millis
  FROM user_devices ud
  JOIN user_accounts ua ON ud.user_id = ua.user_id
  WHERE ud.device_id = device_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. دالة لربط جهاز بحساب
CREATE OR REPLACE FUNCTION link_device_to_account(
  user_id_param UUID,
  device_id_param TEXT,
  device_name_param TEXT DEFAULT NULL,
  device_type_param TEXT DEFAULT 'unknown',
  is_primary_param BOOLEAN DEFAULT false
)
RETURNS BOOLEAN AS $$
DECLARE
  existing_user_id UUID;
BEGIN
  -- فحص إذا كان الجهاز مرتبط بحساب آخر
  SELECT user_id INTO existing_user_id 
  FROM user_devices 
  WHERE device_id = device_id_param;
  
  -- إذا كان الجهاز مرتبط بحساب آخر، ارجع false
  IF existing_user_id IS NOT NULL AND existing_user_id != user_id_param THEN
    RAISE EXCEPTION 'الجهاز مرتبط بحساب آخر';
  END IF;
  
  -- إذا كان الجهاز مرتبط بنفس الحساب، حدث النشاط فقط
  IF existing_user_id = user_id_param THEN
    UPDATE user_devices 
    SET last_active = NOW(),
        device_name = COALESCE(device_name_param, device_name),
        device_type = COALESCE(device_type_param, device_type)
    WHERE device_id = device_id_param;
    RETURN true;
  END IF;
  
  -- إذا كان جهاز أساسي، قم بإزالة الأساسية من الأجهزة الأخرى
  IF is_primary_param THEN
    UPDATE user_devices 
    SET is_primary = false 
    WHERE user_id = user_id_param;
  END IF;
  
  -- ربط الجهاز الجديد
  INSERT INTO user_devices (
    user_id, 
    device_id, 
    device_name, 
    device_type, 
    is_primary
  ) VALUES (
    user_id_param, 
    device_id_param, 
    device_name_param, 
    device_type_param, 
    is_primary_param
  );
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. دالة لإلغاء ربط جهاز
CREATE OR REPLACE FUNCTION unlink_device(device_id_param TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  DELETE FROM user_devices WHERE device_id = device_id_param;
  DELETE FROM device_sessions WHERE device_id = device_id_param;
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. دالة لتعيين جهاز كأساسي
CREATE OR REPLACE FUNCTION set_primary_device(
  user_id_param UUID,
  device_id_param TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
  -- إزالة الأساسية من جميع الأجهزة
  UPDATE user_devices 
  SET is_primary = false 
  WHERE user_id = user_id_param;
  
  -- تعيين الجهاز المحدد كأساسي
  UPDATE user_devices 
  SET is_primary = true 
  WHERE user_id = user_id_param AND device_id = device_id_param;
  
  RETURN true;
EXCEPTION
  WHEN OTHERS THEN
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. دالة لتنظيف الجلسات المنتهية
CREATE OR REPLACE FUNCTION cleanup_expired_sessions()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM device_sessions 
  WHERE expires_at < NOW() OR is_active = false;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. إعداد Row Level Security (RLS)
ALTER TABLE user_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE device_sessions ENABLE ROW LEVEL SECURITY;

-- سياسة للمستخدمين لرؤية أجهزتهم فقط
CREATE POLICY "Users can view their own devices" ON user_devices
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own devices" ON user_devices
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own devices" ON user_devices
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own devices" ON user_devices
  FOR DELETE USING (auth.uid() = user_id);

-- سياسة للجلسات
CREATE POLICY "Users can view their own sessions" ON device_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own sessions" ON device_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own sessions" ON device_sessions
  FOR UPDATE USING (auth.uid() = user_id);

-- 11. إنشاء مهمة تنظيف تلقائية (اختيارية)
-- يمكن تشغيلها كـ cron job
SELECT cron.schedule(
  'cleanup-expired-sessions',
  '0 2 * * *', -- كل يوم في الساعة 2 صباحاً
  'SELECT cleanup_expired_sessions();'
);

-- 12. إدراج بيانات تجريبية (اختيارية للاختبار)
-- INSERT INTO user_devices (user_id, device_id, device_name, device_type, is_primary)
-- VALUES 
--   ('user-uuid-here', 'test-device-1', 'Samsung Galaxy S21', 'android', true),
--   ('user-uuid-here', 'test-device-2', 'iPhone 13', 'ios', false);

COMMIT;
