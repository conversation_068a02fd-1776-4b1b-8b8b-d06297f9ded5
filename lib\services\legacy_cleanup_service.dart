import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة تنظيف البقايا القديمة من النظام السابق
class LegacyCleanupService {
  
  /// قائمة المفاتيح القديمة التي يجب حذفها
  static const List<String> _legacyKeys = [
    // مفاتيح النظام القديم
    'is_trial',
    'expiry_millis', 
    'creation_millis',
    'activation_millis',
    'active_package',
    'trial_days',
    'user_display_name',
    'actve_package', // خطأ إملائي في النظام القديم
    
    // مفاتيح الكاش القديمة (ستحتاج معرف المستخدم)
    'cache_is_trial_',
    'cache_expiry_millis_',
    'cache_creation_millis_',
    'cache_display_name_',
  ];

  /// تنظيف شامل للبيانات القديمة
  static Future<Map<String, dynamic>> performFullCleanup({
    String? userId,
  }) async {
    final results = <String, dynamic>{
      'cleaned_keys': <String>[],
      'errors': <String>[],
      'total_cleaned': 0,
    };

    try {
      debugPrint('🧹 [CLEANUP] بدء التنظيف الشامل للبقايا القديمة...');
      
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();
      
      // تنظيف المفاتيح الثابتة
      for (final legacyKey in _legacyKeys) {
        try {
          if (legacyKey.endsWith('_')) {
            // مفاتيح تحتاج معرف المستخدم
            if (userId != null) {
              final fullKey = '$legacyKey$userId';
              if (allKeys.contains(fullKey)) {
                await prefs.remove(fullKey);
                results['cleaned_keys'].add(fullKey);
                results['total_cleaned']++;
                debugPrint('🗑️ [CLEANUP] تم حذف: $fullKey');
              }
            }
            
            // البحث عن جميع المفاتيح التي تبدأ بهذا النمط
            final matchingKeys = allKeys.where((key) => key.startsWith(legacyKey));
            for (final key in matchingKeys) {
              await prefs.remove(key);
              results['cleaned_keys'].add(key);
              results['total_cleaned']++;
              debugPrint('🗑️ [CLEANUP] تم حذف: $key');
            }
          } else {
            // مفاتيح ثابتة
            if (allKeys.contains(legacyKey)) {
              await prefs.remove(legacyKey);
              results['cleaned_keys'].add(legacyKey);
              results['total_cleaned']++;
              debugPrint('🗑️ [CLEANUP] تم حذف: $legacyKey');
            }
          }
        } catch (e) {
          results['errors'].add('خطأ في حذف $legacyKey: $e');
          debugPrint('❌ [CLEANUP] خطأ في حذف $legacyKey: $e');
        }
      }

      // تنظيف مفاتيح إضافية قد تكون موجودة
      await _cleanupAdditionalLegacyKeys(prefs, results);
      
      debugPrint('✅ [CLEANUP] تم تنظيف ${results['total_cleaned']} مفتاح قديم');
      
    } catch (e) {
      results['errors'].add('خطأ عام في التنظيف: $e');
      debugPrint('❌ [CLEANUP] خطأ عام في التنظيف: $e');
    }

    return results;
  }

  /// تنظيف مفاتيح إضافية
  static Future<void> _cleanupAdditionalLegacyKeys(
    SharedPreferences prefs,
    Map<String, dynamic> results,
  ) async {
    final allKeys = prefs.getKeys();
    
    // أنماط المفاتيح القديمة
    final legacyPatterns = [
      RegExp(r'^cache_.*_[a-f0-9-]{36}$'), // مفاتيح الكاش مع UUID
      RegExp(r'^user_.*_[a-f0-9-]{36}$'), // مفاتيح المستخدم مع UUID
      RegExp(r'^trial_.*'), // مفاتيح التجربة
      RegExp(r'^activation_.*'), // مفاتيح التفعيل
      RegExp(r'^expiry_.*'), // مفاتيح الانتهاء
    ];

    for (final key in allKeys) {
      for (final pattern in legacyPatterns) {
        if (pattern.hasMatch(key)) {
          try {
            await prefs.remove(key);
            results['cleaned_keys'].add(key);
            results['total_cleaned']++;
            debugPrint('🗑️ [CLEANUP] تم حذف مفتاح بنمط قديم: $key');
          } catch (e) {
            results['errors'].add('خطأ في حذف $key: $e');
            debugPrint('❌ [CLEANUP] خطأ في حذف $key: $e');
          }
          break;
        }
      }
    }
  }

  /// فحص وجود بقايا قديمة
  static Future<Map<String, dynamic>> scanForLegacyData() async {
    final results = <String, dynamic>{
      'found_keys': <String>[],
      'total_found': 0,
      'needs_cleanup': false,
    };

    try {
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();

      // فحص المفاتيح الثابتة
      for (final legacyKey in _legacyKeys) {
        if (legacyKey.endsWith('_')) {
          // البحث عن مفاتيح تبدأ بهذا النمط
          final matchingKeys = allKeys.where((key) => key.startsWith(legacyKey));
          results['found_keys'].addAll(matchingKeys);
        } else {
          if (allKeys.contains(legacyKey)) {
            results['found_keys'].add(legacyKey);
          }
        }
      }

      results['total_found'] = results['found_keys'].length;
      results['needs_cleanup'] = results['total_found'] > 0;

      debugPrint('🔍 [CLEANUP] تم العثور على ${results['total_found']} مفتاح قديم');
      
    } catch (e) {
      debugPrint('❌ [CLEANUP] خطأ في فحص البقايا القديمة: $e');
    }

    return results;
  }

  /// تنظيف بيانات مستخدم معين
  static Future<int> cleanupUserData(String userId) async {
    int cleanedCount = 0;
    
    try {
      debugPrint('🧹 [CLEANUP] تنظيف بيانات المستخدم: $userId');
      
      final prefs = await SharedPreferences.getInstance();
      final allKeys = prefs.getKeys();
      
      // البحث عن جميع المفاتيح المرتبطة بهذا المستخدم
      final userKeys = allKeys.where((key) => key.contains(userId));
      
      for (final key in userKeys) {
        // التحقق إذا كان المفتاح من النظام القديم
        bool isLegacyKey = false;
        for (final legacyPattern in _legacyKeys) {
          if (key.contains(legacyPattern.replaceAll('_', ''))) {
            isLegacyKey = true;
            break;
          }
        }
        
        if (isLegacyKey) {
          await prefs.remove(key);
          cleanedCount++;
          debugPrint('🗑️ [CLEANUP] تم حذف مفتاح المستخدم: $key');
        }
      }
      
      debugPrint('✅ [CLEANUP] تم تنظيف $cleanedCount مفتاح للمستخدم: $userId');
      
    } catch (e) {
      debugPrint('❌ [CLEANUP] خطأ في تنظيف بيانات المستخدم: $e');
    }

    return cleanedCount;
  }

  /// إنشاء تقرير مفصل عن البقايا القديمة
  static Future<String> generateCleanupReport() async {
    final buffer = StringBuffer();
    
    try {
      buffer.writeln('📋 تقرير تنظيف البقايا القديمة');
      buffer.writeln('=' * 40);
      buffer.writeln('التاريخ: ${DateTime.now()}');
      buffer.writeln();

      // فحص البقايا
      final scanResults = await scanForLegacyData();
      buffer.writeln('🔍 نتائج الفحص:');
      buffer.writeln('- إجمالي المفاتيح القديمة: ${scanResults['total_found']}');
      buffer.writeln('- يحتاج تنظيف: ${scanResults['needs_cleanup'] ? 'نعم' : 'لا'}');
      buffer.writeln();

      if (scanResults['total_found'] > 0) {
        buffer.writeln('📝 المفاتيح القديمة الموجودة:');
        for (final key in scanResults['found_keys']) {
          buffer.writeln('  - $key');
        }
        buffer.writeln();
      }

      // إجراء التنظيف
      final cleanupResults = await performFullCleanup();
      buffer.writeln('🧹 نتائج التنظيف:');
      buffer.writeln('- تم تنظيف: ${cleanupResults['total_cleaned']} مفتاح');
      buffer.writeln('- أخطاء: ${cleanupResults['errors'].length}');
      buffer.writeln();

      if (cleanupResults['errors'].isNotEmpty) {
        buffer.writeln('❌ الأخطاء:');
        for (final error in cleanupResults['errors']) {
          buffer.writeln('  - $error');
        }
        buffer.writeln();
      }

      buffer.writeln('✅ تم الانتهاء من التنظيف بنجاح');
      
    } catch (e) {
      buffer.writeln('❌ خطأ في إنشاء التقرير: $e');
    }

    return buffer.toString();
  }

  /// فحص سريع لوجود بقايا
  static Future<bool> hasLegacyData() async {
    try {
      final scanResults = await scanForLegacyData();
      return scanResults['needs_cleanup'] ?? false;
    } catch (e) {
      debugPrint('❌ [CLEANUP] خطأ في الفحص السريع: $e');
      return false;
    }
  }
}
