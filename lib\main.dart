import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite/sqflite.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'simple_root_screen.dart';
import 'features/subscribers/domain/subscribers_repository.dart';
import 'features/subscribers/domain/subscribers_repository_impl.dart';
import 'features/subscribers/data/subscribers_storage_impl.dart';
import 'features/main_home_screen.dart';
import 'db_helper.dart';
import 'dart:async';
import 'screens/internet_check_screen.dart';
// النظام الموحد الجديد
import 'core/services/unified_initialization_service.dart';

// تعريف RouteObserver ليكون متاحًا للتطبيق كله
final RouteObserver<ModalRoute<void>> routeObserver =
    RouteObserver<ModalRoute<void>>();

// تعريف navigatorKey عالمي للوصول للـ context في أي مكان
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// تعريف ValueNotifier عالمي لحالة الاتصال بأي سيرفر أو لوحة
final ValueNotifier<bool> anyDeviceConnectedNotifier = ValueNotifier(false);
final ValueNotifier<bool> syncCompletedNotifier = ValueNotifier(false);

void main() async {
  debugPrint('🚀 [MAIN] بدء تهيئة التطبيق الموحد...');

  // تشغيل التطبيق مع معالج الأخطاء العام
  runZonedGuarded(
    () async {
      try {
        // تهيئة Flutter
        WidgetsFlutterBinding.ensureInitialized();
        debugPrint('✅ [MAIN] تم تهيئة Flutter');

        // تهيئة قاعدة البيانات للمنصات المختلفة
        if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
          sqfliteFfiInit();
          databaseFactory = databaseFactoryFfi;
          debugPrint('✅ [MAIN] تم تهيئة قاعدة البيانات للديسكتوب');
        }

        // تهيئة Supabase مباشرة بدون النظام الموحد
        debugPrint('🔧 [MAIN] تهيئة Supabase مباشرة...');
        try {
          await Supabase.initialize(
            url: 'https://iwtvsvfqmafsziqnoekm.supabase.co',
            anonKey:
                'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3dHZzdmZxbWFmc3ppcW5vZWttIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMyMjM2NzUsImV4cCI6MjA2ODc5OTY3NX0.9ho9O3Bk-iziVQBSiQ5X5V9E45KqEzJ2tuMoSu5kNKg',
          );
          debugPrint('✅ [MAIN] تم تهيئة Supabase بنجاح');
        } catch (e) {
          if (e.toString().contains('already initialized')) {
            debugPrint('✅ [MAIN] Supabase مهيأ بالفعل');
          } else {
            debugPrint('❌ [MAIN] خطأ في تهيئة Supabase: $e');
          }
        }

        // بدء التطبيق
        debugPrint('🚀 [MAIN] بدء تشغيل التطبيق...');
        runApp(const MyApp());
        debugPrint('✅ [MAIN] تم تشغيل التطبيق');
      } catch (e, stack) {
        debugPrint('❌ [MAIN] خطأ في تهيئة التطبيق: $e');
        debugPrint('Stack trace: $stack');

        // طباعة تقرير حالة النظام للتشخيص
        try {
          UnifiedInitializationService.printSystemStatus();
        } catch (_) {}

        runApp(
          MaterialApp(
            home: Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text(
                      'خطأ في تهيئة التطبيق',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      e.toString(),
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () async {
                        try {
                          await UnifiedInitializationService.reinitialize();
                          // إعادة تشغيل التطبيق
                          runApp(const MyApp());
                        } catch (retryError) {
                          debugPrint('❌ فشل في إعادة التهيئة: $retryError');
                        }
                      },
                      child: const Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }
    },
    (error, stackTrace) {
      // معالج الأخطاء العام
      debugPrint('❌ [MAIN] خطأ غير معالج في التطبيق: $error');
      debugPrint('Stack trace: $stackTrace');
    },
  );
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  // حالة التطبيق المبسطة
  bool _internetChecked = false;

  // المتغيرات الأساسية
  late final SubscribersRepository subscribersRepository;
  ThemeMode themeMode = ThemeMode.system;

  // متغيرات المزامنة المبسطة
  Timer? _autoSyncTimer;
  bool _logoutMessageSent = false; // ✅ فلاج لمنع رسائل تسجيل الخروج المتكررة

  void _toggleTheme() async {
    debugPrint(
      'تم استدعاء _toggleTheme. القيمة الحالية: '
      '${themeMode == ThemeMode.dark ? 'dark' : 'light'}',
    );
    setState(() {
      if (themeMode == ThemeMode.dark) {
        themeMode = ThemeMode.light;
      } else {
        themeMode = ThemeMode.dark;
      }
    });
    debugPrint(
      'تم تغيير themeMode إلى: '
      '${themeMode == ThemeMode.dark ? 'dark' : 'light'}',
    );
    final prefs = await SharedPreferences.getInstance();
    prefs.setString('themeMode', themeMode.toString().split('.').last);
  }

  void _showThemeStatus(BuildContext context) {
    String text;
    if (themeMode == ThemeMode.dark) {
      text = 'تم تفعيل الوضع الليلي';
    } else {
      text = 'تم تفعيل الوضع النهاري';
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(text, textAlign: TextAlign.center),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.symmetric(horizontal: 40, vertical: 20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    debugPrint('🔧 [MyApp] initState بدء...');
    subscribersRepository = SubscribersRepositoryImpl(SubscribersStorageImpl());
    _initThemeMode();
    debugPrint('✅ [MyApp] initState انتهى');
    // تم نقل منطق التحقق من تسجيل الدخول إلى SimpleRootScreen
  }

  // تم نقل منطق المزامنة والاتصال إلى النظام الموحد

  Future<void> _initThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final mode = prefs.getString('themeMode');
    if (mode == null) {
      // أول تشغيل: اعتمد على النظام
      final brightness =
          WidgetsBinding.instance.platformDispatcher.platformBrightness;
      setState(() {
        themeMode = brightness == Brightness.dark
            ? ThemeMode.dark
            : ThemeMode.light;
      });
    } else {
      setState(() {
        if (mode == 'dark') {
          themeMode = ThemeMode.dark;
        } else {
          themeMode = ThemeMode.light;
        }
      });
    }
  }

  @override
  void dispose() {
    debugPrint('[MAIN] _MyAppState.dispose called, cleaning up');
    _autoSyncTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🎨 [MyApp] build بدء... _internetChecked=$_internetChecked');
    // وضع NotificationListener حول MaterialApp لضمان استقبال الإشعارات دائمًا
    return NotificationListener<ServerStatusChangedNotification>(
      onNotification: (notification) {
        // تأجيل استدعاء _updateAnyDeviceConnected لتجنب مشاكل التهيئة
        Future.microtask(() => _updateAnyDeviceConnected());
        return true;
      },
      child: NotificationListener<BoardStatusChangedNotification>(
        onNotification: (notification) {
          // تأجيل استدعاء _updateAnyDeviceConnected لتجنب مشاكل التهيئة
          Future.microtask(() => _updateAnyDeviceConnected());
          return true;
        },
        child: MaterialApp(
          navigatorKey: navigatorKey,
          title: 'iTower',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          ),
          darkTheme: ThemeData(
            colorScheme: ColorScheme.fromSeed(
              seedColor: Colors.deepPurple,
              brightness: Brightness.dark,
            ),
            brightness: Brightness.dark,
          ),
          themeMode: themeMode,
          locale: const Locale('ar', ''),
          supportedLocales: const [Locale('ar', '')],
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          navigatorObservers: [routeObserver],
          home: Directionality(
            textDirection: TextDirection.rtl,
            child: _internetChecked
                ? SimpleRootScreen(
                    themeMode: themeMode,
                    onToggleTheme: _toggleTheme,
                    showThemeStatus: _showThemeStatus,
                    onLoginSuccess: () {
                      debugPrint(
                        '[MAIN] تم تسجيل الدخول بنجاح - النظام الموحد سيتولى الباقي',
                      );
                    },
                    onLogout: () {
                      // ✅ منع الرسائل المتكررة
                      if (!_logoutMessageSent) {
                        _logoutMessageSent = true;
                        debugPrint('[MAIN] تم تسجيل الخروج - تنظيف البيانات');
                        _autoSyncTimer?.cancel();

                        // إعادة تعيين الفلاج بعد ثانية واحدة
                        Future.delayed(const Duration(seconds: 1), () {
                          _logoutMessageSent = false;
                        });
                      }
                    },
                  )
                : InternetCheckScreen(
                    onInternetConnected: _onInternetConnected,
                  ),
          ),
        ),
      ),
    );
  }

  // تحديث حالة الاتصال العامة (أي سيرفر أو لوحة متصل)
  Future<void> _updateAnyDeviceConnected() async {
    final servers = await DBHelper.instance.getAllServers();
    final boards = await DBHelper.instance.getAllBoards();
    final anyConnected =
        servers.any(
          (s) =>
              s['connected'] == 1 ||
              s['connected'] == true ||
              s['connected'] == '1',
        ) ||
        boards.any(
          (b) =>
              b['connected'] == 1 ||
              b['connected'] == true ||
              b['connected'] == '1',
        );
    anyDeviceConnectedNotifier.value = anyConnected;
  }

  // منطق مركزي لتغيير التدرج اللوني بناءً على الرسائل في جميع الشاشات
  void updateGradientByMessage(String? message) {
    if (message == 'تم الاتصال بنجاح (الاتصال سيبقى مفتوحًا حتى تضغط قطع)') {
      anyDeviceConnectedNotifier.value = true;
    } else if (message == 'تم قطع الاتصال مع السيرفر') {
      anyDeviceConnectedNotifier.value = false;
    }
  }

  /// معالج عند نجاح الاتصال بالإنترنت
  Future<void> _onInternetConnected() async {
    debugPrint('🌐 [MAIN] تم تأكيد الاتصال بالإنترنت - التطبيق جاهز');

    try {
      // النظام الموحد تم تهيئته بالفعل في main()
      // لا حاجة لتهيئة Supabase مرة أخرى
      debugPrint('✅ [MAIN] النظام الموحد جاهز بالفعل');

      // تحديث حالة الاتصال
      debugPrint('🔧 [MAIN] تحديث حالة الاتصال...');
      await _updateAnyDeviceConnected();
      debugPrint('✅ [MAIN] تم تحديث حالة الاتصال');

      // الانتقال للشاشة الرئيسية
      debugPrint('🔧 [MAIN] الانتقال للشاشة الرئيسية...');
      if (mounted) {
        setState(() {
          _internetChecked = true;
        });
        debugPrint('✅ [MAIN] تم تحديث _internetChecked = true');
      } else {
        debugPrint('❌ [MAIN] Widget غير mounted!');
      }

      debugPrint('🎉 [MAIN] التطبيق جاهز للاستخدام');
    } catch (e) {
      debugPrint('❌ [MAIN] خطأ في تهيئة التطبيق: $e');

      // في حالة الخطأ، إظهار رسالة خطأ
      if (mounted && navigatorKey.currentContext != null) {
        ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
          SnackBar(
            content: Text('خطأ في تهيئة التطبيق: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }
}
