# دليل إعداد نظام الموظفين في Supabase

## 📋 الخطوات المطلوبة

### 1. تنفيذ SQL Script في Supabase

1. **افتح Supabase Dashboard**
   - اذهب إلى: https://supabase.com/dashboard
   - اختر مشروعك

2. **افتح SQL Editor**
   - من القائمة الجانبية، اختر "SQL Editor"
   - أو اذهب مباشرة إلى: https://supabase.com/dashboard/project/YOUR_PROJECT_ID/sql

3. **تنفيذ الـ Script**
   - انسخ محتوى ملف `supabase_employee_system_setup.sql`
   - الصقه في SQL Editor
   - اضغط "Run" أو Ctrl+Enter

### 2. التحقق من نجاح الإعداد

بعد تنفيذ الـ script، تأكد من إنشاء الجداول التالية:

#### الجداول الجديدة:
- ✅ `employees` - جدول الموظفين
- ✅ `employee_sessions` - جلسات الموظفين  
- ✅ `employee_activity_logs` - سجل أنشطة الموظفين

#### الدوال الجديدة:
- ✅ `verify_employee_password()` - التحقق من كلمة مرور الموظف
- ✅ `create_employee()` - إنشاء موظف جديد
- ✅ `log_employee_activity()` - تسجيل نشاط الموظف

### 3. اختبار النظام

#### إنشاء موظف تجريبي:
```sql
SELECT create_employee(
    'YOUR_USER_ID_HERE'::uuid,  -- معرف المدير (من auth.users)
    'موظف تجريبي',              -- اسم الموظف
    'test_employee',            -- اسم المستخدم
    'password123',              -- كلمة المرور
    '{"viewSubscribers": true, "addSubscribers": true}'::jsonb  -- الصلاحيات
);
```

#### اختبار تسجيل الدخول:
```sql
SELECT * FROM verify_employee_password('test_employee', 'password123');
```

## 🔒 الأمان والصلاحيات

### Row Level Security (RLS)
- تم تفعيل RLS على جميع الجداول
- كل مدير يرى موظفيه فقط
- الموظفون لا يمكنهم الوصول لبيانات بعضهم البعض

### تشفير كلمات المرور
- يتم تشفير كلمات المرور باستخدام bcrypt
- لا يتم حفظ كلمات المرور بشكل واضح أبداً

## 📊 هيكل البيانات

### جدول employees
```sql
- id (UUID) - المعرف الفريد
- manager_id (UUID) - معرف المدير
- employee_name (VARCHAR) - اسم الموظف
- username (VARCHAR) - اسم المستخدم (فريد)
- password_hash (VARCHAR) - كلمة المرور المشفرة
- permissions (JSONB) - الصلاحيات
- is_active (BOOLEAN) - حالة النشاط
- created_at, updated_at - طوابع زمنية
```

### جدول employee_sessions
```sql
- id (UUID) - المعرف الفريد
- employee_id (UUID) - معرف الموظف
- device_info (TEXT) - معلومات الجهاز
- login_time - وقت تسجيل الدخول
- last_activity - آخر نشاط
- is_active (BOOLEAN) - حالة الجلسة
```

### جدول employee_activity_logs
```sql
- id (UUID) - المعرف الفريد
- employee_id (UUID) - معرف الموظف
- manager_id (UUID) - معرف المدير
- action (VARCHAR) - نوع العملية
- details (TEXT) - تفاصيل العملية
- timestamp - وقت العملية
```

## 🚀 الخطوات التالية

بعد تنفيذ الإعداد في Supabase:

1. **اختبر إنشاء موظف تجريبي**
2. **تأكد من عمل تسجيل الدخول**
3. **اختبر تسجيل الأنشطة**
4. **شغل التطبيق واختبر النظام**

## ⚠️ ملاحظات مهمة

- **احتفظ بنسخة احتياطية** من قاعدة البيانات قبل التنفيذ
- **اختبر النظام** في بيئة التطوير أولاً
- **تأكد من صحة معرف المدير** عند إنشاء الموظفين
- **استخدم كلمات مرور قوية** للموظفين

## 🆘 في حالة وجود مشاكل

إذا واجهت أي مشاكل:

1. **تحقق من الأخطاء** في SQL Editor
2. **تأكد من الصلاحيات** في Supabase
3. **راجع logs** التطبيق
4. **تواصل معي** لحل المشكلة

---

**تم إنشاء هذا الدليل في:** 2025-07-29  
**الإصدار:** 1.0  
**الحالة:** جاهز للتنفيذ ✅
