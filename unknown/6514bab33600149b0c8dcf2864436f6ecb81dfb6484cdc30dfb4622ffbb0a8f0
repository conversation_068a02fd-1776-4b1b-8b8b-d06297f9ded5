import '../data/subscriber_model.dart';

abstract class SubscribersRepository {
  Future<List<Subscriber>> getAllSubscribers({int? boardId});
  Future<void> addSubscriber(Subscriber subscriber);
  Future<void> updateSubscriber(Subscriber subscriber);
  Future<void> deleteSubscriber(int id);
  Future<void> payDebt(int subscriberId, double amount, {String? note});
  Future<Subscriber?> getSubscriberById(int id);
}
