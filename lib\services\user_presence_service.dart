import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:device_info_plus/device_info_plus.dart';

import 'dart:async';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'account_service.dart';
import 'deleted_account_detector.dart';
import '../core/managers/internet_status_manager.dart';

/// خدمة تتبع حالة اتصال المستخدمين
class UserPresenceService {
  static Timer? _heartbeatTimer;
  static bool _isInitialized = false;
  static String? _currentDeviceId;
  static String? _currentUserId;

  static const Duration _heartbeatInterval = Duration(minutes: 2);
  static const Duration _connectionTimeout = Duration(seconds: 10);

  /// تهيئة خدمة تتبع الحالة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('🔄 [PRESENCE] تهيئة خدمة تتبع الحالة...');

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('⚠️ [PRESENCE] لا يوجد مستخدم مسجل دخول');
        return;
      }

      _currentUserId = user.id;
      _currentDeviceId = await _getDeviceId();

      // تسجيل الاتصال الأولي
      final connectSuccess = await _connectUser();

      // بدء heartbeat timer حتى لو فشل الاتصال الأولي
      _startHeartbeat();

      // مراقبة تغييرات الاتصال
      _setupConnectivityListener();

      _isInitialized = true;

      if (connectSuccess) {
        debugPrint('✅ [PRESENCE] تم تهيئة خدمة تتبع الحالة بنجاح');
      } else {
        debugPrint('⚠️ [PRESENCE] تم تهيئة الخدمة مع فشل في الاتصال الأولي');
      }
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في تهيئة خدمة تتبع الحالة: $e');
    }
  }

  /// تسجيل اتصال المستخدم
  static Future<bool> _connectUser() async {
    try {
      if (_currentUserId == null || _currentDeviceId == null) return false;

      debugPrint('🔗 [PRESENCE] تسجيل اتصال المستخدم...');

      final deviceInfo = await _getDeviceInfo();

      final result = await Supabase.instance.client
          .rpc(
            'user_connect',
            params: {
              'user_id_param': _currentUserId,
              'device_id_param': _currentDeviceId,
              'device_name_param': deviceInfo['name'],
              'device_type_param': deviceInfo['type'],
              'app_version_param': deviceInfo['version'],
              'location_info_param': deviceInfo['location'],
            },
          )
          .timeout(_connectionTimeout);

      if (result == true) {
        debugPrint('✅ [PRESENCE] تم تسجيل الاتصال بنجاح');
        return true;
      } else {
        debugPrint('❌ [PRESENCE] فشل في تسجيل الاتصال');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في تسجيل الاتصال: $e');
      return false;
    }
  }

  /// إرسال heartbeat للسيرفر
  static Future<bool> _sendHeartbeat() async {
    try {
      if (_currentUserId == null || _currentDeviceId == null) return false;

      // فحص الاتصال بالإنترنت أولاً
      final hasConnection = InternetStatusManager.isConnected;
      if (!hasConnection) {
        debugPrint('⚠️ [PRESENCE] لا يوجد اتصال بالإنترنت - تخطي heartbeat');
        return false;
      }

      final result = await Supabase.instance.client
          .rpc(
            'user_heartbeat',
            params: {
              'user_id_param': _currentUserId,
              'device_id_param': _currentDeviceId,
            },
          )
          .timeout(_connectionTimeout);

      if (result == true) {
        debugPrint('💓 [PRESENCE] تم إرسال heartbeat بنجاح');
        return true;
      } else {
        debugPrint('⚠️ [PRESENCE] فشل في إرسال heartbeat');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في إرسال heartbeat: $e');
      return false;
    }
  }

  /// قطع اتصال المستخدم
  static Future<bool> disconnect() async {
    try {
      if (_currentUserId == null || _currentDeviceId == null) return false;

      debugPrint('🔌 [PRESENCE] قطع اتصال المستخدم...');

      // إيقاف heartbeat timer
      _stopHeartbeat();

      final result = await Supabase.instance.client
          .rpc(
            'user_disconnect',
            params: {
              'user_id_param': _currentUserId,
              'device_id_param': _currentDeviceId,
            },
          )
          .timeout(_connectionTimeout);

      if (result == true) {
        debugPrint('✅ [PRESENCE] تم قطع الاتصال بنجاح');
        _isInitialized = false;
        return true;
      } else {
        debugPrint('❌ [PRESENCE] فشل في قطع الاتصال');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في قطع الاتصال: $e');
      return false;
    }
  }

  /// بدء heartbeat timer (محدث لفحص حالة الحساب)
  static void _startHeartbeat() {
    _stopHeartbeat(); // إيقاف أي timer موجود

    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) async {
      // فحص حالة الحساب قبل إرسال heartbeat
      final shouldStop = await _shouldStopHeartbeatForAccount();
      if (shouldStop) {
        debugPrint('🚫 [PRESENCE] إيقاف heartbeat - الحساب منتهي/محظور');
        _stopHeartbeat();
        return;
      }

      final success = await _sendHeartbeat();
      if (!success) {
        debugPrint('⚠️ [PRESENCE] فشل heartbeat - محاولة إعادة الاتصال...');
        await _connectUser();
      }
    });

    debugPrint(
      '⏰ [PRESENCE] تم بدء heartbeat timer - كل ${_heartbeatInterval.inMinutes} دقيقة',
    );
  }

  /// فحص إذا كان يجب إيقاف heartbeat للحساب
  static Future<bool> _shouldStopHeartbeatForAccount() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return true;

      // فحص إذا كان الحساب منتهي محلياً
      if (await DeletedAccountDetector.isAccountExpiredLocally()) {
        return true;
      }

      // فحص علامة إيقاف heartbeat
      final prefs = await SharedPreferences.getInstance();
      if (prefs.getBool('account_expired_stop_heartbeat') == true) {
        return true;
      }

      // فحص حالة الحساب من قاعدة البيانات (كل 10 دقائق فقط لتوفير الموارد)
      final lastCheck = prefs.getInt('last_account_status_check') ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - lastCheck > 600000) {
        // 10 دقائق
        try {
          final accountData = await AccountService.getAccountDataV2(user.id);
          final accountStatus = accountData?['account_status'] as String?;

          await prefs.setInt('last_account_status_check', now);

          if (accountStatus == 'expired' || accountStatus == 'banned') {
            return true;
          }

          if (accountStatus == 'trial') {
            final daysLeft = accountData?['trial_days_remaining'] as int? ?? 0;
            if (daysLeft <= 0) {
              return true;
            }
          }
        } catch (e) {
          debugPrint('⚠️ [PRESENCE] خطأ في فحص حالة الحساب: $e');
          // في حالة الخطأ، نتابع heartbeat
        }
      }

      return false;
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في فحص حالة الحساب للـ heartbeat: $e');
      return false;
    }
  }

  /// إيقاف heartbeat timer
  static void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
    debugPrint('⏹️ [PRESENCE] تم إيقاف heartbeat timer');
  }

  /// مراقبة تغييرات الاتصال (محدث للنظام الجديد)
  static void _setupConnectivityListener() {
    InternetStatusManager.statusStream.listen((isConnected) async {
      if (isConnected) {
        debugPrint('🌐 [PRESENCE] عاد الاتصال بالإنترنت - إعادة تسجيل الاتصال');
        await _connectUser();
        if (!_isHeartbeatRunning()) {
          _startHeartbeat();
        }
      } else {
        debugPrint('📵 [PRESENCE] انقطع الاتصال بالإنترنت');
        _stopHeartbeat();
      }
    });
  }

  /// فحص إذا كان heartbeat يعمل
  static bool _isHeartbeatRunning() {
    return _heartbeatTimer != null && _heartbeatTimer!.isActive;
  }

  /// الحصول على معرف الجهاز
  static Future<String> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return 'android_${androidInfo.fingerprint}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return 'ios_${iosInfo.identifierForVendor ?? 'unknown'}';
      }

      return 'unknown_${DateTime.now().millisecondsSinceEpoch}';
    } catch (e) {
      return 'error_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// الحصول على معلومات الجهاز
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'name': '${androidInfo.brand} ${androidInfo.model}',
          'type': 'android',
          'version': '1.0.0', // يمكن جلبها من package_info
          'location': null, // يمكن إضافة الموقع لاحقاً
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'name': '${iosInfo.name} (${iosInfo.model})',
          'type': 'ios',
          'version': '1.0.0',
          'location': null,
        };
      }

      return {
        'name': 'جهاز غير معروف',
        'type': 'unknown',
        'version': '1.0.0',
        'location': null,
      };
    } catch (e) {
      return {
        'name': 'جهاز غير معروف',
        'type': 'unknown',
        'version': '1.0.0',
        'location': null,
      };
    }
  }

  /// جلب المستخدمين المتصلين (للنظام الداخلي فقط - لا يُعرض للمستخدمين)
  static Future<List<Map<String, dynamic>>> _getOnlineUsersForSystem() async {
    try {
      debugPrint('👥 [PRESENCE] جلب المستخدمين المتصلين للنظام الداخلي...');

      final response = await Supabase.instance.client
          .rpc('get_online_users')
          .timeout(_connectionTimeout);

      final users = List<Map<String, dynamic>>.from(response);
      debugPrint('✅ [PRESENCE] تم جلب ${users.length} مستخدم متصل للنظام');

      return users;
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في جلب المستخدمين المتصلين: $e');
      return [];
    }
  }

  /// جلب إحصائيات الحضور (للنظام الداخلي فقط - لا يُعرض للمستخدمين)
  static Future<Map<String, dynamic>?> _getPresenceStatsForSystem() async {
    try {
      debugPrint('📊 [PRESENCE] جلب إحصائيات الحضور للنظام الداخلي...');

      final response = await Supabase.instance.client
          .from('user_presence_summary')
          .select('*')
          .single()
          .timeout(_connectionTimeout);

      debugPrint('✅ [PRESENCE] تم جلب إحصائيات الحضور للنظام');
      return response;
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في جلب إحصائيات الحضور: $e');
      return null;
    }
  }

  /// تحديث إحصائيات النظام (للاستخدام الداخلي فقط)
  static Future<void> updateSystemStats() async {
    try {
      // جلب البيانات للنظام الداخلي فقط
      await _getOnlineUsersForSystem();
      await _getPresenceStatsForSystem();

      debugPrint('✅ [PRESENCE] تم تحديث إحصائيات النظام');
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في تحديث إحصائيات النظام: $e');
    }
  }

  /// جلب عدد المستخدمين المتصلين فقط (آمن للعرض)
  static Future<int> getOnlineUsersCount() async {
    try {
      debugPrint('🔢 [PRESENCE] جلب عدد المستخدمين المتصلين...');

      final response = await Supabase.instance.client
          .rpc('get_online_users_count')
          .timeout(_connectionTimeout);

      final count = response as int? ?? 0;
      debugPrint('✅ [PRESENCE] عدد المستخدمين المتصلين: $count');

      return count;
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في جلب عدد المستخدمين المتصلين: $e');
      return 0;
    }
  }

  /// جلب إحصائيات عامة آمنة (بدون تفاصيل شخصية)
  static Future<Map<String, int>> getSafeStats() async {
    try {
      debugPrint('📊 [PRESENCE] جلب الإحصائيات العامة الآمنة...');

      final response = await Supabase.instance.client
          .rpc('get_safe_presence_stats')
          .timeout(_connectionTimeout);

      final stats = Map<String, int>.from(response ?? {});
      debugPrint('✅ [PRESENCE] تم جلب الإحصائيات العامة الآمنة');

      return stats;
    } catch (e) {
      debugPrint('❌ [PRESENCE] خطأ في جلب الإحصائيات العامة: $e');
      return {'total_users': 0, 'online_users': 0, 'active_sessions': 0};
    }
  }

  /// تنظيف الخدمة
  static void dispose() {
    _stopHeartbeat();
    _isInitialized = false;
    _currentDeviceId = null;
    _currentUserId = null;
    debugPrint('🧹 [PRESENCE] تم تنظيف خدمة تتبع الحالة');
  }

  /// إعادة تهيئة الخدمة
  static Future<void> reinitialize() async {
    dispose();
    await initialize();
  }

  /// فحص حالة الخدمة
  static bool get isInitialized => _isInitialized;
  static bool get isHeartbeatActive => _isHeartbeatRunning();
  static String? get currentUserId => _currentUserId;
  static String? get currentDeviceId => _currentDeviceId;
}
