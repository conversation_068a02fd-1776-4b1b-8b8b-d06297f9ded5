// شاشة عرض سجل نشاط الموظفين

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/activity_logger.dart';
import '../services/employee_service.dart';
import '../models/employee_models.dart';

class ActivityLogScreen extends StatefulWidget {
  const ActivityLogScreen({super.key});

  @override
  State<ActivityLogScreen> createState() => _ActivityLogScreenState();
}

class _ActivityLogScreenState extends State<ActivityLogScreen> {
  List<Map<String, dynamic>> _activityLogs = [];
  bool _isLoading = true;
  bool _isManagerMode = false;
  DateTime? _fromDate;
  DateTime? _toDate;
  String _selectedFilter = 'all'; // all, today, week, month

  @override
  void initState() {
    super.initState();
    _checkUserMode();
  }

  Future<void> _checkUserMode() async {
    final isManager = await EmployeeService.isManagerMode();
    setState(() {
      _isManagerMode = isManager;
    });
    _loadActivityLogs();
  }

  Future<void> _loadActivityLogs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<Map<String, dynamic>> logs;

      if (_isManagerMode) {
        // المدير يرى جميع العمليات
        logs = await ActivityLogger.getAllEmployeesActivityLogs(
          limit: 100,
          fromDate: _fromDate,
          toDate: _toDate,
        );
      } else {
        // الموظف يرى عملياته فقط
        final session = await EmployeeService.getCurrentSession();
        if (session != null) {
          final employeeLogs = await ActivityLogger.getEmployeeActivityLogs(
            employeeId: session.employeeId,
            limit: 100,
            fromDate: _fromDate,
            toDate: _toDate,
          );
          
          logs = employeeLogs.map((log) => {
            ...log.toMap(),
            'employee_name': session.employeeName,
            'employee_email': session.employeeEmail,
          }).toList();
        } else {
          logs = [];
        }
      }

      setState(() {
        _activityLogs = logs;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل سجل النشاط: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyDateFilter(String filter) {
    setState(() {
      _selectedFilter = filter;
      final now = DateTime.now();
      
      switch (filter) {
        case 'today':
          _fromDate = DateTime(now.year, now.month, now.day);
          _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        case 'week':
          _fromDate = now.subtract(Duration(days: now.weekday - 1));
          _fromDate = DateTime(_fromDate!.year, _fromDate!.month, _fromDate!.day);
          _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        case 'month':
          _fromDate = DateTime(now.year, now.month, 1);
          _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        default:
          _fromDate = null;
          _toDate = null;
      }
    });
    
    _loadActivityLogs();
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _fromDate != null && _toDate != null
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = DateTime(picked.end.year, picked.end.month, picked.end.day, 23, 59, 59);
        _selectedFilter = 'custom';
      });
      _loadActivityLogs();
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(_isManagerMode ? 'سجل نشاط الموظفين' : 'سجل نشاطي'),
        backgroundColor: colorScheme.surfaceContainer,
        actions: [
          IconButton(
            onPressed: _selectDateRange,
            icon: const Icon(Icons.date_range),
            tooltip: 'تحديد فترة زمنية',
          ),
          IconButton(
            onPressed: _loadActivityLogs,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الفلاتر
          _buildFilterBar(colorScheme),
          
          // قائمة النشاط
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _activityLogs.isEmpty
                    ? _buildEmptyState(colorScheme)
                    : _buildActivityList(colorScheme),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterBar(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(color: colorScheme.outline.withValues(alpha: 0.2)),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterChip('الكل', 'all', colorScheme),
            const SizedBox(width: 8),
            _buildFilterChip('اليوم', 'today', colorScheme),
            const SizedBox(width: 8),
            _buildFilterChip('هذا الأسبوع', 'week', colorScheme),
            const SizedBox(width: 8),
            _buildFilterChip('هذا الشهر', 'month', colorScheme),
            const SizedBox(width: 8),
            _buildFilterChip('فترة مخصصة', 'custom', colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, ColorScheme colorScheme) {
    final isSelected = _selectedFilter == value;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (value == 'custom') {
          _selectDateRange();
        } else {
          _applyDateFilter(value);
        }
      },
      backgroundColor: isSelected ? colorScheme.primaryContainer : null,
      selectedColor: colorScheme.primaryContainer,
      labelStyle: TextStyle(
        color: isSelected ? colorScheme.onPrimaryContainer : null,
      ),
    );
  }

  Widget _buildEmptyState(ColorScheme colorScheme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد نشاط مسجل',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم يتم تسجيل أي عمليات في الفترة المحددة',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList(ColorScheme colorScheme) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _activityLogs.length,
      itemBuilder: (context, index) {
        final log = _activityLogs[index];
        return _buildActivityCard(log, colorScheme);
      },
    );
  }

  Widget _buildActivityCard(Map<String, dynamic> log, ColorScheme colorScheme) {
    final timestamp = DateTime.parse(log['timestamp']);
    final timeFormat = DateFormat('dd/MM/yyyy - HH:mm');
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                _buildActionIcon(log['action'], colorScheme),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        log['action'] ?? 'عملية غير محددة',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_isManagerMode && log['employee_name'] != null)
                        Text(
                          'بواسطة: ${log['employee_name']}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
                Text(
                  timeFormat.format(timestamp),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            
            // التفاصيل
            if (log['details'] != null && log['details'].toString().isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  log['details'],
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionIcon(String? action, ColorScheme colorScheme) {
    IconData icon;
    Color backgroundColor;
    
    switch (action) {
      case 'إضافة مشترك':
        icon = Icons.person_add;
        backgroundColor = Colors.green;
        break;
      case 'تعديل مشترك':
        icon = Icons.edit;
        backgroundColor = Colors.blue;
        break;
      case 'تجديد اشتراك':
        icon = Icons.refresh;
        backgroundColor = Colors.orange;
        break;
      case 'تسديد دين':
        icon = Icons.payment;
        backgroundColor = Colors.purple;
        break;
      case 'إضافة معاملة':
        icon = Icons.add_circle;
        backgroundColor = Colors.teal;
        break;
      case 'حذف سجل':
        icon = Icons.delete;
        backgroundColor = Colors.red;
        break;
      case 'تصدير بيانات':
        icon = Icons.download;
        backgroundColor = Colors.indigo;
        break;
      case 'عرض تقرير':
        icon = Icons.analytics;
        backgroundColor = Colors.amber;
        break;
      case 'إدارة جهاز':
        icon = Icons.router;
        backgroundColor = Colors.cyan;
        break;
      case 'تسجيل دخول':
        icon = Icons.login;
        backgroundColor = Colors.green;
        break;
      case 'تسجيل خروج':
        icon = Icons.logout;
        backgroundColor = Colors.grey;
        break;
      default:
        icon = Icons.info;
        backgroundColor = colorScheme.primary;
    }
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        icon,
        color: backgroundColor,
        size: 20,
      ),
    );
  }
}
