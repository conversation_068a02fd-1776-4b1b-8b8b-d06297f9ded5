import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'add_board_screen.dart';
import 'dart:convert';
import 'dart:io';
import 'package:itower/db_helper.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'dart:typed_data';
import 'package:itower/features/main_home_screen.dart'; // لإشعار BoardStatusChangedNotification
import 'package:itower/main.dart'; // لاستيراد anyDeviceConnectedNotifier
import 'features/sync_progress_page.dart'; // شاشة المزامنة الاحترافية
import 'utils/api_helper.dart'; // لجلب الرصيد

// دالة تشفير AES CBC مع salt متوافقة مع CryptoJS/OpenSSL
String encryptWithOpenSSL(String plaintext, String passphrase) {
  final rnd = Random.secure();
  final salt = List<int>.generate(8, (_) => rnd.nextInt(256));
  final keyAndIV = _evpBytesToKey(passphrase.codeUnits, salt, 32, 16);
  final key = keyAndIV.sublist(0, 32);
  final iv = Uint8List.fromList(keyAndIV.sublist(32, 48));
  final cipher = encrypt.Encrypter(
    encrypt.AES(
      encrypt.Key(Uint8List.fromList(key)),
      mode: encrypt.AESMode.cbc,
      padding: null,
    ),
  );
  final padded = _pkcs7Pad(Uint8List.fromList(utf8.encode(plaintext)), 16);
  final encrypted = cipher.encryptBytes(padded, iv: encrypt.IV(iv));
  final prefix = utf8.encode('Salted__');
  final out = <int>[]
    ..addAll(prefix)
    ..addAll(salt)
    ..addAll(encrypted.bytes);
  return base64.encode(out);
}

List<int> _evpBytesToKey(
  List<int> password,
  List<int> salt,
  int keyLen,
  int ivLen,
) {
  final totalLen = keyLen + ivLen;
  var derived = <int>[];
  var block = <int>[];
  while (derived.length < totalLen) {
    final hasher = md5.convert([...block, ...password, ...salt]);
    block = hasher.bytes;
    derived.addAll(block);
  }
  return derived.sublist(0, totalLen);
}

Uint8List _pkcs7Pad(Uint8List data, int blockSize) {
  final pad = blockSize - (data.length % blockSize);
  final padded = Uint8List(data.length + pad)
    ..setAll(0, data)
    ..setAll(data.length, List.filled(pad, pad));
  return padded;
}

class BoardsListScreen extends StatefulWidget {
  const BoardsListScreen({super.key});
  @override
  State<BoardsListScreen> createState() => _BoardsListScreenState();
}

class _BoardsListScreenState extends State<BoardsListScreen> {
  List<Map<String, dynamic>> boards = [];
  List<Map<String, dynamic>> filteredBoards = [];
  final Map<int, bool> isActuallyConnected = {};

  @override
  void initState() {
    super.initState();
    DBHelper.instance.createBoardsTableIfNotExists().then((_) async {
      await _loadBoards();
      // إعادة الاتصال التلقائي إذا كانت اللوحة متصلة في قاعدة البيانات
      for (int i = 0; i < boards.length; i++) {
        if (i < boards.length && boards.isNotEmpty) {
          if (boards[i]['connected'] == true ||
              boards[i]['connected'] == 1 ||
              boards[i]['connected'] == '1') {
            await _tryReconnectOrReset(i);
          }
        }
      }
    });
  }

  Future<void> _tryReconnectOrReset(int i) async {
    debugPrint(
      '[BoardsListScreen] _tryReconnectOrReset START for board index $i',
    );
    if (i < 0 || i >= boards.length || boards.isEmpty) {
      debugPrint('[BoardsListScreen] Invalid board index: $i');
      return;
    }
    final board = boards[i];
    try {
      final result = await tryConnect(board);
      debugPrint(
        '[BoardsListScreen] _tryReconnectOrReset after tryConnect, result: success=[32m${result['success']}[0m',
      );
      final updatedBoard = Map<String, dynamic>.from(board);
      updatedBoard['connected'] = result['success'] == true ? 1 : 0;
      if (result['token'] != null) updatedBoard['token'] = result['token'];
      final id = updatedBoard['id'];
      await DBHelper.instance.updateBoard(id, updatedBoard);
      debugPrint('[BoardsListScreen] _tryReconnectOrReset after updateBoard');
      await _loadBoards();
      debugPrint('[BoardsListScreen] _tryReconnectOrReset after _loadBoards');
      if (!mounted) return;
      debugPrint(
        '[BoardsListScreen] BoardStatusChangedNotification DISPATCHED (success=${result['success']})',
      );
      BoardStatusChangedNotification().dispatch(context);
    } catch (e) {
      debugPrint('[BoardsListScreen] _tryReconnectOrReset CATCH: $e');
      final updatedBoard = Map<String, dynamic>.from(board);
      updatedBoard['connected'] = 0;
      final id = updatedBoard['id'];
      await DBHelper.instance.updateBoard(id, updatedBoard);
      await _loadBoards();
      if (!mounted) return;
      debugPrint(
        '[BoardsListScreen] BoardStatusChangedNotification DISPATCHED (FAILED)',
      );
      BoardStatusChangedNotification().dispatch(context);
    }
    debugPrint(
      '[BoardsListScreen] _tryReconnectOrReset END for board index $i',
    );
  }

  // دالة تسجيل دخول احترافية مع إرجاع تفاصيل النتيجة
  Future<Map<String, dynamic>> tryConnect(Map<String, dynamic> board) async {
    // بناء رابط تسجيل الدخول بشكل مرن بدون افتراض .com أو دومين ثابت
    String url = board['url']?.toString().trim() ?? '';
    url = url.replaceAll(RegExp(r'[:/]+$'), '');
    final user = board['user'] ?? board['name'] ?? '';
    final pass = board['pass'] ?? '';
    final language = 'en';
    print('[CONNECT TRACE] --- بدء محاولة الاتصال بالموقع ---');
    print('[CONNECT TRACE] الرابط: $url');
    print('[CONNECT TRACE] اسم المستخدم: $user');
    print('[CONNECT TRACE] كلمة المرور: $pass');
    if (url.isEmpty) {
      print('[CONNECT TRACE] فشل: رابط الموقع فارغ');
      return {'success': false, 'message': 'رابط الموقع فارغ'};
    }
    try {
      // إذا كان الرابط يبدأ بـ http أو https استخدمه كما هو، وإلا أضف https://
      final loginUrl = url.startsWith('http')
          ? '$url/admin/api/index.php/api/login'
          : 'https://$url/admin/api/index.php/api/login';
      print('[CONNECT TRACE] إرسال طلب POST إلى: $loginUrl');
      final ioClient = HttpClient()
        ..badCertificateCallback = (cert, host, port) {
          print('[CONNECT TRACE] تجاهل خطأ شهادة SSL للخادم: $host:$port');
          return true;
        };
      final client = IOClient(ioClient);
      final uri = Uri.parse(loginUrl);
      // تشفير البيانات بطريقة CryptoJS/OpenSSL
      final loginData = {
        'username': user,
        'password': pass,
        'language': language,
      };
      final jsonString = jsonEncode(loginData);
      final payload = encryptWithOpenSSL(
        jsonString,
        'abcdefghijuklmno0123456789012345',
      );
      print('[CONNECT TRACE] البيانات المرسلة (payload): $payload');
      final response = await client.post(
        uri,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
          // تم حذف Origin وReferer وhost لجعل الكود مرن لأي موقع
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Accept-Language': 'en-US,en;q=0.9,ar-EG;q=0.8,ar;q=0.7',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
        },
        body: jsonEncode({'payload': payload}),
      );
      print('[CONNECT TRACE] كود الاستجابة: \\${response.statusCode}');
      print('[CONNECT TRACE] رؤوس الاستجابة: \\${response.headers}');
      print('[CONNECT TRACE] محتوى الرد: \\${response.body}');
      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        if (decoded['token'] != null) {
          print(
            '[CONNECT TRACE] تم تسجيل الدخول بنجاح. التوكن: \\${decoded['token']}',
          );
          final mutableBoard = Map<String, dynamic>.from(board);
          mutableBoard['token'] = decoded['token'];
          return {
            'success': true,
            'token': decoded['token'],
            'message': 'تم تسجيل الدخول بنجاح',
          };
        } else if (decoded['message'] != null) {
          print('[CONNECT TRACE] فشل: \\${decoded['message']}');
          return {'success': false, 'message': decoded['message']};
        }
        print('[CONNECT TRACE] فشل: لم يتم العثور على توكن في الرد');
        return {'success': false, 'message': 'لم يتم العثور على توكن في الرد'};
      }
      print('[CONNECT TRACE] فشل: كود الاستجابة \\${response.statusCode}');
      return {
        'success': false,
        'message': 'فشل تسجيل الدخول: كود الاستجابة \\${response.statusCode}',
      };
    } catch (e, st) {
      print('[CONNECT TRACE] استثناء أثناء الاتصال: $e');
      print('[CONNECT TRACE] Stacktrace: $st');
      return {'success': false, 'message': 'خطأ أثناء الاتصال: $e'};
    } finally {
      print('[CONNECT TRACE] --- نهاية محاولة الاتصال ---');
    }
  }

  // تحديث fetchSubscribers لاستخدام التوكن تلقائياً
  Future<List<Map<String, dynamic>>> fetchSubscribers(
    Map<String, dynamic> board, {
    String? token,
    String endpoint = '/admin/api/index.php/api/resources/subscribers',
  }) async {
    final url = board['url']?.toString().trim();
    final authToken = token ?? board['token'];
    if (url == null || url.isEmpty || authToken == null) return [];
    try {
      final fullUrl = url.startsWith('http')
          ? '$url$endpoint'
          : 'https://$url$endpoint';
      print('[SAS API DEBUG] إرسال طلب GET لجلب المشتركين: $fullUrl');
      print(
        '[SAS API DEBUG] رؤوس الطلب: {"Authorization": "Bearer $authToken"}',
      );
      final uri = Uri.parse(fullUrl);
      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $authToken',
          'Accept': 'application/json',
        },
      );
      print(
        '[SAS API DEBUG] كود الاستجابة لجلب المشتركين: ${response.statusCode}',
      );
      print('[SAS API DEBUG] رؤوس الاستجابة: ${response.headers}');
      print('[SAS API DEBUG] محتوى الرد: ${response.body}');
      if (response.statusCode == 200) {
        final List<dynamic> data = response.body.startsWith('[')
            ? List<Map<String, dynamic>>.from(jsonDecode(response.body))
            : [];
        print('[SAS API DEBUG] تم جلب ${data.length} مشترك من السيرفر');
        return data.cast<Map<String, dynamic>>();
      }
      print('[SAS API DEBUG] لم يتم جلب أي بيانات من السيرفر');
      return [];
    } catch (e) {
      print('[SAS API DEBUG] ERROR: $e');
      return [];
    }
  }

  Future<void> _loadBoards() async {
    final list = await DBHelper.instance.getAllBoards();
    if (!mounted) return;
    setState(() {
      boards = list;
      filteredBoards = List.from(boards);
    });
  }

  Future<void> _addBoard(Map<String, dynamic> board) async {
    final result = await tryConnect(board);
    final boardDb = {
      'name': board['name'] ?? '',
      'url': board['url'] ?? '',
      'user': board['user'] ?? '',
      'pass': board['pass'] ?? '',
      'token': result['token'] ?? '',
      'connected': result['success'] == true ? 1 : 0,
    };

    // إذا نجح تسجيل الدخول، جلب الرصيد
    if (result['success'] == true && result['token'] != null) {
      try {
        final balanceResult = await fetchBoardBalance(
          board: boardDb,
          token: result['token'],
        );

        if (balanceResult['success'] == true) {
          boardDb['balance'] = balanceResult['balance'];
          boardDb['balance_text'] = balanceResult['balanceText'];
          boardDb['currency'] = balanceResult['currency'];
          debugPrint(
            '[BOARD_BALANCE] تم جلب الرصيد: ${balanceResult['balanceText']}',
          );
        } else {
          debugPrint(
            '[BOARD_BALANCE] فشل في جلب الرصيد: ${balanceResult['message']}',
          );
        }
      } catch (e) {
        debugPrint('[BOARD_BALANCE] خطأ في جلب الرصيد: $e');
      }
    }
    await DBHelper.instance.insertBoard(boardDb);
    await _loadBoards();
    if (mounted) {
      BoardStatusChangedNotification().dispatch(
        Navigator.of(context, rootNavigator: true).context,
      );
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(result['message'] ?? '')));
      if (result['message'] ==
          'تم الاتصال بنجاح (الاتصال سيبقى مفتوحًا حتى تضغط قطع)') {
        anyDeviceConnectedNotifier.value = true;
      } else if (result['message'] == 'تم قطع الاتصال مع السيرفر') {
        anyDeviceConnectedNotifier.value = false;
      }
    }
  }

  Future<void> _updateBoard(int i, Map<String, dynamic> board) async {
    if (i < 0 || i >= boards.length || boards.isEmpty) {
      debugPrint('[BoardsListScreen] Invalid board index for update: $i');
      return;
    }
    final result = await tryConnect(board);
    final id = boards[i]['id'];
    final boardDb = {
      'name': board['name'] ?? '',
      'url': board['url'] ?? '',
      'user': board['user'] ?? '',
      'pass': board['pass'] ?? '',
      'token': result['token'] ?? board['token'] ?? '',
      'connected': result['success'] == true ? 1 : 0,
    };
    await DBHelper.instance.updateBoard(id, boardDb);
    await _loadBoards();
    if (context.mounted) {
      BoardStatusChangedNotification().dispatch(
        Navigator.of(context, rootNavigator: true).context,
      );
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(result['message'] ?? '')));
      if (result['message'] ==
          'تم الاتصال بنجاح (الاتصال سيبقى مفتوحًا حتى تضغط قطع)') {
        anyDeviceConnectedNotifier.value = true;
      } else if (result['message'] == 'تم قطع الاتصال مع السيرفر') {
        anyDeviceConnectedNotifier.value = false;
      }
    }
  }

  Future<void> _removeBoard(int i) async {
    if (i < 0 || i >= boards.length || boards.isEmpty) {
      debugPrint('[BoardsListScreen] Invalid board index for removal: $i');
      return;
    }
    final id = boards[i]['id'];
    await DBHelper.instance.deleteBoard(id);
    await _loadBoards();
  }

  // عند نجاح الاتصال، تبقى اللوحة متصلة حتى يتم قطع الاتصال يدويًا
  Future<void> _setBoardConnected(
    int i,
    bool connected, {
    String? token,
  }) async {
    if (i < 0 || i >= boards.length || boards.isEmpty) {
      debugPrint('[BoardsListScreen] Invalid board index for connection: $i');
      return;
    }
    final id = boards[i]['id'];
    final updatedBoard = Map<String, dynamic>.from(boards[i]);
    updatedBoard['connected'] = connected == true ? 1 : 0;
    if (token != null) {
      updatedBoard['token'] = token;
    }
    await DBHelper.instance.updateBoard(id, updatedBoard);
    await _loadBoards();
    if (context.mounted) {
      debugPrint(
        '[BoardsListScreen] BoardStatusChangedNotification DISPATCHED (_setBoardConnected: connected=\x1B[32m$connected\x1B[0m)',
      );
      BoardStatusChangedNotification().dispatch(
        Navigator.of(context, rootNavigator: true).context,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),

                  const SizedBox(height: 32),

                  // محتوى الشاشة
                  boards.isEmpty
                      ? _buildEmptyState(colorScheme, isDark)
                      : _buildBoardsList(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء قائمة اللوحات
  Widget _buildBoardsList(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        for (int i = 0; i < boards.length; i++)
          _buildBoardCard(boards[i], i, colorScheme, isDark),
      ],
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
      child: Column(
        children: [
          // الأيقونة الرئيسية في الوسط
          Container(
            margin: const EdgeInsets.only(bottom: 18),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: colorScheme.primary.withValues(alpha: 0.18),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 48,
              backgroundColor: Colors.white.withValues(
                alpha: isDark ? 0.08 : 0.18,
              ),
              child: Icon(Icons.dns, color: colorScheme.primary, size: 54),
            ),
          ),

          // العنوان والوصف
          Text(
            'SAS4',
            style: TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: colorScheme.onPrimary,
              letterSpacing: 1,
              shadows: [
                Shadow(
                  color: colorScheme.shadow.withValues(alpha: 0.13),
                  blurRadius: 4,
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إدارة اللوحات والاتصال بالخوادم',
            style: TextStyle(
              fontSize: 16,
              color: colorScheme.onPrimary.withValues(alpha: 0.92),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // بناء حالة عدم وجود لوحات
  Widget _buildEmptyState(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.dns_outlined,
              size: 64,
              color: colorScheme.primary.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد لوحة مضافة',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'أضف لوحة جديدة للبدء في إدارة المشتركين',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            // إظهار زر الإضافة فقط إذا لم تكن هناك لوحات موجودة
            if (boards.isEmpty)
              ElevatedButton.icon(
                onPressed: () async {
                  await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (_) => AddBoardScreen(
                        onBoardAdded: (board) async {
                          await _addBoard(board);
                        },
                      ),
                    ),
                  );
                  await _loadBoards();
                },
                icon: const Icon(Icons.add),
                label: const Text('إضافة لوحة جديدة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                ),
              )
            else
              // رسالة توضيحية عندما تكون هناك لوحة موجودة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: colorScheme.primary,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'يمكن إضافة لوحة واحدة فقط. استخدم زر التعديل لتغيير بيانات اللوحة الحالية.',
                        style: TextStyle(
                          color: colorScheme.primary,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة لوحة واحدة
  Widget _buildBoardCard(
    Map<String, dynamic> board,
    int index,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    final isConnected =
        board['connected'] == true ||
        board['connected'] == 1 ||
        board['connected'] == '1';

    return Card(
      elevation: 0,
      margin: const EdgeInsets.only(bottom: 16),
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            _buildBoardHeader(board, isConnected, colorScheme),
            const SizedBox(height: 16),

            // معلومات اللوحة
            _buildBoardInfo(board, colorScheme),
            const SizedBox(height: 20),

            // أزرار التحكم
            _buildBoardActions(board, index, isConnected, colorScheme),
          ],
        ),
      ),
    );
  }

  // بناء رأس بطاقة اللوحة
  Widget _buildBoardHeader(
    Map<String, dynamic> board,
    bool isConnected,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        // أيقونة الحالة
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isConnected
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            isConnected ? Icons.link : Icons.link_off,
            color: isConnected ? Colors.green : Colors.red,
            size: 24,
          ),
        ),

        const SizedBox(width: 16),

        // اسم اللوحة
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                board['name'] ?? '',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                board['url'] ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        // مؤشر الحالة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isConnected
                ? Colors.green.withValues(alpha: 0.1)
                : Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isConnected
                  ? Colors.green.withValues(alpha: 0.3)
                  : Colors.red.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            isConnected ? 'متصل' : 'غير متصل',
            style: TextStyle(
              color: isConnected ? Colors.green : Colors.red,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  // بناء معلومات اللوحة
  Widget _buildBoardInfo(Map<String, dynamic> board, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Icon(Icons.language, color: colorScheme.primary, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              board['url'] ?? '',
              style: TextStyle(
                color: colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء أزرار التحكم في اللوحة
  Widget _buildBoardActions(
    Map<String, dynamic> board,
    int index,
    bool isConnected,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primary.withValues(alpha: 0.03),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.08)),
      ),
      child: Row(
        children: [
          // زر الاتصال/قطع الاتصال
          Expanded(
            child: _buildModernActionButton(
              label: isConnected ? 'قطع' : 'اتصال',
              icon: isConnected ? Icons.link_off : Icons.link,
              color: isConnected ? Colors.red : Colors.green,
              colorScheme: colorScheme,
              onTap: () async {
                if (isConnected) {
                  await _setBoardConnected(index, false);
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم قطع الاتصال مع السيرفر'),
                      ),
                    );
                  }
                  anyDeviceConnectedNotifier.value = false;
                } else {
                  final result = await tryConnect(board);
                  await _setBoardConnected(
                    index,
                    result['success'] == true,
                    token: result['token'],
                  );
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          result['message'] ?? 'فشل الاتصال بالموقع',
                        ),
                      ),
                    );
                  }
                  if (result['message'] ==
                      'تم الاتصال بنجاح (الاتصال سيبقى مفتوحًا حتى تضغط قطع)') {
                    anyDeviceConnectedNotifier.value = true;
                  } else if (result['message'] == 'تم قطع الاتصال مع السيرفر') {
                    anyDeviceConnectedNotifier.value = false;
                  }
                }
              },
            ),
          ),

          const SizedBox(width: 8),

          // زر المزامنة
          Expanded(
            child: _buildModernActionButton(
              label: 'مزامنة',
              icon: Icons.sync,
              color: Colors.orange,
              colorScheme: colorScheme,
              isEnabled: isConnected,
              onTap: isConnected
                  ? () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => SyncProgressPage(board: board),
                        ),
                      );
                    }
                  : null,
            ),
          ),

          const SizedBox(width: 8),

          // زر التعديل
          Expanded(
            child: _buildModernActionButton(
              label: 'تعديل',
              icon: Icons.edit,
              color: Colors.blue.shade700,
              colorScheme: colorScheme,
              onTap: () async {
                await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (_) => AddBoardScreen(
                      isEdit: true,
                      initialBoard: board,
                      onBoardAdded: (editedBoard) async {
                        await _updateBoard(index, editedBoard);
                      },
                    ),
                  ),
                );
                await _loadBoards();
              },
            ),
          ),

          const SizedBox(width: 8),

          // زر الحذف
          Expanded(
            child: _buildModernActionButton(
              label: 'حذف',
              icon: Icons.delete,
              color: Colors.red.shade700,
              colorScheme: colorScheme,
              onTap: () async {
                showDialog(
                  context: context,
                  builder: (ctx) => AlertDialog(
                    title: const Text('تأكيد الحذف'),
                    content: const Text('هل أنت متأكد من حذف هذه اللوحة؟'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(ctx).pop(),
                        child: const Text('إلغاء'),
                      ),
                      TextButton(
                        onPressed: () async {
                          await _removeBoard(index);
                          if (ctx.mounted) {
                            Navigator.of(ctx).pop();
                          }
                        },
                        child: const Text(
                          'حذف',
                          style: TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // بناء زر عمل حديث
  Widget _buildModernActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required ColorScheme colorScheme,
    required VoidCallback? onTap,
    bool isEnabled = true,
  }) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: isEnabled
            ? color.withValues(alpha: 0.1)
            : colorScheme.outline.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isEnabled
              ? color.withValues(alpha: 0.3)
              : colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: isEnabled ? onTap : null,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color: isEnabled ? color : colorScheme.outline,
              ),
              const SizedBox(height: 2),
              Text(
                label,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                  color: isEnabled ? color : colorScheme.outline,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
