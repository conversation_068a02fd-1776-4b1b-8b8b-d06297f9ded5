import 'subscriber_model.dart';
import 'subscribers_storage.dart';
import '../../../db_helper.dart';
import 'transaction_model.dart';

class SubscribersStorageImpl implements SubscribersStorage {
  @override
  Future<List<Subscriber>> getAll({int? boardId}) async {
    final data = await DBHelper.instance.getAllSubscribers(boardId: boardId);
    return data;
  }

  @override
  Future<void> add(Subscriber subscriber) async {
    await DBHelper.instance.insertSubscriber(subscriber);
  }

  @override
  Future<void> update(Subscriber subscriber) async {
    await DBHelper.instance.updateSubscriber(subscriber);
  }

  @override
  Future<void> delete(int id) async {
    await DBHelper.instance.deleteSubscriber(id);
  }

  @override
  Future<void> payDebt(int subscriberId, double amount, {String? note}) async {
    // إذا لم تكن دالة payDebt موجودة في DBHelper، يجب إزالتها أو تعديلها
    // await DBHelper.instance.payDebt(subscriberId, amount, note: note);
    throw UnimplementedError('payDebt غير مدعومة حالياً');
  }

  @override
  Future<void> insertTransaction(Transaction transaction) async {
    await DBHelper.instance.insertTransaction(transaction);
  }

  @override
  Future<Subscriber?> getById(int id) async {
    return await DBHelper.instance.getSubscriberById(id);
  }
}
