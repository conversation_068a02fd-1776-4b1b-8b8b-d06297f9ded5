import 'package:flutter/foundation.dart';

/// نموذج البيانات الموحد لحالة الحساب
/// هذا هو المصدر الوحيد للحقيقة في النظام
class UnifiedAccountModel {
  // البيانات الأساسية
  final String userId;
  final String email;
  final String displayName;

  // حالة الحساب
  final AccountStatus status;
  final AccountType type;

  // بيانات الفترة التجريبية
  final bool isTrial;
  final int trialDaysRemaining;
  final DateTime? trialStartDate;
  final DateTime? trialEndDate;

  // بيانات الاشتراك
  final String? subscriptionType;
  final DateTime? subscriptionStart;
  final DateTime? subscriptionEnd;
  final bool isSubscriptionActive;

  // بيانات القيود
  final bool isLocked;
  final String? lockReason;
  final DateTime? lockDate;

  // بيانات التتبع
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime lastActivity;
  final String dataSource; // 'supabase', 'local', 'default'

  const UnifiedAccountModel({
    required this.userId,
    required this.email,
    required this.displayName,
    required this.status,
    required this.type,
    required this.isTrial,
    required this.trialDaysRemaining,
    this.trialStartDate,
    this.trialEndDate,
    this.subscriptionType,
    this.subscriptionStart,
    this.subscriptionEnd,
    required this.isSubscriptionActive,
    required this.isLocked,
    this.lockReason,
    this.lockDate,
    required this.createdAt,
    required this.updatedAt,
    required this.lastActivity,
    required this.dataSource,
  });

  /// إنشاء من بيانات Supabase مع معالجة شاملة للأخطاء
  factory UnifiedAccountModel.fromSupabase({
    required Map<String, dynamic> data,
    required String userId,
    required String email,
  }) {
    try {
      // معالجة آمنة لجميع الحقول
      final displayName =
          _safeString(data['display_name']) ?? email.split('@').first;

      // تحديد حالة الحساب بمنطق موحد
      final status = _determineAccountStatus(data);
      final type = _determineAccountType(data);

      // معالجة بيانات الفترة التجريبية
      final isTrial =
          _safeBool(data['is_trial']) ||
          _safeString(data['account_status']) == 'trial';

      final trialDaysRemaining =
          _safeInt(data['trial_days_remaining']) ??
          _safeInt(data['trial_days']) ??
          _calculateTrialDaysFromExpiry(data);

      // معالجة بيانات الاشتراك
      final subscriptionType = _safeString(data['subscription_type']);
      final subscriptionStart = _safeDateTime(data['subscription_start']);
      final subscriptionEnd = _safeDateTime(data['subscription_end']);
      final isSubscriptionActive = _isSubscriptionCurrentlyActive(
        subscriptionEnd,
        status,
      );

      // معالجة بيانات القيود
      final isLocked =
          _safeBool(data['is_locked']) || status == AccountStatus.locked;
      final lockReason = _safeString(data['lock_reason']);

      // معالجة التواريخ
      final createdAt = _safeDateTime(data['created_at']) ?? DateTime.now();
      final updatedAt = _safeDateTime(data['updated_at']) ?? DateTime.now();
      final lastActivity =
          _safeDateTime(data['last_activity']) ?? DateTime.now();

      return UnifiedAccountModel(
        userId: userId,
        email: email,
        displayName: displayName,
        status: status,
        type: type,
        isTrial: isTrial,
        trialDaysRemaining: trialDaysRemaining,
        trialStartDate: _safeDateTime(data['trial_start_date']),
        trialEndDate: _calculateTrialEndDate(data),
        subscriptionType: subscriptionType,
        subscriptionStart: subscriptionStart,
        subscriptionEnd: subscriptionEnd,
        isSubscriptionActive: isSubscriptionActive,
        isLocked: isLocked,
        lockReason: lockReason,
        lockDate: _safeDateTime(data['lock_date']),
        createdAt: createdAt,
        updatedAt: updatedAt,
        lastActivity: lastActivity,
        dataSource: 'supabase',
      );
    } catch (e) {
      debugPrint('❌ [UNIFIED_MODEL] خطأ في تحليل بيانات Supabase: $e');
      // ✅ أمان: لا ننشئ نموذج افتراضي
      throw Exception('FORCE_EXIT: خطأ في تحليل بيانات Supabase');
    }
  }

  /// إنشاء من البيانات المحلية
  factory UnifiedAccountModel.fromLocal({
    required Map<String, dynamic> data,
    required String userId,
    required String email,
  }) {
    try {
      final displayName =
          _safeString(data['display_name']) ?? email.split('@').first;

      final accountStatus = _safeString(data['account_status']) ?? 'unknown';
      final status = _parseAccountStatus(accountStatus);

      final isTrial = _safeBool(data['is_trial']) || accountStatus == 'trial';
      final trialDaysRemaining =
          _safeInt(data['trial_days_remaining']) ??
          _safeInt(data['trial_days']) ??
          0;

      return UnifiedAccountModel(
        userId: userId,
        email: email,
        displayName: displayName,
        status: status,
        type: isTrial ? AccountType.trial : AccountType.premium,
        isTrial: isTrial,
        trialDaysRemaining: trialDaysRemaining,
        subscriptionEnd: _safeDateTime(data['subscription_end']),
        isSubscriptionActive: status == AccountStatus.active,
        isLocked: _safeBool(data['is_locked']),
        lockReason: _safeString(data['lock_reason']),
        createdAt: _safeDateTime(data['created_at']) ?? DateTime.now(),
        updatedAt: _safeDateTime(data['updated_at']) ?? DateTime.now(),
        lastActivity: DateTime.now(),
        dataSource: 'local',
      );
    } catch (e) {
      debugPrint('❌ [UNIFIED_MODEL] خطأ في تحليل البيانات المحلية: $e');
      // ✅ أمان: لا ننشئ نموذج افتراضي
      throw Exception('FORCE_EXIT: خطأ في تحليل البيانات المحلية');
    }
  }

  // ✅ تم حذف دالة createDefault لأسباب أمنية

  // الخصائص المحسوبة
  bool get isActive =>
      status == AccountStatus.active || status == AccountStatus.trial;
  bool get isRestricted =>
      status == AccountStatus.expired ||
      status == AccountStatus.banned ||
      status == AccountStatus.suspended ||
      status == AccountStatus.locked;
  bool get isExpired => status == AccountStatus.expired;
  bool get isBanned => status == AccountStatus.banned;
  bool get isTrialExpired => isTrial && trialDaysRemaining <= 0;
  bool get hasValidSubscription =>
      isSubscriptionActive &&
      subscriptionEnd != null &&
      subscriptionEnd!.isAfter(DateTime.now());

  /// تحويل إلى Map للحفظ
  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'email': email,
      'display_name': displayName,
      'account_status': status.name,
      'account_type': type.name,
      'is_trial': isTrial,
      'trial_days_remaining': trialDaysRemaining,
      'trial_start_date': trialStartDate?.toIso8601String(),
      'trial_end_date': trialEndDate?.toIso8601String(),
      'subscription_type': subscriptionType,
      'subscription_start': subscriptionStart?.toIso8601String(),
      'subscription_end': subscriptionEnd?.toIso8601String(),
      'is_subscription_active': isSubscriptionActive,
      'is_locked': isLocked,
      'lock_reason': lockReason,
      'lock_date': lockDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_activity': lastActivity.toIso8601String(),
      'data_source': dataSource,
    };
  }

  /// إنشاء نسخة محدثة
  UnifiedAccountModel copyWith({
    AccountStatus? status,
    AccountType? type,
    bool? isTrial,
    int? trialDaysRemaining,
    DateTime? trialEndDate,
    String? subscriptionType,
    DateTime? subscriptionStart,
    DateTime? subscriptionEnd,
    bool? isSubscriptionActive,
    bool? isLocked,
    String? lockReason,
    DateTime? lockDate,
    DateTime? lastActivity,
    String? dataSource,
  }) {
    return UnifiedAccountModel(
      userId: userId,
      email: email,
      displayName: displayName,
      status: status ?? this.status,
      type: type ?? this.type,
      isTrial: isTrial ?? this.isTrial,
      trialDaysRemaining: trialDaysRemaining ?? this.trialDaysRemaining,
      trialStartDate: trialStartDate,
      trialEndDate: trialEndDate ?? this.trialEndDate,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionStart: subscriptionStart ?? this.subscriptionStart,
      subscriptionEnd: subscriptionEnd ?? this.subscriptionEnd,
      isSubscriptionActive: isSubscriptionActive ?? this.isSubscriptionActive,
      isLocked: isLocked ?? this.isLocked,
      lockReason: lockReason ?? this.lockReason,
      lockDate: lockDate ?? this.lockDate,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      lastActivity: lastActivity ?? DateTime.now(),
      dataSource: dataSource ?? this.dataSource,
    );
  }

  @override
  String toString() {
    return 'UnifiedAccountModel(userId: $userId, status: ${status.name}, '
        'isTrial: $isTrial, trialDays: $trialDaysRemaining, '
        'source: $dataSource)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UnifiedAccountModel &&
        other.userId == userId &&
        other.status == status &&
        other.isTrial == isTrial &&
        other.trialDaysRemaining == trialDaysRemaining;
  }

  @override
  int get hashCode {
    return Object.hash(userId, status, isTrial, trialDaysRemaining);
  }

  // الدوال المساعدة الآمنة
  static String? _safeString(dynamic value) {
    if (value == null) return null;
    return value.toString();
  }

  static bool _safeBool(dynamic value) {
    if (value == null) return false;
    if (value is bool) return value;
    if (value is int) return value == 1;
    if (value is String) return value.toLowerCase() == 'true' || value == '1';
    return false;
  }

  static int? _safeInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) return int.tryParse(value);
    if (value is bool) return value ? 1 : 0;
    return null;
  }

  static DateTime? _safeDateTime(dynamic value) {
    if (value == null) return null;
    if (value is DateTime) return value;
    if (value is String) return DateTime.tryParse(value);
    if (value is int) return DateTime.fromMillisecondsSinceEpoch(value);
    return null;
  }

  // دوال تحديد الحالة والنوع
  static AccountStatus _determineAccountStatus(Map<String, dynamic> data) {
    // فحص الحالة المباشرة
    final directStatus = _safeString(data['account_status']);
    if (directStatus != null) {
      final parsed = _parseAccountStatus(directStatus);
      if (parsed != AccountStatus.unknown) return parsed;
    }

    // فحص القيود
    if (_safeBool(data['is_banned'])) return AccountStatus.banned;
    if (_safeBool(data['is_suspended'])) return AccountStatus.suspended;
    if (_safeBool(data['is_locked'])) return AccountStatus.locked;

    // فحص انتهاء الصلاحية
    final expiryMillis = _safeInt(data['expiry_millis']);
    if (expiryMillis != null && expiryMillis > 0) {
      if (DateTime.now().millisecondsSinceEpoch > expiryMillis) {
        return AccountStatus.expired;
      }
    }

    // فحص الفترة التجريبية
    final isTrial = _safeBool(data['is_trial']);
    final trialDays =
        _safeInt(data['trial_days_remaining']) ??
        _safeInt(data['trial_days']) ??
        0;

    if (isTrial) {
      if (trialDays <= 0) {
        return AccountStatus.expired;
      }
      return AccountStatus.trial;
    }

    // الحالة الافتراضية
    return AccountStatus.active;
  }

  static AccountType _determineAccountType(Map<String, dynamic> data) {
    final subscriptionType = _safeString(data['subscription_type']);
    if (subscriptionType != null && subscriptionType != 'trial') {
      return AccountType.premium;
    }

    final isTrial = _safeBool(data['is_trial']);
    return isTrial ? AccountType.trial : AccountType.premium;
  }

  static AccountStatus _parseAccountStatus(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return AccountStatus.active;
      case 'trial':
        return AccountStatus.trial;
      case 'expired':
        return AccountStatus.expired;
      case 'banned':
        return AccountStatus.banned;
      case 'suspended':
        return AccountStatus.suspended;
      case 'locked':
        return AccountStatus.locked;
      default:
        return AccountStatus.unknown;
    }
  }

  static int _calculateTrialDaysFromExpiry(Map<String, dynamic> data) {
    final expiryMillis = _safeInt(data['expiry_millis']);
    if (expiryMillis == null || expiryMillis <= 0) return 0;

    final expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryMillis);
    final now = DateTime.now();

    if (expiryDate.isBefore(now)) return 0;

    return expiryDate.difference(now).inDays;
  }

  static DateTime? _calculateTrialEndDate(Map<String, dynamic> data) {
    final expiryMillis = _safeInt(data['expiry_millis']);
    if (expiryMillis != null && expiryMillis > 0) {
      return DateTime.fromMillisecondsSinceEpoch(expiryMillis);
    }

    final trialDays =
        _safeInt(data['trial_days_remaining']) ??
        _safeInt(data['trial_days']) ??
        0;
    if (trialDays > 0) {
      return DateTime.now().add(Duration(days: trialDays));
    }

    return null;
  }

  static bool _isSubscriptionCurrentlyActive(
    DateTime? subscriptionEnd,
    AccountStatus status,
  ) {
    if (subscriptionEnd == null) return false;
    if (status == AccountStatus.banned ||
        status == AccountStatus.suspended ||
        status == AccountStatus.locked) {
      return false;
    }

    return subscriptionEnd.isAfter(DateTime.now());
  }

  // ✅ تم حذف دالة _createDefaultModel لأسباب أمنية
}

/// حالات الحساب
enum AccountStatus {
  active, // حساب نشط
  trial, // حساب تجريبي
  expired, // حساب منتهي
  banned, // حساب محظور
  suspended, // حساب موقوف
  locked, // حساب مقفل
  unknown, // حالة غير معروفة
}

/// أنواع الحساب
enum AccountType {
  trial, // تجريبي
  premium, // مدفوع
  free, // مجاني
}
