import 'package:supabase_flutter/supabase_flutter.dart';

/// خدمة المصادقة باستخدام Supabase - مبسطة
class SupabaseAuthService {
  static final SupabaseAuthService _instance = SupabaseAuthService._internal();
  factory SupabaseAuthService() => _instance;
  SupabaseAuthService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn {
    final user = _supabase.auth.currentUser;
    return user != null;
  }

  /// الحصول على المستخدم الحالي
  User? get currentUser => _supabase.auth.currentUser;

  /// الحصول على معرف المستخدم
  String? get currentUserId => _supabase.auth.currentUser?.id;

  /// الحصول على بريد المستخدم
  String? get currentUserEmail => _supabase.auth.currentUser?.email;

  /// تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// إنشاء حساب جديد
  Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: metadata,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// تسجيل الدخول كضيف (Anonymous)
  Future<AuthResponse> signInAnonymously() async {
    try {
      final response = await _supabase.auth.signInAnonymously();
      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: 'itower://reset-password', // Custom scheme للتطبيق
      );
    } catch (e) {
      rethrow;
    }
  }

  /// تحديث كلمة المرور (للمستخدم المسجل دخول)
  Future<UserResponse> updatePassword(String newPassword) async {
    try {
      final response = await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  /// التحقق من صحة الجلسة
  Future<bool> isSessionValid() async {
    try {
      final session = _supabase.auth.currentSession;
      if (session == null) return false;

      // التحقق من انتهاء صلاحية الجلسة
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        session.expiresAt! * 1000,
      );
      return DateTime.now().isBefore(expiresAt);
    } catch (e) {
      return false;
    }
  }
}
