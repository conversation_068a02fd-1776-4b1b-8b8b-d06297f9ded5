import 'dart:convert';
import 'dart:io';
import 'package:http/io_client.dart';
import 'dart:math';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:crypto/crypto.dart';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../db_helper.dart';
import '../features/subscribers/data/subscriber_model.dart';

// دالة تشفير AES CBC مع salt متوافقة مع CryptoJS/OpenSSL
String encryptWithOpenSSL(String plaintext, String passphrase) {
  final rnd = Random.secure();
  final salt = List<int>.generate(8, (_) => rnd.nextInt(256));
  final keyAndIV = _evpBytesToKey(passphrase.codeUnits, salt, 32, 16);
  final key = keyAndIV.sublist(0, 32);
  final iv = Uint8List.fromList(keyAndIV.sublist(32, 48));
  final cipher = encrypt.Encrypter(
    encrypt.AES(
      encrypt.Key(Uint8List.fromList(key)),
      mode: encrypt.AESMode.cbc,
      padding: null,
    ),
  );
  final padded = _pkcs7Pad(Uint8List.fromList(utf8.encode(plaintext)), 16);
  final encrypted = cipher.encryptBytes(padded, iv: encrypt.IV(iv));
  final prefix = utf8.encode('Salted__');
  final out = <int>[]
    ..addAll(prefix)
    ..addAll(salt)
    ..addAll(encrypted.bytes);
  return base64.encode(out);
}

List<int> _evpBytesToKey(
  List<int> password,
  List<int> salt,
  int keyLen,
  int ivLen,
) {
  final totalLen = keyLen + ivLen;
  var derived = <int>[];
  var block = <int>[];
  while (derived.length < totalLen) {
    final hasher = md5.convert([...block, ...password, ...salt]);
    block = hasher.bytes;
    derived.addAll(block);
  }
  return derived.sublist(0, totalLen);
}

Uint8List _pkcs7Pad(Uint8List data, int blockSize) {
  final pad = blockSize - (data.length % blockSize);
  final padded = Uint8List(data.length + pad)
    ..setAll(0, data)
    ..setAll(data.length, List.filled(pad, pad));
  return padded;
}

/// دالة تسجيل دخول احترافية مع إرجاع تفاصيل النتيجة
Future<Map<String, dynamic>> tryConnect(Map<String, dynamic> board) async {
  String url = board['url']?.toString().trim() ?? '';
  url = url.replaceAll(RegExp(r'[:/]+ $'), '');
  final user = board['user'] ?? board['name'] ?? '';
  final pass = board['pass'] ?? '';
  final language = 'en';
  print('[CONNECT TRACE] --- بدء محاولة الاتصال بالموقع ---');
  print('[CONNECT TRACE] الرابط: $url');
  print('[CONNECT TRACE] اسم المستخدم: $user');
  print('[CONNECT TRACE] كلمة المرور: $pass');
  if (url.isEmpty) {
    print('[CONNECT TRACE] فشل: رابط الموقع فارغ');
    return {'success': false, 'message': 'رابط الموقع فارغ'};
  }
  try {
    final loginUrl = 'https://$url/admin/api/index.php/api/login';
    print('[CONNECT TRACE] إرسال طلب POST إلى: $loginUrl');
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) {
        print('[CONNECT TRACE] تجاهل خطأ شهادة SSL للخادم: $host:$port');
        return true;
      };
    final client = IOClient(ioClient);
    final uri = Uri.parse(loginUrl);
    final loginData = {
      'username': user,
      'password': pass,
      'language': language,
    };
    final jsonString = jsonEncode(loginData);
    final payload = encryptWithOpenSSL(
      jsonString,
      'abcdefghijuklmno0123456789012345',
    );
    print('[CONNECT TRACE] البيانات المرسلة (payload): $payload');
    final response = await client.post(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Origin': 'https://${board['url']}',
        'Referer': 'https://${board['url']}/',
        'Accept-Language': 'en-US,en;q=0.9,ar-EG;q=0.8,ar;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'host': board['url'],
      },
      body: jsonEncode({'payload': payload}),
    );
    print('[CONNECT TRACE] كود الاستجابة: ${response.statusCode}');
    print('[CONNECT TRACE] رؤوس الاستجابة: ${response.headers}');
    print('[CONNECT TRACE] محتوى الرد: ${response.body}');
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['token'] != null) {
        print(
          '[CONNECT TRACE] تم تسجيل الدخول بنجاح. التوكن: ${decoded['token']}',
        );
        return {
          'success': true,
          'token': decoded['token'],
          'message': 'تم تسجيل الدخول بنجاح',
        };
      } else if (decoded['message'] != null) {
        print('[CONNECT TRACE] فشل: ${decoded['message']}');
        return {'success': false, 'message': decoded['message']};
      }
      print('[CONNECT TRACE] فشل: لم يتم العثور على توكن في الرد');
      return {'success': false, 'message': 'لم يتم العثور على توكن في الرد'};
    }
    print('[CONNECT TRACE] فشل: كود الاستجابة ${response.statusCode}');
    return {
      'success': false,
      'message': 'فشل تسجيل الدخول: كود الاستجابة ${response.statusCode}',
    };
  } catch (e, st) {
    print('[CONNECT TRACE] استثناء أثناء الاتصال: $e');
    print('[CONNECT TRACE] Stacktrace: $st');
    return {'success': false, 'message': 'خطأ أثناء الاتصال: $e'};
  } finally {
    print('[CONNECT TRACE] --- نهاية محاولة الاتصال ---');
  }
}

// جلب بيانات مشترك واحد من السيرفر
Future<Map<String, dynamic>?> fetchSubscriber(String username) async {
  try {
    // يجب تعديل الرابط ليأخذ الدومين من المستخدم
    final board = await DBHelper.instance.getActiveBoard();
    final domain = board != null ? board['url']?.toString().trim() ?? '' : '';
    final url = 'https://$domain/admin/api/index.php/api/user/get';
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    final client = IOClient(ioClient);
    final response = await client.post(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: jsonEncode({'username': username}),
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['status'] == 200 && decoded['data'] != null) {
        return decoded['data'];
      }
    }
    return null;
  } catch (e) {
    print('[fetchSubscriber][ERROR] $e');
    return null;
  }
}

// دالة تغيير الباقة على السيرفر مع تسجيل الدخول أولاً
Future<Map<String, dynamic>> changeProfileOnServer({
  required Map<String, dynamic> board, // بيانات اللوحة/السيرفر
  required String username,
  required int profileId,
  required int userId, // إضافة userId كـ parameter إجباري
}) async {
  try {
    // تسجيل الدخول أولاً للحصول على توكن جديد
    final loginResult = await tryConnect(board);
    if (!(loginResult['success'] ?? false)) {
      return {
        'success': false,
        'message': 'فشل تسجيل الدخول: ${loginResult['message'] ?? ''}',
      };
    }
    final token = loginResult['token'];
    final url =
        'https://${board['url']}/admin/api/index.php/api/user/changeProfile';
    final requestJson = jsonEncode({
      'user_id': userId,
      'username': username,
      'profile_id': profileId,
      'when': 'immediate',
    });
    final payload = encryptWithOpenSSL(
      requestJson,
      'abcdefghijuklmno0123456789012345',
    );
    final requestBody = {'payload': payload};
    final headers = {
      'authorization': 'Bearer $token',
      'content-type': 'application/json',
      'accept': 'application/json, text/plain, */*',
      'origin': 'https://${board['url']}',
      'referer': 'https://${board['url']}/',
      'user-agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'accept-language': 'en,en-US;q=0.9,ar;q=0.8',
      'host': board['url'],
      'sec-ch-ua':
          '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
    };
    print('[changeProfileOnServer][REQUEST] headers: ' + jsonEncode(headers));
    print('[changeProfileOnServer][REQUEST] body: ' + jsonEncode(requestBody));
    final dio = Dio();
    // تجاوز التحقق من الشهادة SSL (للاستخدام الداخلي فقط)
    (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
        (client) {
          client.badCertificateCallback =
              (X509Certificate cert, String host, int port) => true;
          return client;
        };
    final response = await dio.post(
      url,
      data: requestBody,
      options: Options(
        headers: headers,
        validateStatus: (status) => true, // لا ترمي استثناء عند أي كود
      ),
    );
    print('[changeProfileOnServer][RESPONSE][status] ${response.statusCode}');
    print('[changeProfileOnServer][RESPONSE][headers] ${response.headers}');
    print('[changeProfileOnServer][RESPONSE][data] ${response.data}');
    if (response.statusCode == 200) {
      final decoded = response.data;
      if (decoded['status'] == 200 &&
          decoded['message'] == 'rsp_profile_changed') {
        return {'success': true, 'message': 'تم تغيير الباقة بنجاح'};
      } else {
        return {
          'success': false,
          'message': decoded['message'] ?? 'فشل تغيير الباقة',
          'response': decoded,
        };
      }
    }
    return {
      'success': false,
      'message': 'فشل تغيير الباقة: كود الاستجابة ${response.statusCode}',
      'response': response.data,
    };
  } catch (e) {
    print('[changeProfileOnServer][ERROR] $e');
    return {'success': false, 'message': 'خطأ أثناء تغيير الباقة: $e'};
  }
}

/// مزامنة بيانات مشترك مع السيرفر وتحديث قاعدة البيانات المحلية
Future<bool> syncSubscriberData(Subscriber subscriber) async {
  try {
    // جلب بيانات اللوحة النشطة
    final board = await DBHelper.instance.getActiveBoard();
    if (board == null) {
      print('[SYNC][API_HELPER][ERROR] لم يتم العثور على لوحة نشطة');
      return false;
    }
    // جلب التوكن
    String? token = board['token'];
    if (token == null || token.isEmpty) {
      final loginResult = await tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        token = loginResult['token'];
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': token,
        });
      } else {
        print('[SYNC][API_HELPER][ERROR] فشل تسجيل الدخول وجلب التوكن');
        return false;
      }
    }
    // بناء البايلود
    final Map<String, dynamic> data = {
      'user_id': subscriber.remoteId ?? 0,
      'username': subscriber.user,
      'profile_id': subscriber.subscriptionId ?? 0,
      'start_date': subscriber.startDate
          .toIso8601String()
          .replaceFirst('T', ' ')
          .split('.')
          .first,
      'activation_method': 'Manager Balance',
    };
    print('[SYNC][API_HELPER][DEBUG] JSON قبل التشفير: ${jsonEncode(data)}');
    final payload = encryptWithOpenSSL(
      jsonEncode(data),
      'abcdefghijuklmno0123456789012345',
    );
    print('[SYNC][API_HELPER][DEBUG] payload المشفر: $payload');
    final dio = Dio();
    (dio.httpClientAdapter as dynamic).onHttpClientCreate = (client) {
      client.badCertificateCallback = (cert, host, port) {
        print('[SYNC][API_HELPER][SSL] تجاوز تحقق الشهادة لـ $host:$port');
        return true;
      };
      return client;
    };
    final headers = {
      'authorization': 'Bearer $token',
      'content-type': 'application/json',
      'accept': 'application/json, text/plain, */*',
      'origin': 'https://${board['url']}',
      'referer': 'https://${board['url']}/',
      'user-agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'accept-language': 'en,en-US;q=0.9,ar;q=0.8',
      'host': board['url'],
      'sec-ch-ua':
          '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
    };
    print('[SYNC][API_HELPER][DEBUG] headers المرسلة: ${headers.toString()}');
    final url = 'https://${board['url']}/admin/api/index.php/api/user/activate';
    final response = await dio.post(
      url,
      data: jsonEncode({'payload': payload}),
      options: Options(headers: headers, validateStatus: (status) => true),
    );
    print('[SYNC][API_HELPER][DEBUG] كود الاستجابة: ${response.statusCode}');
    print('[SYNC][API_HELPER][DEBUG] محتوى الرد: ${response.data}');
    if (response.statusCode == 200) {
      if (response.data == null ||
          (response.data is String && response.data.trim().isEmpty)) {
        print('[SYNC][API_HELPER][ERROR] الاستجابة من السيرفر فارغة!');
        return false;
      }
      final resp = response.data is String
          ? jsonDecode(response.data)
          : response.data;
      print('[SYNC][API_HELPER] الاستجابة المفككة: $resp');
      // تحديث قاعدة البيانات المحلية إذا لزم الأمر
      // يمكن هنا تحديث بيانات المشترك إذا كان resp يحتوي على بيانات جديدة
      // أو جلب بيانات المشترك من السيرفر وتحديثها
      final remoteData = await DBHelper.instance.getSubscriberById(
        subscriber.id!,
      );
      if (remoteData != null) {
        await DBHelper.instance.insertOrUpdateSubscriber(remoteData.toMap());
        print('[SYNC][API_HELPER] تم تحديث قاعدة البيانات المحلية.');
      }
      return true;
    } else {
      print(
        '[SYNC][API_HELPER][ERROR] كود استجابة غير متوقع: ${response.statusCode}',
      );
      return false;
    }
  } catch (e, st) {
    print('[SYNC][API_HELPER][ERROR] $e');
    print(st);
    return false;
  }
}

/// دالة مركزية لمزامنة كل البيانات من السيرفر (بروفايلات + مشتركين)
/// يمكن تمرير progressCallback لتتبع الخطوات أو silent=true لمزامنة صامتة
///
/// ⚠️ مهم: لا يتم جلب حقل 'balance' من السيرفر للحفاظ على قيم 'totalDebt' المحلية
/// هذا يمنع فقدان البيانات المالية المدارة محلياً في التطبيق
Future<List<String>> syncAllFromServer({
  required Map<String, dynamic> board,
  void Function(String step, {String? status, int? count, int? total})?
  progressCallback,
  bool silent = false,
}) async {
  try {
    List<String> syncNotifications = [];
    progressCallback?.call('login', status: 'in_progress');
    String? token = board['token'];
    if (token == null || token.isEmpty) {
      final loginResult = await tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        token = loginResult['token'];
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': token,
        });
      } else {
        throw Exception('فشل تسجيل الدخول: ${loginResult['message']}');
      }
    }
    progressCallback?.call('login', status: 'done');
    // إعداد IOClient مع تجاوز شهادة SSL
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    final client = IOClient(ioClient);
    // مزامنة المشتركين: ربط المشترك بالباقة عبر subscriptionId فقط، لا تحفظ أي سعر
    progressCallback?.call('users', status: 'in_progress');
    int page = 1;
    int perPage = 10;
    bool hasMore = true;
    List<Map<String, dynamic>> allUsers = [];
    int totalUsers = 0;
    while (hasMore) {
      http.Response usersResp;
      final userBody = {'page': page, 'per_page': perPage};
      final userPayload = encryptWithOpenSSL(
        jsonEncode(userBody),
        'abcdefghijuklmno0123456789012345',
      );
      usersResp = await client.post(
        Uri.parse('https://${board['url']}/admin/api/index.php/api/index/user'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
          'Authorization': 'Bearer $token',
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Origin': 'https://${board['url']}',
          'Referer': 'https://${board['url']}/',
          'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
        },
        body: jsonEncode({'payload': userPayload}),
      );
      if (usersResp.statusCode != 200) {
        throw Exception('فشل جلب المشتركين: ${usersResp.body}');
      }
      final usersJson = jsonDecode(usersResp.body);
      final users = List<Map<String, dynamic>>.from(usersJson['data'] ?? []);
      perPage = int.tryParse(usersJson['per_page']?.toString() ?? '10') ?? 10;
      totalUsers = int.tryParse(usersJson['total']?.toString() ?? '0') ?? 0;
      allUsers.addAll(users);
      int currentPage = usersJson['current_page'] ?? page;
      int lastPage = usersJson['last_page'] ?? page;
      hasMore = currentPage < lastPage;
      page++;
      progressCallback?.call(
        'users',
        status: 'in_progress',
        count: allUsers.length,
        total: totalUsers,
      );
    }
    progressCallback?.call(
      'users',
      status: 'done',
      count: allUsers.length,
      total: totalUsers,
    );

    // استنتاج وإنشاء الباقات من بيانات المشتركين
    try {
      final packageSyncResults = await syncPackagesFromUsers(
        users: allUsers,
        token: token!,
        baseUrl: board['url'],
        progressCallback: progressCallback,
      );
      syncNotifications.addAll(packageSyncResults);
    } catch (e) {
      print('[SYNC][API_HELPER][PACKAGES_ERROR] خطأ في استنتاج الباقات: $e');
      syncNotifications.add('خطأ في استنتاج الباقات: $e');
    }

    // مقارنة المشتركين المحليين مع السيرفر
    progressCallback?.call('db', status: 'in_progress');
    final localSubscribers = await DBHelper.instance.getAllSubscribers(
      boardId: board['id'],
    );
    final Map<int, Map<String, dynamic>> localSubsMap = {
      for (final sub in localSubscribers) (sub.remoteId ?? -1): sub.toMap(),
    };
    final Set<int> remoteIds = allUsers.map((u) => u['id'] as int).toSet();
    int userIndex = 0;
    for (final user in allUsers) {
      userIndex++;
      final profile = user['profile_details'] ?? {};
      final subscriptionId = user['profile_id']?.toString();
      double subscriptionPrice = 0.0;
      final remoteId = user['id'] as int;
      final local = localSubsMap[remoteId];
      int onlineStatus = (user['online_status'] == 1) ? 1 : 0;
      // الحفاظ على totalDebt المحلي وعدم استبداله بـ balance من السيرفر
      final localTotalDebt = local?['totalDebt']?.toDouble() ?? 0.0;

      // الحفاظ على رصيد المحفظة المحلي
      final localNotes = local?['notes']?.toString() ?? '';
      final walletMatch = RegExp(
        r'wallet:(\d+(?:\.\d+)?)',
      ).firstMatch(localNotes);
      final walletBalance = walletMatch?.group(0);

      // دمج notes السيرفر مع رصيد المحفظة المحلي
      final serverNotes = user['notes']?.toString() ?? '';
      final mergedNotes = _mergeNotesWithWallet(serverNotes, walletBalance);

      final newMap = {
        'user': user['username'],
        'name': user['firstname'] ?? '',
        'totalDebt':
            localTotalDebt, // استخدام القيمة المحلية بدلاً من balance السيرفر
        'subscriptionPrice': subscriptionPrice,
        'subscriptionType': profile['name'] ?? '',
        'startDate': user['created_at'] ?? '',
        'endDate': user['expiration'] ?? '',
        'phone': user['phone'] ?? '',
        'notes': mergedNotes.isEmpty ? null : mergedNotes,
        'ip': user['static_ip'],
        'subscriptionId': subscriptionId,
        'buyPrice': null,
        'remoteId': remoteId,
        'contract': user['contract_id'] == 1 ? 1 : 0,
        'online_status': onlineStatus,
        'isDeleted': 0,
        'source_type': 'sas',
      };
      if (local == null) {
        syncNotifications.add(
          'تمت إضافة مشترك جديد: ${user['firstname'] ?? user['username']}',
        );
      } else {
        // لا تضف إشعار التعديل إطلاقًا
      }
      await DBHelper.instance.insertOrUpdateSubscriber(
        newMap,
        boardId: board['id'],
      );
      progressCallback?.call(
        'db',
        status: 'in_progress',
        count: userIndex,
        total: allUsers.length,
      );
    }
    // منطق تمييز المحذوفين: أي مشترك محلي remoteId غير موجود في السيرفر يتم تعيين isDeleted=1 له
    List<String> deletedSubscribersNames = [];
    for (final sub in localSubscribers) {
      final remoteId = sub.remoteId;
      if (remoteId != null && !remoteIds.contains(remoteId)) {
        // تحقق إذا كان الحذف جديدًا فقط (كان isDeleted=0 أو false)
        if (sub.isDeleted == 0) {
          await DBHelper.instance.insertOrUpdateSubscriber({
            ...sub.toMap(),
            'isDeleted': 1,
          }, boardId: board['id']);
          deletedSubscribersNames.add(sub.name);
        } else {
          // إذا كان محذوف مسبقًا، لا تضف إشعار
          await DBHelper.instance.insertOrUpdateSubscriber({
            ...sub.toMap(),
            'isDeleted': 1,
          }, boardId: board['id']);
        }
      }
    }
    // إشعار حذف مشتركين دفعة واحدة فقط إذا كان هناك حذف جديد
    if (deletedSubscribersNames.isNotEmpty) {
      syncNotifications.add(
        'تم حذف ${deletedSubscribersNames.length} مشترك من السيرفر: ${deletedSubscribersNames.join('، ')}',
      );
    }
    progressCallback?.call(
      'db',
      status: 'done',
      count: allUsers.length,
      total: allUsers.length,
    );
    if (!silent && syncNotifications.isNotEmpty) {
      progressCallback?.call(
        'notifications',
        status: 'done',
        count: syncNotifications.length,
        total: syncNotifications.length,
      );
      for (final msg in syncNotifications) {
        progressCallback?.call('notify', status: msg);
      }
    }
    if (!silent) print('[SYNC][API_HELPER] تمت مزامنة جميع البيانات بنجاح');

    // مزامنة رصيد اللوحة
    try {
      if (!silent) {
        progressCallback?.call('balance_sync', status: 'start');
      }

      final balanceResult = await fetchBoardBalance(board: board, token: token);

      if (balanceResult['success'] == true) {
        // تحديث رصيد اللوحة في قاعدة البيانات
        final updatedBoard = Map<String, dynamic>.from(board);
        updatedBoard['balance'] = balanceResult['balance'];
        updatedBoard['balance_text'] = balanceResult['balanceText'];
        updatedBoard['currency'] = balanceResult['currency'];

        await DBHelper.instance.updateBoard(board['id'], updatedBoard);

        final balanceMsg =
            'تم تحديث رصيد اللوحة: ${balanceResult['balanceText']}';
        syncNotifications.add(balanceMsg);

        if (!silent) {
          progressCallback?.call('notify', status: balanceMsg);
          print('[SYNC][API_HELPER] $balanceMsg');
        }
      } else {
        final balanceErrorMsg =
            'فشل في جلب رصيد اللوحة: ${balanceResult['message']}';
        syncNotifications.add(balanceErrorMsg);
        if (!silent) {
          progressCallback?.call('notify', status: balanceErrorMsg);
          print('[SYNC][API_HELPER][BALANCE_ERROR] $balanceErrorMsg');
        }
      }

      if (!silent) {
        progressCallback?.call('balance_sync', status: 'done');
      }
    } catch (e) {
      final balanceErrorMsg = 'خطأ في مزامنة رصيد اللوحة: $e';
      syncNotifications.add(balanceErrorMsg);
      if (!silent) {
        progressCallback?.call('notify', status: balanceErrorMsg);
        print('[SYNC][API_HELPER][BALANCE_ERROR] $balanceErrorMsg');
      }
    }

    // مزامنة صامتة لعناوين IP بعد المزامنة الرئيسية
    try {
      if (!silent) {
        progressCallback?.call('ip_sync', status: 'start');
      }

      final ipSyncResults = await syncSubscribersIP(
        board: board,
        token: token,
        progressCallback: silent ? null : progressCallback,
      );

      // إضافة نتائج مزامنة IP إلى النتائج الرئيسية
      syncNotifications.addAll(ipSyncResults);

      if (!silent) {
        for (final msg in ipSyncResults) {
          progressCallback?.call('notify', status: msg);
        }
        print('[SYNC][API_HELPER] تمت مزامنة عناوين IP بنجاح');
      }
    } catch (e) {
      final ipErrorMsg = 'خطأ في مزامنة عناوين IP: $e';
      syncNotifications.add(ipErrorMsg);
      if (!silent) {
        progressCallback?.call('notify', status: ipErrorMsg);
        print('[SYNC][API_HELPER][IP_ERROR] $ipErrorMsg');
      }
    }

    return syncNotifications;
  } catch (e, st) {
    if (!silent) print('[SYNC][API_HELPER][ERROR] $e\n$st');
    return ['[SYNC][ERROR] $e'];
  }
}

/// مزامنة الباقات فقط من السيرفر وتحديث قاعدة البيانات المحلية
Future<void> syncPackagesFromServer({
  required Map<String, dynamic> board,
  void Function(String step, {String? status, int? count, int? total})?
  progressCallback,
  bool silent = false,
}) async {
  try {
    progressCallback?.call('login', status: 'in_progress');
    String? token = board['token'];
    if (token == null || token.isEmpty) {
      final loginResult = await tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        token = loginResult['token'];
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': token,
        });
      } else {
        throw Exception('فشل تسجيل الدخول: ${loginResult['message']}');
      }
    }
    progressCallback?.call('login', status: 'done');
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    final client = IOClient(ioClient);
    // جلب المشتركين لاستنتاج الباقات منهم
    progressCallback?.call('users', status: 'in_progress');
    int page = 1;
    int perPage = 10;
    bool hasMore = true;
    List<Map<String, dynamic>> allUsers = [];
    int totalUsers = 0;

    while (hasMore) {
      http.Response usersResp;
      final userBody = {'page': page, 'per_page': perPage};
      final userPayload = encryptWithOpenSSL(
        jsonEncode(userBody),
        'abcdefghijuklmno0123456789012345',
      );
      usersResp = await client.post(
        Uri.parse('https://${board['url']}/admin/api/index.php/api/index/user'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
          'Authorization': 'Bearer $token',
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Origin': 'https://${board['url']}',
          'Referer': 'https://${board['url']}/',
          'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
        },
        body: jsonEncode({'payload': userPayload}),
      );
      if (usersResp.statusCode != 200) {
        throw Exception('فشل جلب المشتركين: ${usersResp.body}');
      }
      final usersJson = jsonDecode(usersResp.body);
      final users = List<Map<String, dynamic>>.from(usersJson['data'] ?? []);
      perPage = int.tryParse(usersJson['per_page']?.toString() ?? '10') ?? 10;
      totalUsers = int.tryParse(usersJson['total']?.toString() ?? '0') ?? 0;
      allUsers.addAll(users);
      int currentPage = usersJson['current_page'] ?? page;
      int lastPage = usersJson['last_page'] ?? page;
      hasMore = currentPage < lastPage;
      page++;
      progressCallback?.call(
        'users',
        status: 'in_progress',
        count: allUsers.length,
        total: totalUsers,
      );
    }

    progressCallback?.call(
      'users',
      status: 'done',
      count: allUsers.length,
      total: totalUsers,
    );

    client.close();

    // استنتاج وإنشاء الباقات من بيانات المشتركين
    final packageSyncResults = await syncPackagesFromUsers(
      users: allUsers,
      token: token!,
      baseUrl: board['url'],
      progressCallback: progressCallback,
    );

    progressCallback?.call(
      'packages_extraction',
      status: 'done',
      count: packageSyncResults.length,
      total: packageSyncResults.length,
    );
    if (!silent) {
      print(
        '[SYNC][API_HELPER] تمت مزامنة الباقات من خلال استنتاجها من بيانات المشتركين بنجاح',
      );
      for (final msg in packageSyncResults) {
        print('[SYNC][PACKAGES] $msg');
      }
    }
  } catch (e, st) {
    if (!silent) print('[SYNC][API_HELPER][ERROR][PKG] $e\n$st');
    rethrow;
  }
}

/// مزامنة المشتركين فقط من السيرفر وتحديث قاعدة البيانات المحلية (بدون الباقات)
///
/// ⚠️ مهم: لا يتم جلب حقل 'balance' من السيرفر للحفاظ على قيم 'totalDebt' المحلية
/// هذا يمنع فقدان البيانات المالية المدارة محلياً في التطبيق
Future<void> syncSubscribersFromServer({
  required Map<String, dynamic> board,
  void Function(String step, {String? status, int? count, int? total})?
  progressCallback,
  bool silent = false,
}) async {
  try {
    progressCallback?.call('login', status: 'in_progress');
    String? token = board['token'];
    if (token == null || token.isEmpty) {
      final loginResult = await tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        token = loginResult['token'];
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': token,
        });
      } else {
        throw Exception('فشل تسجيل الدخول: ${loginResult['message']}');
      }
    }
    progressCallback?.call('login', status: 'done');
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    final client = IOClient(ioClient);
    // جلب المشتركين فقط
    progressCallback?.call('users', status: 'in_progress');
    int page = 1;
    int perPage = 10;
    bool hasMore = true;
    List<Map<String, dynamic>> allUsers = [];
    int totalUsers = 0;
    // جلب جميع الباقات من قاعدة البيانات لمطابقة الأسعار
    final db = await DBHelper.instance.database;
    final packagesList = await db.query('subscriptions');
    final Map<String, double> packagePrices = {
      for (final pkg in packagesList)
        pkg['id'].toString(): (pkg['sellPrice'] is num
            ? (pkg['sellPrice'] as num).toDouble()
            : 0.0),
    };
    while (hasMore) {
      http.Response usersResp;
      final userBody = {'page': page, 'per_page': perPage};
      final userPayload = encryptWithOpenSSL(
        jsonEncode(userBody),
        'abcdefghijuklmno0123456789012345',
      );
      usersResp = await client.post(
        Uri.parse('https://${board['url']}/admin/api/index.php/api/index/user'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
          'Authorization': 'Bearer $token',
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Origin': 'https://${board['url']}',
          'Referer': 'https://${board['url']}/',
          'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
        },
        body: jsonEncode({'payload': userPayload}),
      );
      if (usersResp.statusCode != 200) {
        throw Exception('فشل جلب المشتركين: ${usersResp.body}');
      }
      final usersJson = jsonDecode(usersResp.body);
      final users = List<Map<String, dynamic>>.from(usersJson['data'] ?? []);
      perPage = int.tryParse(usersJson['per_page']?.toString() ?? '10') ?? 10;
      totalUsers = int.tryParse(usersJson['total']?.toString() ?? '0') ?? 0;
      allUsers.addAll(users);
      int currentPage = usersJson['current_page'] ?? page;
      int lastPage = usersJson['last_page'] ?? page;
      hasMore = currentPage < lastPage;
      page++;
      progressCallback?.call(
        'users',
        status: 'in_progress',
        count: allUsers.length,
        total: totalUsers,
      );
    }
    progressCallback?.call(
      'users',
      status: 'done',
      count: allUsers.length,
      total: totalUsers,
    );
    // تحديث قاعدة البيانات (تخزين المشتركين)
    progressCallback?.call('db', status: 'in_progress');
    int userIndex = 0;
    for (final user in allUsers) {
      userIndex++;
      final profile = user['profile_details'] ?? {};
      final subscriptionId = user['profile_id']?.toString();
      // لا تجلب أي سعر من قاعدة البيانات أو السيرفر، اجعل السعر دائماً 0 عند المزامنة
      final subscriptionPrice = 0.0;
      final phone = user['phone'] ?? '';
      // جلب البيانات المحلية للمشترك للحفاظ على totalDebt ورصيد المحفظة
      final db = await DBHelper.instance.database;
      final localResult = await db.query(
        'subscribers',
        where: 'remoteId = ?',
        whereArgs: [user['id']],
        limit: 1,
      );
      final localTotalDebt = localResult.isNotEmpty
          ? (localResult.first['totalDebt'] as num?)?.toDouble() ?? 0.0
          : 0.0;

      // استخراج رصيد المحفظة من notes المحلي
      final localNotes = localResult.isNotEmpty
          ? localResult.first['notes']?.toString() ?? ''
          : '';
      final walletMatch = RegExp(
        r'wallet:(\d+(?:\.\d+)?)',
      ).firstMatch(localNotes);
      final walletBalance = walletMatch?.group(0);

      // دمج notes السيرفر مع رصيد المحفظة المحلي
      final serverNotes = user['notes']?.toString() ?? '';
      final mergedNotes = _mergeNotesWithWallet(serverNotes, walletBalance);

      await DBHelper.instance.insertOrUpdateSubscriber({
        'user': user['username'],
        'name': user['firstname'] ?? '',
        'totalDebt': localTotalDebt, // الحفاظ على القيمة المحلية
        'subscriptionPrice': subscriptionPrice,
        'subscriptionType': profile['name'] ?? '',
        'startDate': user['created_at'] ?? '',
        'endDate': user['expiration'] ?? '',
        'phone': phone,
        'notes': mergedNotes.isEmpty ? null : mergedNotes,
        'ip': user['static_ip'],
        'subscriptionId': subscriptionId,
        'buyPrice': null,
        'remoteId': user['id'],
        'contract': user['contract_id'] == 1 ? 1 : 0,
        'online_status': user['online_status'] == 1 ? 1 : 0,
        'status': (user['online_status'] == 1 ? 'متصل' : 'غير متصل'),
        'source_type': 'sas',
      });
      progressCallback?.call(
        'db',
        status: 'in_progress',
        count: userIndex,
        total: allUsers.length,
      );
    }
    progressCallback?.call(
      'db',
      status: 'done',
      count: allUsers.length,
      total: allUsers.length,
    );

    // استنتاج وإنشاء الباقات من بيانات المشتركين
    try {
      await syncPackagesFromUsers(
        users: allUsers,
        token: token!,
        baseUrl: board['url'],
        progressCallback: progressCallback,
      );
      if (!silent)
        print(
          '[SYNC][API_HELPER] تم استنتاج الباقات من بيانات المشتركين بنجاح',
        );
    } catch (e) {
      if (!silent)
        print('[SYNC][API_HELPER][PACKAGES_ERROR] خطأ في استنتاج الباقات: $e');
    }

    // مزامنة رصيد اللوحة أيضاً
    try {
      final balanceResult = await fetchBoardBalance(board: board, token: token);

      if (balanceResult['success'] == true) {
        // تحديث رصيد اللوحة في قاعدة البيانات
        final updatedBoard = Map<String, dynamic>.from(board);
        updatedBoard['balance'] = balanceResult['balance'];
        updatedBoard['balance_text'] = balanceResult['balanceText'];
        updatedBoard['currency'] = balanceResult['currency'];

        await DBHelper.instance.updateBoard(board['id'], updatedBoard);

        if (!silent) {
          print(
            '[SYNC][API_HELPER] تم تحديث رصيد اللوحة: ${balanceResult['balanceText']}',
          );
        }
      }
    } catch (e) {
      if (!silent) {
        print(
          '[SYNC][API_HELPER][BALANCE_ERROR] خطأ في مزامنة رصيد اللوحة: $e',
        );
      }
    }

    if (!silent) print('[SYNC][API_HELPER] تمت مزامنة المشتركين فقط بنجاح');
  } catch (e, st) {
    if (!silent) print('[SYNC][API_HELPER][ERROR][USERS] $e\n$st');
    rethrow;
  }
}

/// جلب تفاصيل التفعيل لمستخدم معين للحصول على أسعار الباقة
Future<Map<String, dynamic>?> fetchActivationData({
  required int userId,
  required String token,
  required String baseUrl,
}) async {
  try {
    // إعداد IOClient مع تجاوز شهادة SSL
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    final client = IOClient(ioClient);

    final response = await client.get(
      Uri.parse(
        'https://$baseUrl/admin/api/index.php/api/user/activationData/$userId',
      ),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Authorization': 'Bearer $token',
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      },
    );

    client.close();

    if (response.statusCode == 200) {
      final jsonData = jsonDecode(response.body);
      if (jsonData['status'] == 200 && jsonData['data'] != null) {
        return jsonData['data'] as Map<String, dynamic>;
      }
    }

    print(
      '[ACTIVATION_DATA] فشل جلب تفاصيل التفعيل للمستخدم $userId: ${response.statusCode}',
    );
    return null;
  } catch (e) {
    print('[ACTIVATION_DATA] خطأ في جلب تفاصيل التفعيل للمستخدم $userId: $e');
    return null;
  }
}

/// استنتاج وإنشاء الباقات من بيانات المشتركين
Future<List<String>> syncPackagesFromUsers({
  required List<Map<String, dynamic>> users,
  required String token,
  required String baseUrl,
  void Function(String step, {String? status, int? count, int? total})?
  progressCallback,
}) async {
  List<String> syncNotifications = [];

  try {
    progressCallback?.call('packages_extraction', status: 'in_progress');

    // استخراج profile_ids الفريدة
    Set<int> uniqueProfileIds = users
        .map((u) => u['profile_id'] as int?)
        .where((id) => id != null)
        .cast<int>()
        .toSet();

    print(
      '[PACKAGES_SYNC] تم العثور على ${uniqueProfileIds.length} باقة فريدة',
    );

    int processedPackages = 0;
    final db = await DBHelper.instance.database;

    for (int profileId in uniqueProfileIds) {
      try {
        // البحث عن مستخدم بهذا profile_id للحصول على تفاصيل التفعيل
        final sampleUser = users.firstWhere(
          (u) => u['profile_id'] == profileId,
          orElse: () => <String, dynamic>{},
        );

        if (sampleUser.isEmpty) continue;

        // جلب تفاصيل التفعيل
        final activationData = await fetchActivationData(
          userId: sampleUser['id'] as int,
          token: token,
          baseUrl: baseUrl,
        );

        if (activationData != null) {
          // استخراج البيانات المطلوبة
          final packageId =
              activationData['profile_id']?.toString() ?? profileId.toString();
          final packageName =
              activationData['profile_name']?.toString() ??
              sampleUser['profile_details']?['name']?.toString() ??
              'باقة $profileId';

          // استخراج الأسعار
          double buyPrice = 0.0;
          double sellPrice = 0.0;

          // سعر الشراء من n_required_amount أو unit_price
          if (activationData['n_required_amount'] != null) {
            buyPrice = (activationData['n_required_amount'] as num).toDouble();
          } else if (activationData['unit_price'] != null) {
            // إزالة النص والفواصل من unit_price
            final unitPriceStr = activationData['unit_price']
                .toString()
                .replaceAll(RegExp(r'[^\d.]'), '');
            buyPrice = double.tryParse(unitPriceStr) ?? 0.0;
          }

          // سعر البيع من user_price
          if (activationData['user_price'] != null) {
            sellPrice = (activationData['user_price'] as num).toDouble();
          }

          // إنشاء أو تحديث الباقة باستخدام الدالة المحسنة
          final packageData = {
            'id': packageId,
            'name': packageName,
            'buyPrice': buyPrice,
            'sellPrice': sellPrice,
          };

          // التحقق من وجود الباقة للإشعارات
          final existingPackage = await db.query(
            'subscriptions',
            where: 'id = ?',
            whereArgs: [packageId],
            limit: 1,
          );

          final isNewPackage = existingPackage.isEmpty;
          final existing = isNewPackage ? null : existingPackage.first;

          // استخدام الدالة المحسنة التي تحافظ على custom_buy_price
          await DBHelper.instance.insertOrUpdateSubscription(packageData);

          // إضافة الإشعارات المناسبة
          if (isNewPackage) {
            syncNotifications.add(
              'تمت إضافة باقة جديدة: $packageName (شراء: ${buyPrice.toStringAsFixed(0)}, بيع: ${sellPrice.toStringAsFixed(0)})',
            );
          } else {
            // إشعار فقط إذا تغير شيء مهم
            if (existing!['name'] != packageName) {
              syncNotifications.add(
                'تم تحديث اسم الباقة ($packageId): $packageName',
              );
            }
            if ((existing['buyPrice'] as num?)?.toDouble() != buyPrice ||
                (existing['sellPrice'] as num?)?.toDouble() != sellPrice) {
              syncNotifications.add('تم تحديث أسعار الباقة: $packageName');
            }
          }

          print(
            '[PACKAGES_SYNC] تمت معالجة الباقة: $packageName (ID: $packageId)',
          );
        } else {
          print('[PACKAGES_SYNC] فشل جلب تفاصيل الباقة $profileId');
        }

        processedPackages++;
        progressCallback?.call(
          'packages_extraction',
          status: 'in_progress',
          count: processedPackages,
          total: uniqueProfileIds.length,
        );

        // تأخير قصير لتجنب إرهاق السيرفر
        await Future.delayed(const Duration(milliseconds: 100));
      } catch (e) {
        print('[PACKAGES_SYNC] خطأ في معالجة الباقة $profileId: $e');
        syncNotifications.add('خطأ في معالجة الباقة $profileId: $e');
      }
    }

    progressCallback?.call(
      'packages_extraction',
      status: 'done',
      count: processedPackages,
      total: uniqueProfileIds.length,
    );

    print('[PACKAGES_SYNC] تمت معالجة $processedPackages باقة بنجاح');
    return syncNotifications;
  } catch (e) {
    print('[PACKAGES_SYNC] خطأ عام في مزامنة الباقات: $e');
    progressCallback?.call('packages_extraction', status: 'error');
    return ['خطأ في مزامنة الباقات: $e'];
  }
}

/// دمج notes السيرفر مع رصيد المحفظة المحلي
/// يحافظ على رصيد المحفظة المحلي ويدمجه مع notes السيرفر
String _mergeNotesWithWallet(String serverNotes, String? walletBalance) {
  // إزالة أي wallet موجود في notes السيرفر
  final cleanServerNotes = serverNotes
      .replaceAll(RegExp(r'wallet:\d+(?:\.\d+)?'), '')
      .trim();

  // إذا كان هناك رصيد محفظة محلي، أضفه في البداية
  if (walletBalance != null && walletBalance.isNotEmpty) {
    if (cleanServerNotes.isNotEmpty) {
      return '$walletBalance $cleanServerNotes';
    } else {
      return walletBalance;
    }
  }

  // إذا لم يكن هناك رصيد محفظة، أرجع notes السيرفر فقط
  return cleanServerNotes;
}

/// جلب بيانات المشتركين المتصلين (Online) مع عناوين IP الفعلية
Future<List<Map<String, dynamic>>> fetchOnlineUsers({
  required Map<String, dynamic> board,
  String? token,
}) async {
  HttpClient? httpClient;
  IOClient? client;

  try {
    // فحص صالحية التوكن أولاً
    String? authToken = token;
    if (!isTokenValid(authToken)) {
      debugPrint('[ONLINE_USERS] التوكن غير صالح، محاولة تسجيل دخول جديد...');
      final loginResult = await tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        authToken = loginResult['token'];
        // تحديث التوكن في قاعدة البيانات
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': authToken,
        });
      } else {
        throw Exception('فشل تسجيل الدخول وجلب التوكن');
      }
    }

    // إنشاء HTTP client مع تجاوز شهادة SSL
    httpClient = HttpClient();
    httpClient.badCertificateCallback =
        (X509Certificate cert, String host, int port) => true;
    client = IOClient(httpClient);

    List<Map<String, dynamic>> allOnlineUsers = [];
    int page = 1;
    int perPage = 50; // زيادة عدد العناصر لكل صفحة
    bool hasMore = true;

    while (hasMore) {
      final body = {'page': page, 'per_page': perPage};
      final payload = encryptWithOpenSSL(
        jsonEncode(body),
        'abcdefghijuklmno0123456789012345',
      );

      final response = await client.post(
        Uri.parse(
          'https://${board['url']}/admin/api/index.php/api/index/online',
        ),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
          'Authorization': 'Bearer $authToken',
          'User-Agent':
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          'Origin': 'https://${board['url']}',
          'Referer': 'https://${board['url']}/',
          'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
        },
        body: jsonEncode({'payload': payload}),
      );

      if (response.statusCode != 200) {
        throw Exception('فشل جلب المشتركين المتصلين: ${response.body}');
      }

      final responseData = jsonDecode(response.body);

      if (responseData['data'] != null) {
        final List<dynamic> users = responseData['data'];
        allOnlineUsers.addAll(users.cast<Map<String, dynamic>>());

        // فحص إذا كان هناك صفحات أخرى
        final currentPage = responseData['current_page'] ?? 1;
        final lastPage = responseData['last_page'] ?? 1;
        hasMore = currentPage < lastPage;
        page++;
      } else {
        hasMore = false;
      }
    }

    client.close();
    httpClient.close();
    debugPrint('[ONLINE_API] تم جلب ${allOnlineUsers.length} مشترك متصل');
    return allOnlineUsers;
  } catch (e) {
    debugPrint('[ONLINE_API][ERROR] خطأ في جلب المشتركين المتصلين: $e');
    try {
      client?.close();
      httpClient?.close();
    } catch (_) {}
    return [];
  }
}

/// فحص صالحية JWT Token
bool isTokenValid(String? token) {
  if (token == null || token.isEmpty) return false;

  try {
    // فصل أجزاء JWT Token
    final parts = token.split('.');
    if (parts.length != 3) return false;

    // فك تشفير payload (الجزء الثاني)
    final payload = parts[1];
    // إضافة padding إذا لزم الأمر
    final normalizedPayload = payload.padRight(
      (payload.length + 3) ~/ 4 * 4,
      '=',
    );

    final decoded = utf8.decode(base64Decode(normalizedPayload));
    final Map<String, dynamic> claims = jsonDecode(decoded);

    // فحص تاريخ انتهاء الصلاحية
    final exp = claims['exp'];
    if (exp == null) return false;

    final expiryTime = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    final now = DateTime.now();

    // إضافة هامش أمان 5 دقائق قبل انتهاء الصلاحية
    final safetyMargin = Duration(minutes: 5);
    return now.isBefore(expiryTime.subtract(safetyMargin));
  } catch (e) {
    debugPrint('[TOKEN_VALIDATION] خطأ في فحص صالحية التوكن: $e');
    return false;
  }
}

/// جلب الرصيد الحالي للوحة
Future<Map<String, dynamic>> fetchBoardBalance({
  required Map<String, dynamic> board,
  String? token,
}) async {
  try {
    String? authToken = token ?? board['token'];

    // فحص صالحية التوكن أولاً
    if (!isTokenValid(authToken)) {
      debugPrint('[BALANCE_API] التوكن غير صالح، محاولة تسجيل دخول جديد...');
      final loginResult = await tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        authToken = loginResult['token'];
        // تحديث التوكن في قاعدة البيانات
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': authToken,
        });
      } else {
        return {
          'success': false,
          'message': 'فشل في تسجيل الدخول وجلب توكن صالح',
        };
      }
    }

    final baseUrl = board['url']?.toString().trim() ?? '';
    if (baseUrl.isEmpty) {
      return {'success': false, 'message': 'رابط اللوحة غير صحيح'};
    }

    // إعداد IOClient مع تجاوز شهادة SSL
    final ioClient = HttpClient()
      ..badCertificateCallback = (cert, host, port) => true;
    final client = IOClient(ioClient);

    final url = baseUrl.startsWith('http')
        ? '$baseUrl/admin/api/index.php/api/widgetData/internal/wd_balance'
        : 'https://$baseUrl/admin/api/index.php/api/widgetData/internal/wd_balance';

    debugPrint('[BALANCE_API] جلب الرصيد من: $url');

    final response = await client.get(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json, text/plain, */*',
        'Authorization': 'Bearer $authToken',
        'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Referer': baseUrl.startsWith('http')
            ? '$baseUrl/'
            : 'https://$baseUrl/',
        'Accept-Language': 'en,en-US;q=0.9,ar;q=0.8',
      },
    );

    client.close();

    debugPrint('[BALANCE_API] كود الاستجابة: ${response.statusCode}');
    debugPrint('[BALANCE_API] محتوى الاستجابة: ${response.body}');

    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['status'] == 200 && decoded['data'] != null) {
        final balanceText = decoded['data'].toString();

        // استخراج الرقم من النص (مثل "IQD 2,577,969.00")
        final numberRegex = RegExp(r'[\d,]+\.?\d*');
        final match = numberRegex.firstMatch(balanceText);

        if (match != null) {
          final balanceString = match.group(0)?.replaceAll(',', '') ?? '0';
          final balanceValue = double.tryParse(balanceString) ?? 0.0;

          return {
            'success': true,
            'balance': balanceValue,
            'balanceText': balanceText,
            'currency': balanceText.contains('IQD') ? 'IQD' : 'USD',
          };
        }

        return {
          'success': true,
          'balance': 0.0,
          'balanceText': balanceText,
          'currency': 'IQD',
        };
      } else {
        return {
          'success': false,
          'message': decoded['message'] ?? 'فشل في جلب الرصيد',
        };
      }
    } else {
      return {
        'success': false,
        'message': 'فشل في الاتصال بالخادم: ${response.statusCode}',
      };
    }
  } catch (e) {
    debugPrint('[BALANCE_API][ERROR] خطأ في جلب الرصيد: $e');
    return {'success': false, 'message': 'خطأ في جلب الرصيد: $e'};
  }
}

/// مزامنة عناوين IP للمشتركين بعد المزامنة الرئيسية
Future<List<String>> syncSubscribersIP({
  required Map<String, dynamic> board,
  String? token,
  Function(String, {String? status, int? count, int? total})? progressCallback,
}) async {
  List<String> syncNotifications = [];

  try {
    progressCallback?.call('ip_sync', status: 'start');

    // جلب بيانات المشتركين المتصلين
    final onlineUsers = await fetchOnlineUsers(board: board, token: token);

    if (onlineUsers.isEmpty) {
      syncNotifications.add('لا توجد مشتركين متصلين حالياً');
      progressCallback?.call('ip_sync', status: 'done', count: 0, total: 0);
      return syncNotifications;
    }

    // جلب جميع المشتركين من قاعدة البيانات المحلية
    final db = await DBHelper.instance.database;
    final localSubscribers = await db.query('subscribers');

    int updatedCount = 0;
    int totalProcessed = 0;

    for (final onlineUser in onlineUsers) {
      totalProcessed++;
      progressCallback?.call(
        'ip_sync',
        status: 'in_progress',
        count: totalProcessed,
        total: onlineUsers.length,
      );

      final username = onlineUser['username']?.toString();
      final framedIP = onlineUser['framedipaddress']?.toString();

      if (username == null || framedIP == null || framedIP.isEmpty) {
        continue;
      }

      // البحث عن المشترك في قاعدة البيانات المحلية
      final localSubscriber = localSubscribers.firstWhere(
        (sub) => sub['user'] == username,
        orElse: () => <String, dynamic>{},
      );

      if (localSubscriber.isNotEmpty) {
        final currentIP = localSubscriber['ip']?.toString() ?? '';
        final isCustomIP = (localSubscriber['custom_ip'] ?? 0) == 1;

        // تجاهل المشتركين الذين لديهم IP مخصص
        if (isCustomIP) {
          debugPrint(
            '[IP_SYNC] تجاهل المشترك $username - لديه IP مخصص: $currentIP',
          );
          continue;
        }

        // تحديث IP إذا كان مختلفاً
        if (currentIP != framedIP) {
          await db.update(
            'subscribers',
            {'ip': framedIP},
            where: 'id = ?',
            whereArgs: [localSubscriber['id']],
          );

          updatedCount++;
          debugPrint(
            '[IP_SYNC] تم تحديث IP للمشترك $username: $currentIP → $framedIP',
          );
        }
      }
    }

    syncNotifications.add(
      'تم تحديث عناوين IP لـ $updatedCount مشترك من أصل ${onlineUsers.length} متصل',
    );
    progressCallback?.call(
      'ip_sync',
      status: 'done',
      count: updatedCount,
      total: onlineUsers.length,
    );

    debugPrint(
      '[IP_SYNC] مزامنة IP مكتملة: $updatedCount تحديث من أصل ${onlineUsers.length} متصل',
    );
  } catch (e) {
    final errorMsg = 'خطأ في مزامنة عناوين IP: $e';
    syncNotifications.add(errorMsg);
    debugPrint('[IP_SYNC][ERROR] $errorMsg');
    progressCallback?.call('ip_sync', status: 'error');
  }

  return syncNotifications;
}
