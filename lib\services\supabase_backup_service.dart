import 'dart:io';
import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'package:intl/intl.dart';
import '../db_helper.dart';
import '../core/managers/internet_status_manager.dart';

/// خدمة النسخ الاحتياطي السحابي باستخدام Supabase
class SupabaseBackupService {
  static final SupabaseBackupService _instance =
      SupabaseBackupService._internal();
  factory SupabaseBackupService() => _instance;
  SupabaseBackupService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;

  // مفاتيح SharedPreferences
  static const String _lastBackupKey = 'last_supabase_backup';
  static const String _lastBackupSizeKey = 'last_supabase_backup_size';
  static const String _totalBackupsKey = 'total_supabase_backups';
  static const int _maxBackupsPerUser = 5;

  /// التحقق من الاتصال (بدون مصادقة)
  bool get isUserLoggedIn => true; // دائماً متاح

  /// الحصول على معرف المستخدم
  Future<String> get currentUserId async {
    // التحقق من المستخدم المسجل في Supabase
    final user = _supabase.auth.currentUser;
    if (user != null) {
      return user.id;
    }

    // إذا لم يكن مسجل، استخدم معرف الجهاز مع بادئة
    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString('device_id');

    if (deviceId == null) {
      // إنشاء معرف فريد ثابت للجهاز
      deviceId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString('device_id', deviceId);
      print('🆔 تم إنشاء معرف جهاز جديد: $deviceId');
    }

    return 'anonymous_$deviceId';
  }

  /// التحقق من الاتصال بالإنترنت (مهجور - استخدم InternetStatusManager)
  @Deprecated('استخدم InternetStatusManager.isConnected بدلاً من ذلك')
  Future<bool> hasInternetConnection() async {
    return InternetStatusManager.isConnected;
  }

  /// رفع نسخة احتياطية JSON إلى Supabase Storage
  Future<SupabaseBackupResult> uploadJsonBackup(
    Map<String, dynamic> backupData, {
    bool enforceBackupLimit = true,
    int maxRetries = 3,
  }) async {
    int attempt = 0;
    SupabaseBackupException? lastError;

    while (attempt < maxRetries) {
      try {
        print(
          '🚀 بدء محاولة الرفع إلى Supabase (${attempt + 1}/$maxRetries)...',
        );

        // التحقق من الشروط الأساسية
        await _validatePreconditions();

        final userId = await currentUserId;
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final fileName = 'itower_${userId}_$timestamp.json';

        // تحويل البيانات إلى JSON
        final jsonString = JsonEncoder.withIndent('  ').convert(backupData);
        final jsonBytes = utf8.encode(jsonString);
        final checksum = _calculateChecksum(jsonBytes);

        print('📊 حجم البيانات: ${_formatBytes(jsonBytes.length)}');

        // رفع الملف إلى Supabase Storage
        final bucketName = 'userbackups'; // اسم bucket في Supabase
        final filePath = '$userId/$fileName';

        print('📤 رفع الملف إلى Supabase Storage...');
        print('🗂️ المسار: $bucketName/$filePath');

        await _supabase.storage
            .from(bucketName)
            .uploadBinary(
              filePath,
              Uint8List.fromList(jsonBytes),
              fileOptions: const FileOptions(
                contentType: 'application/json',
                upsert: true,
              ),
            );

        print('✅ تم رفع الملف بنجاح إلى Supabase!');

        // الحصول على رابط التحميل العام
        final publicUrl = _supabase.storage
            .from(bucketName)
            .getPublicUrl(filePath);

        print('🔗 رابط التحميل: $publicUrl');

        // حفظ معلومات النسخة الاحتياطية في قاعدة البيانات
        await _saveBackupInfoToDatabase(
          userId: userId,
          fileName: fileName,
          filePath: filePath,
          backupData: backupData,
          fileSize: jsonBytes.length,
          timestamp: timestamp,
          publicUrl: publicUrl,
          checksum: checksum,
        );

        // تطبيق حد النسخ الاحتياطي
        if (enforceBackupLimit) {
          await _enforceBackupLimit(userId);
        }

        // حفظ في SharedPreferences
        await _saveLocalBackupInfo(timestamp, jsonBytes.length);

        print('✅ تم إكمال النسخ الاحتياطي بنجاح!');

        return SupabaseBackupResult(
          success: true,
          fileName: fileName,
          publicUrl: publicUrl,
          fileSize: jsonBytes.length,
          uploadTimestamp: timestamp,
          checksum: checksum,
        );
      } catch (e) {
        lastError = SupabaseBackupException(
          'فشل المحاولة ${attempt + 1}: ${e.toString()}',
        );
        print('❌ خطأ في المحاولة ${attempt + 1}: $e');

        if (attempt < maxRetries - 1) {
          final delay = Duration(seconds: pow(2, attempt).toInt());
          print(
            '⏳ الانتظار لمدة ${delay.inSeconds} ثواني قبل إعادة المحاولة...',
          );
          await Future.delayed(delay);
        }
        attempt++;
      }
    }
    throw lastError!;
  }

  /// الحصول على قائمة النسخ الاحتياطية من Supabase
  Future<List<SupabaseBackupInfo>> getCloudBackups() async {
    try {
      await _validatePreconditions();

      final userId = await currentUserId;
      print('🔍 جاري البحث عن النسخ الاحتياطية في Supabase للمستخدم: $userId');

      // البحث في قاعدة البيانات عن النسخ الاحتياطية
      final response = await _supabase
          .from('backups')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      final backups = <SupabaseBackupInfo>[];

      for (final item in response) {
        try {
          backups.add(
            SupabaseBackupInfo(
              id: item['id'],
              name: item['file_name'],
              publicUrl: item['public_url'],
              size: item['file_size'],
              createdAt: DateTime.parse(item['created_at']),
              checksum: item['checksum'],
              metadata: Map<String, dynamic>.from(item['metadata'] ?? {}),
            ),
          );
        } catch (e) {
          print('⚠️ خطأ في معالجة النسخة الاحتياطية: $e');
        }
      }

      print('✅ تم تحميل ${backups.length} نسخة احتياطية من Supabase');
      return backups;
    } catch (e) {
      print('❌ خطأ في جلب النسخ الاحتياطية من Supabase: $e');
      throw SupabaseBackupException('فشل في جلب القائمة: ${e.toString()}');
    }
  }

  /// حذف نسخة احتياطية من Supabase
  Future<void> deleteCloudBackup(String fileName, String filePath) async {
    try {
      await _validatePreconditions();

      final userId = await currentUserId;
      print('🗑 جاري حذف النسخة الاحتياطية من Supabase: $fileName');

      // حذف الملف من Storage
      await _supabase.storage.from('userbackups').remove([filePath]);

      // حذف السجل من قاعدة البيانات
      await _supabase
          .from('backups')
          .delete()
          .eq('user_id', userId)
          .eq('file_name', fileName);

      print('✅ تم حذف النسخة الاحتياطية بنجاح من Supabase');
    } catch (e) {
      print('❌ خطأ في حذف النسخة من Supabase: $e');
      throw SupabaseBackupException('فشل في الحذف: ${e.toString()}');
    }
  }

  /// تحميل واستعادة نسخة احتياطية من Supabase
  Future<void> downloadAndRestoreBackup(
    String fileName,
    String outputPath,
  ) async {
    try {
      await _validatePreconditions();

      final userId = await currentUserId;
      print('📥 جاري تحميل النسخة الاحتياطية من Supabase: $fileName');

      // تحميل الملف من Storage
      final filePath = '$userId/$fileName';
      final response = await _supabase.storage
          .from('userbackups')
          .download(filePath);

      print('✅ تم تحميل الملف بنجاح، الحجم: ${response.length} bytes');

      // كتابة الملف إلى المسار المحدد
      final outputFile = File(outputPath);
      await outputFile.writeAsBytes(response);

      print('✅ تم حفظ النسخة الاحتياطية في: $outputPath');
    } catch (e) {
      print('❌ خطأ في تحميل النسخة الاحتياطية من Supabase: $e');
      throw SupabaseBackupException('فشل في التحميل: ${e.toString()}');
    }
  }

  /// تصدير البيانات للنسخ السحابي
  Future<Map<String, dynamic>> exportDataForCloudBackup() async {
    final dbHelper = DBHelper.instance;
    try {
      print('🔄 جاري تصدير البيانات للنسخ الاحتياطي في Supabase...');

      // التأكد من وجود الجداول
      await dbHelper.createDevicesTableIfNotExists();

      // جلب البيانات بشكل متوازي
      final futures = await Future.wait([
        dbHelper.getAllSubscribers(),
        dbHelper.getAllTransactions(),
        dbHelper.getAllDevices(),
      ]);

      final subscribers = futures[0] as List<dynamic>;
      final transactions = futures[1] as List<dynamic>;
      final devices = futures[2] as List<Map<String, dynamic>>;

      return {
        'version': '2.0.0',
        'timestamp': DateTime.now().toIso8601String(),
        'app_name': 'iTower',
        'backup_type': 'supabase_cloud_backup',
        'data': {
          'subscribers': subscribers
              .map((s) => s is Map ? s : s.toMap())
              .toList(),
          'transactions': transactions
              .map((t) => t is Map ? t : t.toMap())
              .toList(),
          'devices': devices,
        },
        'metadata': {
          'total_subscribers': subscribers.length,
          'total_transactions': transactions.length,
          'total_devices': devices.length,
          'backup_source': 'manual',
          'platform': Platform.operatingSystem,
          'backup_time': DateTime.now().toIso8601String(),
        },
      };
    } catch (e) {
      print('❌ خطأ في تصدير البيانات: $e');
      throw Exception('فشل في تصدير البيانات: $e');
    }
  }

  // ============ الوظائف المساعدة ============ //

  /// التحقق من الشروط المسبقة للعمليات
  Future<void> _validatePreconditions() async {
    if (!InternetStatusManager.isConnected) {
      throw SupabaseBackupException('لا يوجد اتصال بالإنترنت');
    }
  }

  /// حفظ معلومات النسخة الاحتياطية في قاعدة بيانات Supabase
  Future<void> _saveBackupInfoToDatabase({
    required String userId,
    required String fileName,
    required String filePath,
    required Map<String, dynamic> backupData,
    required int fileSize,
    required int timestamp,
    required String publicUrl,
    required String checksum,
  }) async {
    try {
      await _supabase.from('backups').insert({
        'user_id': userId,
        'file_name': fileName,
        'file_path': filePath,
        'file_size': fileSize,
        'timestamp': timestamp,
        'public_url': publicUrl,
        'checksum': checksum,
        'metadata': {
          'total_subscribers':
              backupData['metadata']?['total_subscribers'] ?? 0,
          'total_transactions':
              backupData['metadata']?['total_transactions'] ?? 0,
          'total_devices': backupData['metadata']?['total_devices'] ?? 0,
          'backup_source': backupData['metadata']?['backup_source'] ?? 'manual',
          'platform':
              backupData['metadata']?['platform'] ?? Platform.operatingSystem,
        },
        'created_at': DateTime.fromMillisecondsSinceEpoch(
          timestamp,
        ).toIso8601String(),
      });

      print('💾 تم حفظ معلومات النسخة الاحتياطية في Supabase');
    } catch (e) {
      print('⚠️ خطأ في حفظ المعلومات في Supabase: $e');
    }
  }

  /// حفظ معلومات النسخة الاحتياطية محلياً
  Future<void> _saveLocalBackupInfo(int timestamp, int fileSize) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastBackupKey, timestamp);
      await prefs.setInt(_lastBackupSizeKey, fileSize);
      await prefs.setInt(
        _totalBackupsKey,
        (prefs.getInt(_totalBackupsKey) ?? 0) + 1,
      );
    } catch (e) {
      print('⚠️ خطأ في حفظ المعلومات محلياً: $e');
    }
  }

  /// تطبيق حد النسخ الاحتياطي
  Future<void> _enforceBackupLimit(String userId) async {
    try {
      final backups = await getCloudBackups();
      if (backups.length > _maxBackupsPerUser) {
        print(
          '⚠️ تجاوز الحد الأقصى للنسخ (${backups.length}/$_maxBackupsPerUser)',
        );

        // ترتيب حسب التاريخ (الأقدم أولاً)
        backups.sort((a, b) => a.createdAt.compareTo(b.createdAt));

        // حذف النسخ الزائدة
        final backupsToDelete = backups.take(
          backups.length - _maxBackupsPerUser,
        );
        for (final backup in backupsToDelete) {
          await deleteCloudBackup(backup.name, '$userId/${backup.name}');
          print('🗑 تم حذف النسخة القديمة: ${backup.name}');
        }
      }
    } catch (e) {
      print('⚠️ خطأ في تطبيق حد النسخ: $e');
    }
  }

  /// حساب checksum للملف
  String _calculateChecksum(List<int> data) {
    return md5.convert(data).toString();
  }

  /// تنسيق حجم الملف
  String _formatBytes(int bytes) {
    return formatFileSize(bytes);
  }
}

// ============ دوال مساعدة مشتركة ============ //

/// تنسيق حجم الملف - دالة مشتركة
String formatFileSize(int bytes) {
  if (bytes <= 0) return '0 B';
  const suffixes = ['B', 'KB', 'MB', 'GB'];
  final i = (log(bytes) / log(1024)).floor();
  return '${(bytes / pow(1024, i)).toStringAsFixed(2)} ${suffixes[i]}';
}

// ============ نماذج البيانات ============ //

class SupabaseBackupResult {
  final bool success;
  final String fileName;
  final String publicUrl;
  final int fileSize;
  final int uploadTimestamp;
  final String checksum;

  SupabaseBackupResult({
    required this.success,
    required this.fileName,
    required this.publicUrl,
    required this.fileSize,
    required this.uploadTimestamp,
    required this.checksum,
  });

  DateTime get uploadDate =>
      DateTime.fromMillisecondsSinceEpoch(uploadTimestamp);
}

class SupabaseBackupInfo {
  final int id;
  final String name;
  final String publicUrl;
  final int size;
  final DateTime createdAt;
  final String? checksum;
  final Map<String, dynamic> metadata;

  SupabaseBackupInfo({
    required this.id,
    required this.name,
    required this.publicUrl,
    required this.size,
    required this.createdAt,
    this.checksum,
    required this.metadata,
  });

  String get formattedSize => _formatBytes(size);
  String get formattedDate => DateFormat('yyyy-MM-dd HH:mm').format(createdAt);

  String _formatBytes(int bytes) {
    return formatFileSize(bytes);
  }
}

class SupabaseBackupException implements Exception {
  final String message;
  SupabaseBackupException(this.message);

  @override
  String toString() => 'SupabaseBackupException: $message';
}
