import '../data/subscriber_model.dart';
import 'subscribers_repository.dart';
import '../data/subscribers_storage.dart';

class SubscribersRepositoryImpl implements SubscribersRepository {
  final SubscribersStorage storage;
  SubscribersRepositoryImpl(this.storage);

  @override
  Future<List<Subscriber>> getAllSubscribers({int? boardId}) =>
      storage.getAll(boardId: boardId);

  @override
  Future<void> addSubscriber(Subscriber subscriber) => storage.add(subscriber);

  @override
  Future<void> updateSubscriber(Subscriber subscriber) =>
      storage.update(subscriber);

  @override
  Future<void> deleteSubscriber(int id) => storage.delete(id);

  @override
  Future<void> payDebt(int subscriberId, double amount, {String? note}) =>
      storage.payDebt(subscriberId, amount, note: note);

  @override
  Future<Subscriber?> getSubscriberById(int id) => storage.getById(id);
}
