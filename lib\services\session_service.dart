import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'dart:io';

/// خدمة إدارة الجلسات النشطة - متوافقة مع النظام الجديد
class SessionService {
  static Timer? _heartbeatTimer;
  static String? _currentSessionToken;
  static String? _currentDeviceId;
  static bool _isInitialized = false;

  static const Duration _heartbeatInterval = Duration(minutes: 2);
  static const Duration _sessionTimeout = Duration(minutes: 10);

  /// تهيئة خدمة الجلسات
  static Future<bool> initialize() async {
    try {
      debugPrint('🔄 [SESSION] تهيئة خدمة الجلسات...');

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('❌ [SESSION] لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // الحصول على معرف الجهاز
      _currentDeviceId = await _getDeviceId();

      // إنشاء جلسة جديدة
      final sessionCreated = await _createSession(user.id);
      if (!sessionCreated) {
        debugPrint('❌ [SESSION] فشل في إنشاء الجلسة');
        return false;
      }

      // بدء heartbeat
      _startHeartbeat();

      _isInitialized = true;
      debugPrint('✅ [SESSION] تم تهيئة خدمة الجلسات بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في تهيئة الجلسات: $e');
      return false;
    }
  }

  /// إنشاء جلسة جديدة
  static Future<bool> _createSession(String userId) async {
    try {
      // إنهاء أي جلسات سابقة لنفس الجهاز
      await _terminateExistingSessions(userId, _currentDeviceId!);

      // ✅ التأكد من وجود الجهاز في قاعدة البيانات قبل إنشاء الجلسة
      await _ensureDeviceExists(userId, _currentDeviceId!);

      // إنشاء token جديد
      _currentSessionToken = const Uuid().v4();

      // الحصول على معلومات الجهاز
      final deviceInfo = await _getDeviceInfo();

      // إدراج الجلسة الجديدة
      await Supabase.instance.client.from('active_sessions').insert({
        'user_id': userId,
        'device_id': _currentDeviceId,
        'session_token': _currentSessionToken,
        'session_start': DateTime.now().toIso8601String(),
        'last_heartbeat': DateTime.now().toIso8601String(),
        'expected_end': DateTime.now().add(_sessionTimeout).toIso8601String(),
        'ip_address': deviceInfo['ip_address'],
        'user_agent': deviceInfo['user_agent'],
        'app_version': deviceInfo['app_version'],
        'is_active': true,
      });

      // تسجيل النشاط
      await _logActivity(userId, 'session_created', {
        'device_id': _currentDeviceId,
        'session_token': _currentSessionToken,
      });

      debugPrint('✅ [SESSION] تم إنشاء جلسة جديدة: $_currentSessionToken');
      return true;
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في إنشاء الجلسة: $e');
      return false;
    }
  }

  /// إنهاء الجلسات الموجودة لنفس الجهاز
  static Future<void> _terminateExistingSessions(
    String userId,
    String deviceId,
  ) async {
    try {
      await Supabase.instance.client
          .from('active_sessions')
          .update({'is_active': false})
          .eq('user_id', userId)
          .eq('device_id', deviceId)
          .eq('is_active', true);

      debugPrint('🔄 [SESSION] تم إنهاء الجلسات السابقة للجهاز: $deviceId');
    } catch (e) {
      debugPrint('⚠️ [SESSION] خطأ في إنهاء الجلسات السابقة: $e');
    }
  }

  /// التأكد من وجود الجهاز في قاعدة البيانات قبل إنشاء الجلسة
  static Future<void> _ensureDeviceExists(
    String userId,
    String deviceId,
  ) async {
    try {
      // فحص وجود الجهاز
      final device = await Supabase.instance.client
          .from('user_devices')
          .select('id')
          .eq('user_id', userId)
          .eq('device_id', deviceId)
          .maybeSingle();

      if (device == null) {
        debugPrint('⚠️ [SESSION] الجهاز غير موجود، انتظار قصير...');

        // انتظار قصير للسماح لعملية ربط الجهاز بالاكتمال
        await Future.delayed(const Duration(milliseconds: 500));

        // فحص مرة أخرى
        final deviceRetry = await Supabase.instance.client
            .from('user_devices')
            .select('id')
            .eq('user_id', userId)
            .eq('device_id', deviceId)
            .maybeSingle();

        if (deviceRetry == null) {
          throw Exception('الجهاز غير موجود في قاعدة البيانات');
        }
      }

      debugPrint('✅ [SESSION] تم التأكد من وجود الجهاز: $deviceId');
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في فحص وجود الجهاز: $e');
      rethrow;
    }
  }

  /// بدء heartbeat
  static void _startHeartbeat() {
    _stopHeartbeat();

    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (timer) async {
      await _sendHeartbeat();
    });

    debugPrint(
      '💓 [SESSION] تم بدء heartbeat - كل ${_heartbeatInterval.inMinutes} دقيقة',
    );
  }

  /// إيقاف heartbeat
  static void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// إرسال heartbeat
  static Future<bool> _sendHeartbeat() async {
    try {
      if (_currentSessionToken == null || _currentDeviceId == null) {
        debugPrint('⚠️ [SESSION] لا توجد جلسة نشطة للـ heartbeat');
        return false;
      }

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('⚠️ [SESSION] لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // تحديث heartbeat
      await Supabase.instance.client
          .from('active_sessions')
          .update({
            'last_heartbeat': DateTime.now().toIso8601String(),
            'expected_end': DateTime.now()
                .add(_sessionTimeout)
                .toIso8601String(),
          })
          .eq('session_token', _currentSessionToken!)
          .eq('is_active', true);

      // تحديث آخر نشاط للمستخدم
      await Supabase.instance.client
          .from('user_accounts')
          .update({'last_activity': DateTime.now().toIso8601String()})
          .eq('user_id', user.id);

      debugPrint('💓 [SESSION] تم إرسال heartbeat بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في إرسال heartbeat: $e');
      return false;
    }
  }

  /// إنهاء الجلسة الحالية
  static Future<bool> terminateSession() async {
    try {
      if (_currentSessionToken == null) {
        debugPrint('ℹ️ [SESSION] لا توجد جلسة نشطة لإنهائها');
        return true;
      }

      final user = Supabase.instance.client.auth.currentUser;

      // إنهاء الجلسة في قاعدة البيانات
      await Supabase.instance.client
          .from('active_sessions')
          .update({'is_active': false})
          .eq('session_token', _currentSessionToken!)
          .eq('is_active', true);

      // تسجيل النشاط
      if (user != null) {
        await _logActivity(user.id, 'session_terminated', {
          'device_id': _currentDeviceId,
          'session_token': _currentSessionToken,
        });
      }

      // إيقاف heartbeat
      _stopHeartbeat();

      // تنظيف المتغيرات
      _currentSessionToken = null;
      _currentDeviceId = null;
      _isInitialized = false;

      debugPrint('✅ [SESSION] تم إنهاء الجلسة بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في إنهاء الجلسة: $e');
      return false;
    }
  }

  /// الحصول على معرف الجهاز
  static Future<String> _getDeviceId() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return 'android_${androidInfo.fingerprint}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return 'ios_${iosInfo.identifierForVendor ?? 'unknown'}';
      }

      return 'unknown_${DateTime.now().millisecondsSinceEpoch}';
    } catch (e) {
      return 'error_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// الحصول على معلومات الجهاز
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return {
          'ip_address': null, // يمكن إضافة منطق للحصول على IP
          'user_agent':
              '${androidInfo.brand} ${androidInfo.model} Android ${androidInfo.version.release}',
          'app_version': '1.0.0', // يمكن جلبها من package_info
        };
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return {
          'ip_address': null,
          'user_agent':
              '${iosInfo.name} ${iosInfo.model} iOS ${iosInfo.systemVersion}',
          'app_version': '1.0.0',
        };
      }

      return {
        'ip_address': null,
        'user_agent': 'Unknown Device',
        'app_version': '1.0.0',
      };
    } catch (e) {
      return {
        'ip_address': null,
        'user_agent': 'Unknown Device',
        'app_version': '1.0.0',
      };
    }
  }

  /// تسجيل النشاط
  static Future<void> _logActivity(
    String userId,
    String actionType,
    Map<String, dynamic> details,
  ) async {
    try {
      await Supabase.instance.client.rpc(
        'log_user_activity',
        params: {
          'p_user_id': userId,
          'p_action_type': actionType,
          'p_action_details': details,
          'p_device_id': _currentDeviceId,
        },
      );
    } catch (e) {
      debugPrint('⚠️ [SESSION] خطأ في تسجيل النشاط: $e');
    }
  }

  /// جلب الجلسات النشطة للمستخدم
  static Future<List<Map<String, dynamic>>> getUserActiveSessions(
    String userId,
  ) async {
    try {
      final response = await Supabase.instance.client
          .from('active_sessions')
          .select('*')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('last_heartbeat', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في جلب الجلسات النشطة: $e');
      return [];
    }
  }

  /// تنظيف الجلسات المنتهية
  static Future<int> cleanupExpiredSessions() async {
    try {
      final result = await Supabase.instance.client.rpc(
        'cleanup_expired_sessions',
      );
      debugPrint('🧹 [SESSION] تم تنظيف $result جلسة منتهية');
      return result ?? 0;
    } catch (e) {
      debugPrint('❌ [SESSION] خطأ في تنظيف الجلسات: $e');
      return 0;
    }
  }

  /// الحصول على معلومات الجلسة الحالية
  static Map<String, dynamic>? getCurrentSessionInfo() {
    if (!_isInitialized || _currentSessionToken == null) {
      return null;
    }

    return {
      'session_token': _currentSessionToken,
      'device_id': _currentDeviceId,
      'is_active': _heartbeatTimer?.isActive ?? false,
    };
  }

  /// تنظيف الخدمة
  static void dispose() {
    _stopHeartbeat();
    _currentSessionToken = null;
    _currentDeviceId = null;
    _isInitialized = false;
    debugPrint('🧹 [SESSION] تم تنظيف خدمة الجلسات');
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static String? get currentSessionToken => _currentSessionToken;
  static String? get currentDeviceId => _currentDeviceId;
}
