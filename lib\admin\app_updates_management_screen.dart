import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:convert';

class AppUpdatesManagementScreen extends StatefulWidget {
  const AppUpdatesManagementScreen({Key? key}) : super(key: key);

  @override
  State<AppUpdatesManagementScreen> createState() =>
      _AppUpdatesManagementScreenState();
}

class _AppUpdatesManagementScreenState
    extends State<AppUpdatesManagementScreen> {
  final _formKey = GlobalKey<FormState>();
  final _versionController = TextEditingController();
  final _buildNumberController = TextEditingController();
  final _releaseNotesController = TextEditingController();
  final _downloadUrlController = TextEditingController();
  final _minCompatibleVersionController = TextEditingController();

  String _selectedPlatform = 'android';
  bool _isForced = false;
  bool _isActive = true;
  bool _databaseMigrationRequired = false;
  List<String> _features = [];
  List<String> _bugFixes = [];
  List<String> _securityUpdates = [];
  List<Map<String, dynamic>> _existingUpdates = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadExistingUpdates();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة تحديثات التطبيق'),
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadExistingUpdates,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // قسم إضافة تحديث جديد
                  _buildNewUpdateSection(colorScheme),

                  const SizedBox(height: 32),

                  // قسم التحديثات الموجودة
                  _buildExistingUpdatesSection(colorScheme),
                ],
              ),
            ),
    );
  }

  Widget _buildNewUpdateSection(ColorScheme colorScheme) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إضافة تحديث جديد',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),

              const SizedBox(height: 20),

              // معلومات أساسية
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _versionController,
                      decoration: const InputDecoration(
                        labelText: 'رقم الإصدار',
                        hintText: '1.1.0',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value?.isEmpty ?? true) return 'مطلوب';
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _buildNumberController,
                      decoration: const InputDecoration(
                        labelText: 'رقم البناء',
                        hintText: '2',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value?.isEmpty ?? true) return 'مطلوب';
                        if (int.tryParse(value!) == null) return 'رقم غير صحيح';
                        return null;
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // المنصة والخيارات
              Row(
                children: [
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedPlatform,
                      decoration: const InputDecoration(
                        labelText: 'المنصة',
                        border: OutlineInputBorder(),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'android',
                          child: Text('Android'),
                        ),
                        DropdownMenuItem(value: 'ios', child: Text('iOS')),
                        DropdownMenuItem(value: 'web', child: Text('Web')),
                      ],
                      onChanged: (value) =>
                          setState(() => _selectedPlatform = value!),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _minCompatibleVersionController,
                      decoration: const InputDecoration(
                        labelText: 'أقل إصدار متوافق',
                        hintText: '1.0.0',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // رابط التحميل
              TextFormField(
                controller: _downloadUrlController,
                decoration: const InputDecoration(
                  labelText: 'رابط التحميل',
                  hintText: 'https://example.com/app.apk',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value?.isEmpty ?? true) return 'مطلوب';
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // ملاحظات الإصدار
              TextFormField(
                controller: _releaseNotesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات الإصدار',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),

              const SizedBox(height: 16),

              // الخيارات
              Wrap(
                spacing: 16,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Checkbox(
                        value: _isForced,
                        onChanged: (value) =>
                            setState(() => _isForced = value!),
                      ),
                      const Text('تحديث إجباري'),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Checkbox(
                        value: _isActive,
                        onChanged: (value) =>
                            setState(() => _isActive = value!),
                      ),
                      const Text('نشط'),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Checkbox(
                        value: _databaseMigrationRequired,
                        onChanged: (value) =>
                            setState(() => _databaseMigrationRequired = value!),
                      ),
                      const Text('يتطلب ترحيل قاعدة البيانات'),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // أزرار الإجراءات
              Row(
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(Icons.add_circle),
                    label: const Text('إضافة ميزة'),
                    onPressed: () => _addFeature(),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.bug_report),
                    label: const Text('إضافة إصلاح'),
                    onPressed: () => _addBugFix(),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.security),
                    label: const Text('تحديث أمني'),
                    onPressed: () => _addSecurityUpdate(),
                  ),
                ],
              ),

              // عرض القوائم المضافة
              if (_features.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildItemsList('الميزات الجديدة', _features, Colors.green),
              ],

              if (_bugFixes.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildItemsList('إصلاحات الأخطاء', _bugFixes, Colors.blue),
              ],

              if (_securityUpdates.isNotEmpty) ...[
                const SizedBox(height: 16),
                _buildItemsList(
                  'التحديثات الأمنية',
                  _securityUpdates,
                  Colors.red,
                ),
              ],

              const SizedBox(height: 24),

              // زر النشر
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.publish),
                  label: const Text('نشر التحديث'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  onPressed: _publishUpdate,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemsList(String title, List<String> items, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontWeight: FontWeight.bold, color: color),
        ),
        const SizedBox(height: 8),
        ...items.asMap().entries.map(
          (entry) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: Row(
              children: [
                Icon(Icons.circle, size: 6, color: color),
                const SizedBox(width: 8),
                Expanded(child: Text(entry.value)),
                IconButton(
                  icon: const Icon(Icons.delete, size: 16),
                  onPressed: () => setState(() => items.removeAt(entry.key)),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExistingUpdatesSection(ColorScheme colorScheme) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'التحديثات الموجودة',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
              ),
            ),

            const SizedBox(height: 16),

            if (_existingUpdates.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('لا توجد تحديثات'),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _existingUpdates.length,
                itemBuilder: (context, index) {
                  final update = _existingUpdates[index];
                  return _buildUpdateCard(update, colorScheme);
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateCard(
    Map<String, dynamic> update,
    ColorScheme colorScheme,
  ) {
    final isActive = update['is_active'] ?? false;
    final isForced = update['is_forced'] ?? false;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: isActive ? Colors.green : Colors.grey,
          child: Text(
            update['platform']?.toString().substring(0, 1).toUpperCase() ?? 'A',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text('${update['version']} (${update['build_number']})'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المنصة: ${update['platform']}'),
            if (isForced)
              const Text(
                'إجباري',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            if (update['created_at'] != null)
              Text('تاريخ الإنشاء: ${_formatDate(update['created_at'])}'),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            PopupMenuItem(
              value: 'toggle',
              child: Text(isActive ? 'إلغاء التفعيل' : 'تفعيل'),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Text('حذف', style: TextStyle(color: Colors.red)),
            ),
          ],
          onSelected: (value) =>
              _handleUpdateAction(update['id'], value.toString()),
        ),
      ),
    );
  }

  Future<void> _loadExistingUpdates() async {
    setState(() => _isLoading = true);

    try {
      final response = await Supabase.instance.client
          .from('app_updates')
          .select('*')
          .order('created_at', ascending: false);

      setState(() {
        _existingUpdates = List<Map<String, dynamic>>.from(response);
      });
    } catch (e) {
      _showError('خطأ في جلب التحديثات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _publishUpdate() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final updateData = {
        'version': _versionController.text,
        'build_number': int.parse(_buildNumberController.text),
        'platform': _selectedPlatform,
        'is_active': _isActive,
        'is_forced': _isForced,
        'min_compatible_version':
            _minCompatibleVersionController.text.isNotEmpty
            ? _minCompatibleVersionController.text
            : null,
        'database_migration_required': _databaseMigrationRequired,
        'release_notes': _releaseNotesController.text,
        'download_url': _downloadUrlController.text,
        'features': _features,
        'bug_fixes': _bugFixes,
        'security_updates': _securityUpdates,
        'published_at': DateTime.now().toIso8601String(),
        'created_by': Supabase.instance.client.auth.currentUser?.id,
      };

      await Supabase.instance.client.from('app_updates').insert(updateData);

      _showSuccess('تم نشر التحديث بنجاح!');
      _clearForm();
      _loadExistingUpdates();
    } catch (e) {
      _showError('خطأ في نشر التحديث: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleUpdateAction(String updateId, String action) async {
    try {
      if (action == 'toggle') {
        // تبديل حالة التفعيل
        final update = _existingUpdates.firstWhere((u) => u['id'] == updateId);
        await Supabase.instance.client
            .from('app_updates')
            .update({'is_active': !(update['is_active'] ?? false)})
            .eq('id', updateId);

        _showSuccess('تم تحديث حالة التحديث');
        _loadExistingUpdates();
      } else if (action == 'delete') {
        // حذف التحديث
        final confirmed = await _showConfirmDialog('هل تريد حذف هذا التحديث؟');
        if (confirmed) {
          await Supabase.instance.client
              .from('app_updates')
              .delete()
              .eq('id', updateId);

          _showSuccess('تم حذف التحديث');
          _loadExistingUpdates();
        }
      }
    } catch (e) {
      _showError('خطأ في تنفيذ العملية: $e');
    }
  }

  void _addFeature() {
    _showInputDialog('إضافة ميزة جديدة', (value) {
      setState(() => _features.add(value));
    });
  }

  void _addBugFix() {
    _showInputDialog('إضافة إصلاح خطأ', (value) {
      setState(() => _bugFixes.add(value));
    });
  }

  void _addSecurityUpdate() {
    _showInputDialog('إضافة تحديث أمني', (value) {
      setState(() => _securityUpdates.add(value));
    });
  }

  void _showInputDialog(String title, Function(String) onAdd) {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'أدخل النص...',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (controller.text.isNotEmpty) {
                onAdd(controller.text);
                Navigator.pop(context);
              }
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  Future<bool> _showConfirmDialog(String message) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('تأكيد'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  void _clearForm() {
    _versionController.clear();
    _buildNumberController.clear();
    _releaseNotesController.clear();
    _downloadUrlController.clear();
    _minCompatibleVersionController.clear();
    setState(() {
      _selectedPlatform = 'android';
      _isForced = false;
      _isActive = true;
      _databaseMigrationRequired = false;
      _features.clear();
      _bugFixes.clear();
      _securityUpdates.clear();
    });
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  @override
  void dispose() {
    _versionController.dispose();
    _buildNumberController.dispose();
    _releaseNotesController.dispose();
    _downloadUrlController.dispose();
    _minCompatibleVersionController.dispose();
    super.dispose();
  }
}
