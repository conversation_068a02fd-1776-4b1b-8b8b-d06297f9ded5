import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// إدارة حدود المزامنة اليومية المحلية
class DailySyncLimits {
  static const String _manualSyncCountKey = 'manual_sync_count_today';
  static const String _lastResetDateKey = 'sync_limits_reset_date';
  static const String _autoSyncCountKey = 'auto_sync_count_today';
  
  static const int maxManualSyncs = 3;
  static const int maxAutoSyncs = 1;
  
  /// فحص وإعادة تعيين العدادات إذا كان يوم جديد
  static Future<void> _checkAndResetIfNewDay() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      final lastResetDate = prefs.getString(_lastResetDateKey);
      
      if (lastResetDate != todayString) {
        // يوم جديد - إعادة تعيين العدادات
        await prefs.setInt(_manualSyncCountKey, 0);
        await prefs.setInt(_autoSyncCountKey, 0);
        await prefs.setString(_lastResetDateKey, todayString);
        debugPrint('🔄 [SYNC_LIMITS] تم إعادة تعيين عدادات المزامنة ليوم جديد: $todayString');
      }
    } catch (e) {
      debugPrint('❌ [SYNC_LIMITS] خطأ في فحص/إعادة تعيين العدادات: $e');
    }
  }
  
  /// فحص إمكانية المزامنة اليدوية
  static Future<Map<String, dynamic>> canPerformManualSync() async {
    try {
      await _checkAndResetIfNewDay();
      
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_manualSyncCountKey) ?? 0;
      final canSync = currentCount < maxManualSyncs;
      final remaining = maxManualSyncs - currentCount;
      
      debugPrint('📊 [SYNC_LIMITS] فحص المزامنة اليدوية: $currentCount/$maxManualSyncs');
      
      return {
        'canSync': canSync,
        'currentCount': currentCount,
        'maxCount': maxManualSyncs,
        'remaining': remaining,
        'message': canSync 
          ? 'يمكنك إجراء $remaining مزامنة أخرى اليوم'
          : 'تم استنفاد المزامنات اليدوية اليوم ($maxManualSyncs/$maxManualSyncs)',
        'resetTime': 'غداً في منتصف الليل',
      };
    } catch (e) {
      debugPrint('❌ [SYNC_LIMITS] خطأ في فحص إمكانية المزامنة اليدوية: $e');
      // في حالة الخطأ، السماح بالمزامنة
      return {
        'canSync': true,
        'currentCount': 0,
        'maxCount': maxManualSyncs,
        'remaining': maxManualSyncs,
        'message': 'فحص الحدود متاح',
        'error': e.toString(),
      };
    }
  }
  
  /// فحص إمكانية المزامنة التلقائية
  static Future<Map<String, dynamic>> canPerformAutoSync() async {
    try {
      await _checkAndResetIfNewDay();
      
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_autoSyncCountKey) ?? 0;
      final canSync = currentCount < maxAutoSyncs;
      
      debugPrint('🤖 [SYNC_LIMITS] فحص المزامنة التلقائية: $currentCount/$maxAutoSyncs');
      
      return {
        'canSync': canSync,
        'currentCount': currentCount,
        'maxCount': maxAutoSyncs,
        'remaining': maxAutoSyncs - currentCount,
        'message': canSync 
          ? 'المزامنة التلقائية متاحة'
          : 'تم تنفيذ المزامنة التلقائية اليوم بالفعل',
      };
    } catch (e) {
      debugPrint('❌ [SYNC_LIMITS] خطأ في فحص إمكانية المزامنة التلقائية: $e');
      return {
        'canSync': false,
        'currentCount': 1,
        'maxCount': maxAutoSyncs,
        'remaining': 0,
        'message': 'خطأ في فحص المزامنة التلقائية',
        'error': e.toString(),
      };
    }
  }
  
  /// تسجيل مزامنة يدوية
  static Future<void> recordManualSync() async {
    try {
      await _checkAndResetIfNewDay();
      
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_manualSyncCountKey) ?? 0;
      final newCount = currentCount + 1;
      await prefs.setInt(_manualSyncCountKey, newCount);
      
      debugPrint('📊 [SYNC_LIMITS] تم تسجيل مزامنة يدوية: $newCount/$maxManualSyncs');
    } catch (e) {
      debugPrint('❌ [SYNC_LIMITS] خطأ في تسجيل المزامنة اليدوية: $e');
    }
  }
  
  /// تسجيل مزامنة تلقائية
  static Future<void> recordAutoSync() async {
    try {
      await _checkAndResetIfNewDay();
      
      final prefs = await SharedPreferences.getInstance();
      final currentCount = prefs.getInt(_autoSyncCountKey) ?? 0;
      final newCount = currentCount + 1;
      await prefs.setInt(_autoSyncCountKey, newCount);
      
      debugPrint('🤖 [SYNC_LIMITS] تم تسجيل مزامنة تلقائية: $newCount/$maxAutoSyncs');
    } catch (e) {
      debugPrint('❌ [SYNC_LIMITS] خطأ في تسجيل المزامنة التلقائية: $e');
    }
  }
  
  /// الحصول على إحصائيات اليوم
  static Future<Map<String, dynamic>> getTodayStats() async {
    try {
      await _checkAndResetIfNewDay();
      
      final prefs = await SharedPreferences.getInstance();
      final manualCount = prefs.getInt(_manualSyncCountKey) ?? 0;
      final autoCount = prefs.getInt(_autoSyncCountKey) ?? 0;
      
      return {
        'manualSyncs': manualCount,
        'autoSyncs': autoCount,
        'maxManualSyncs': maxManualSyncs,
        'maxAutoSyncs': maxAutoSyncs,
        'manualRemaining': maxManualSyncs - manualCount,
        'autoRemaining': maxAutoSyncs - autoCount,
        'totalSyncs': manualCount + autoCount,
        'totalMaxSyncs': maxManualSyncs + maxAutoSyncs,
        'canManualSync': manualCount < maxManualSyncs,
        'canAutoSync': autoCount < maxAutoSyncs,
      };
    } catch (e) {
      debugPrint('❌ [SYNC_LIMITS] خطأ في الحصول على إحصائيات اليوم: $e');
      return {
        'manualSyncs': 0,
        'autoSyncs': 0,
        'maxManualSyncs': maxManualSyncs,
        'maxAutoSyncs': maxAutoSyncs,
        'manualRemaining': maxManualSyncs,
        'autoRemaining': maxAutoSyncs,
        'totalSyncs': 0,
        'totalMaxSyncs': maxManualSyncs + maxAutoSyncs,
        'canManualSync': true,
        'canAutoSync': true,
        'error': e.toString(),
      };
    }
  }
  
  /// إعادة تعيين العدادات يدوياً (للاختبار)
  static Future<void> resetCountersManually() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_manualSyncCountKey, 0);
      await prefs.setInt(_autoSyncCountKey, 0);
      
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';
      await prefs.setString(_lastResetDateKey, todayString);
      
      debugPrint('🔄 [SYNC_LIMITS] تم إعادة تعيين العدادات يدوياً');
    } catch (e) {
      debugPrint('❌ [SYNC_LIMITS] خطأ في إعادة التعيين اليدوي: $e');
    }
  }
  
  /// الحصول على وقت إعادة التعيين التالي
  static String getNextResetTime() {
    final now = DateTime.now();
    final tomorrow = DateTime(now.year, now.month, now.day + 1);
    final hoursLeft = tomorrow.difference(now).inHours;
    final minutesLeft = tomorrow.difference(now).inMinutes % 60;
    
    if (hoursLeft > 0) {
      return 'إعادة تعيين خلال ${hoursLeft}س ${minutesLeft}د';
    } else {
      return 'إعادة تعيين خلال ${minutesLeft} دقيقة';
    }
  }
}
