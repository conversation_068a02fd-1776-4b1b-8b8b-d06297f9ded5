import 'package:flutter/material.dart';
import '../models/unified_account_model.dart';
import 'unified_account_status_manager.dart';

/// مدير القيود الموحد
/// يعتمد بالكامل على UnifiedAccountStatusManager
/// ويطبق منطق قرارات واضح ومتسق
class UnifiedRestrictionManager {
  static const String _tag = '[UNIFIED_RESTRICTION]';

  // تعريف الميزات المختلفة
  static const String featureBoardConnection = 'board_connection';
  static const String featureDataSync = 'data_sync';
  static const String featureSubscribers = 'subscribers';
  static const String featureDevices = 'devices';
  static const String featureBasicSettings = 'basic_settings';
  static const String featureBackup = 'backup';
  static const String featureExport = 'export';
  static const String featureMultipleBoards = 'multiple_boards';
  static const String featureAdvancedSettings = 'advanced_settings';
  static const String featureReports = 'reports';
  static const String featureAutomation = 'automation';

  /// فحص إذا كان يمكن الوصول لميزة معينة
  static bool canAccessFeature(String feature) {
    final canAccess = UnifiedAccountStatusManager.canAccessFeature(feature);

    debugPrint('$_tag فحص الوصول للميزة: $feature = $canAccess');

    return canAccess;
  }

  /// الحصول على رسالة القيد للميزة
  static String? getRestrictionMessage(String feature) {
    return UnifiedAccountStatusManager.getRestrictionMessage(feature);
  }

  /// فحص مجموعة من الميزات
  static Map<String, bool> checkMultipleFeatures(List<String> features) {
    final results = <String, bool>{};

    for (final feature in features) {
      results[feature] = canAccessFeature(feature);
    }

    debugPrint('$_tag فحص متعدد للميزات: $results');
    return results;
  }

  /// فحص الميزات الأساسية للحساب التجريبي
  static Map<String, bool> checkTrialFeatures() {
    return checkMultipleFeatures([
      featureBoardConnection,
      featureDataSync,
      featureSubscribers,
      featureDevices,
      featureBasicSettings,
    ]);
  }

  /// فحص الميزات المتقدمة
  static Map<String, bool> checkPremiumFeatures() {
    return checkMultipleFeatures([
      featureBackup,
      featureExport,
      featureMultipleBoards,
      featureAdvancedSettings,
      featureReports,
      featureAutomation,
    ]);
  }

  /// تطبيق القيود على النظام
  static Future<void> applySystemRestrictions() async {
    final status = UnifiedAccountStatusManager.currentStatus;

    if (status == null) {
      debugPrint('$_tag لا توجد بيانات حساب - تطبيق قيود كاملة');
      await _applyFullRestrictions();
      return;
    }

    debugPrint('$_tag تطبيق القيود للحساب: ${status.status.name}');

    switch (status.status) {
      case AccountStatus.active:
        await _removeAllRestrictions();
        break;

      case AccountStatus.trial:
        if (status.isTrialExpired) {
          await _applyExpiredTrialRestrictions();
        } else {
          await _applyTrialRestrictions();
        }
        break;

      case AccountStatus.expired:
        await _applyExpiredAccountRestrictions();
        break;

      case AccountStatus.banned:
      case AccountStatus.suspended:
      case AccountStatus.locked:
        await _applyFullRestrictions();
        break;

      case AccountStatus.unknown:
        await _applyFullRestrictions();
        break;
    }
  }

  /// إزالة جميع القيود
  static Future<void> _removeAllRestrictions() async {
    debugPrint('$_tag إزالة جميع القيود - حساب نشط');

    // هنا يمكن إضافة منطق لتفعيل جميع الميزات
    // مثل تفعيل المزامنة، إلغاء قيود اللوحات، إلخ
  }

  /// تطبيق قيود الحساب التجريبي
  static Future<void> _applyTrialRestrictions() async {
    debugPrint('$_tag تطبيق قيود الحساب التجريبي');

    // السماح بالميزات الأساسية فقط
    // منع الميزات المتقدمة مثل النسخ الاحتياطي والتصدير
  }

  /// تطبيق قيود الحساب التجريبي المنتهي
  static Future<void> _applyExpiredTrialRestrictions() async {
    debugPrint('$_tag تطبيق قيود الحساب التجريبي المنتهي');

    // منع معظم الميزات مع السماح بالعرض فقط
    await _disconnectAllBoards();
    await _stopSyncServices();
  }

  /// تطبيق قيود الحساب المنتهي
  static Future<void> _applyExpiredAccountRestrictions() async {
    debugPrint('$_tag تطبيق قيود الحساب المنتهي');

    // منع جميع الميزات التفاعلية
    await _disconnectAllBoards();
    await _stopSyncServices();
    await _disableDataModification();
  }

  /// تطبيق قيود كاملة
  static Future<void> _applyFullRestrictions() async {
    debugPrint('$_tag تطبيق قيود كاملة');

    // منع جميع الميزات
    await _disconnectAllBoards();
    await _stopSyncServices();
    await _disableDataModification();
    await _clearSensitiveData();
  }

  /// قطع اتصال جميع اللوحات
  static Future<void> _disconnectAllBoards() async {
    try {
      debugPrint('$_tag قطع اتصال جميع اللوحات');

      // هنا يمكن إضافة منطق قطع اتصال اللوحات
      // مثل إيقاف جميع اتصالات MikroTik
    } catch (e) {
      debugPrint('$_tag خطأ في قطع اتصال اللوحات: $e');
    }
  }

  /// إيقاف خدمات المزامنة
  static Future<void> _stopSyncServices() async {
    try {
      debugPrint('$_tag إيقاف خدمات المزامنة');

      // هنا يمكن إضافة منطق إيقاف المزامنة
      // مثل إيقاف المزامنة التلقائية مع Supabase
    } catch (e) {
      debugPrint('$_tag خطأ في إيقاف خدمات المزامنة: $e');
    }
  }

  /// تعطيل تعديل البيانات
  static Future<void> _disableDataModification() async {
    try {
      debugPrint('$_tag تعطيل تعديل البيانات');

      // هنا يمكن إضافة منطق منع تعديل البيانات
      // مثل جعل قاعدة البيانات للقراءة فقط
    } catch (e) {
      debugPrint('$_tag خطأ في تعطيل تعديل البيانات: $e');
    }
  }

  /// مسح البيانات الحساسة
  static Future<void> _clearSensitiveData() async {
    try {
      debugPrint('$_tag مسح البيانات الحساسة');

      // هنا يمكن إضافة منطق مسح البيانات الحساسة
      // مثل مسح كلمات مرور اللوحات
    } catch (e) {
      debugPrint('$_tag خطأ في مسح البيانات الحساسة: $e');
    }
  }

  /// فحص إذا كان يمكن إضافة لوحة جديدة
  static bool canAddNewBoard() {
    if (!canAccessFeature(featureBoardConnection)) {
      return false;
    }

    final status = UnifiedAccountStatusManager.currentStatus;
    if (status == null) return false;

    // الحساب التجريبي يمكنه إضافة لوحة واحدة فقط
    if (status.isTrial) {
      // هنا يمكن فحص عدد اللوحات الحالية
      // return getCurrentBoardCount() < 1;
      return true; // مؤقتاً
    }

    // الحساب النشط يمكنه إضافة عدة لوحات
    return true;
  }

  /// فحص إذا كان يمكن تصدير البيانات
  static bool canExportData() {
    return canAccessFeature(featureExport);
  }

  /// فحص إذا كان يمكن عمل نسخة احتياطية
  static bool canCreateBackup() {
    return canAccessFeature(featureBackup);
  }

  /// فحص إذا كان يمكن الوصول للإعدادات المتقدمة
  static bool canAccessAdvancedSettings() {
    return canAccessFeature(featureAdvancedSettings);
  }

  /// الحصول على ملخص القيود الحالية
  static Map<String, dynamic> getRestrictionSummary() {
    final status = UnifiedAccountStatusManager.currentStatus;

    if (status == null) {
      return {
        'accountStatus': 'unknown',
        'hasRestrictions': true,
        'allowedFeatures': <String>[],
        'restrictedFeatures': _getAllFeatures(),
        'message': 'لا توجد بيانات حساب',
      };
    }

    final allFeatures = _getAllFeatures();
    final allowedFeatures = <String>[];
    final restrictedFeatures = <String>[];

    for (final feature in allFeatures) {
      if (canAccessFeature(feature)) {
        allowedFeatures.add(feature);
      } else {
        restrictedFeatures.add(feature);
      }
    }

    return {
      'accountStatus': status.status.name,
      'accountType': status.type.name,
      'isTrial': status.isTrial,
      'trialDaysRemaining': status.trialDaysRemaining,
      'hasRestrictions': restrictedFeatures.isNotEmpty,
      'allowedFeatures': allowedFeatures,
      'restrictedFeatures': restrictedFeatures,
      'message': UnifiedAccountStatusManager.getAccountInfo()['message'],
    };
  }

  /// الحصول على قائمة جميع الميزات
  static List<String> _getAllFeatures() {
    return [
      featureBoardConnection,
      featureDataSync,
      featureSubscribers,
      featureDevices,
      featureBasicSettings,
      featureBackup,
      featureExport,
      featureMultipleBoards,
      featureAdvancedSettings,
      featureReports,
      featureAutomation,
    ];
  }

  /// إحصائيات مدير القيود
  static Map<String, dynamic> getManagerStats() {
    final summary = getRestrictionSummary();

    return {
      'totalFeatures': _getAllFeatures().length,
      'allowedFeatures': (summary['allowedFeatures'] as List).length,
      'restrictedFeatures': (summary['restrictedFeatures'] as List).length,
      'restrictionPercentage':
          ((summary['restrictedFeatures'] as List).length /
                  _getAllFeatures().length *
                  100)
              .round(),
      'accountStatus': summary['accountStatus'],
      'lastCheck': DateTime.now().toIso8601String(),
    };
  }

  /// بناء شاشة مقيدة للميزات المحظورة
  static Widget buildRestrictedScreen({
    required String title,
    required String message,
    required String actionButtonText,
    required VoidCallback onActionPressed,
  }) {
    return Scaffold(
      appBar: AppBar(title: Text(title), backgroundColor: Colors.red.shade400),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.lock_outline, size: 80, color: Colors.red.shade400),
              const SizedBox(height: 24),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: onActionPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                ),
                child: Text(
                  actionButtonText,
                  style: const TextStyle(fontSize: 16, color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
