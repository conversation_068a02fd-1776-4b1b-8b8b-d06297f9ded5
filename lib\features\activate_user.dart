import 'package:shared_preferences/shared_preferences.dart';
import '../services/account_service.dart';

Future<void> activateUser({
  required String userId,
  required String type,
  required int amount,
  required String label,
}) async {
  // تحديد مدة التفعيل حسب النوع
  int days = 30;
  if (type == '3months') days = 90;
  if (type == '6months') days = 180;
  if (type == 'year') days = 365;

  // تفعيل الحساب باستخدام AccountService
  final success = await AccountService.activateAccount(userId, label, days);

  if (success) {
    // تحديث القيم محلياً
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isTrial', false);
  } else {
    throw Exception('فشل في تفعيل الحساب');
  }
}
