# 🔐 دليل استعادة وتحديث كلمة المرور

## 🎯 **الميزات المضافة:**

### **1️⃣ استعادة كلمة المرور (نسيت كلمة المرور):**
- شاشة مخصصة لإرسال رابط الاستعادة
- إرسال رابط عبر البريد الإلكتروني
- رسائل خطأ واضحة ومفيدة
- واجهة مستخدم عصرية

### **2️⃣ تحديث كلمة المرور (للمستخدم المسجل دخول):**
- شاشة آمنة لتحديث كلمة المرور
- تأكيد كلمة المرور الجديدة
- فحص صحة الجلسة قبل التحديث
- إخفاء/إظهار كلمة المرور

### **3️⃣ التكامل مع التطبيق:**
- زر "نسيت كلمة المرور" في شاشة تسجيل الدخول
- خيار "تحديث كلمة المرور" في الإعدادات
- فحص صحة الجلسة تلقائياً

## 🔧 **الملفات المضافة/المحدثة:**

### **الملفات الجديدة:**
1. **`lib/forgot_password_screen.dart`** - شاشة استعادة كلمة المرور
2. **`lib/update_password_screen.dart`** - شاشة تحديث كلمة المرور

### **الملفات المحدثة:**
1. **`lib/supabase_login_screen.dart`** - إضافة زر "نسيت كلمة المرور"
2. **`lib/services/supabase_auth_service.dart`** - إضافة دوال جديدة
3. **`lib/settings_page.dart`** - إضافة خيار تحديث كلمة المرور

## 🚀 **كيفية الاستخدام:**

### **📧 استعادة كلمة المرور:**

#### **للمستخدم:**
1. في شاشة تسجيل الدخول، اضغط **"نسيت كلمة المرور؟"**
2. أدخل بريدك الإلكتروني
3. اضغط **"إرسال رابط الاستعادة"**
4. افحص بريدك الإلكتروني (والبريد المهمل)
5. اضغط على الرابط في البريد
6. أدخل كلمة المرور الجديدة

#### **للمطور:**
```dart
// في ForgotPasswordScreen
await _authService.resetPassword(_emailController.text.trim());
```

### **🔒 تحديث كلمة المرور:**

#### **للمستخدم:**
1. اذهب إلى **الإعدادات**
2. اضغط على **"تحديث كلمة المرور"**
3. أدخل كلمة المرور الجديدة
4. أكد كلمة المرور
5. اضغط **"تحديث كلمة المرور"**

#### **للمطور:**
```dart
// في UpdatePasswordScreen
await _authService.updatePassword(_newPasswordController.text);
```

## 🛡️ **الأمان والحماية:**

### **1️⃣ فحص الجلسة:**
```dart
// التحقق من صحة الجلسة قبل تحديث كلمة المرور
final isSessionValid = await authService.isSessionValid();
if (!isSessionValid) {
  // إظهار رسالة خطأ
  return;
}
```

### **2️⃣ التحقق من صحة البيانات:**
```dart
// في ForgotPasswordScreen
if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
  return 'يرجى إدخال بريد إلكتروني صحيح';
}

// في UpdatePasswordScreen
if (value.length < 6) {
  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
}
if (value != _newPasswordController.text) {
  return 'كلمة المرور غير متطابقة';
}
```

### **3️⃣ معالجة الأخطاء:**
```dart
String _getErrorMessage(String error) {
  if (error.contains('Invalid email')) {
    return 'البريد الإلكتروني غير صحيح';
  } else if (error.contains('User not found')) {
    return 'لا يوجد حساب مرتبط بهذا البريد الإلكتروني';
  } else if (error.contains('Too many requests')) {
    return 'تم إرسال عدد كبير من الطلبات. يرجى المحاولة لاحقاً';
  }
  // ... المزيد من الأخطاء
}
```

## 🎨 **واجهة المستخدم:**

### **🎯 التصميم العصري:**
- **أيقونات واضحة** لكل وظيفة
- **ألوان متناسقة** مع ثيم التطبيق
- **رسائل واضحة** للنجاح والفشل
- **تحميل مرئي** أثناء العمليات

### **📱 تجربة مستخدم محسنة:**
- **إخفاء/إظهار كلمة المرور**
- **التحقق الفوري** من صحة البيانات
- **رسائل خطأ مفيدة**
- **انتقال سلس** بين الشاشات

## 🔧 **الدوال المضافة في AuthService:**

### **1️⃣ استعادة كلمة المرور:**
```dart
Future<void> resetPassword(String email) async {
  try {
    await _supabase.auth.resetPasswordForEmail(email);
  } catch (e) {
    rethrow;
  }
}
```

### **2️⃣ تحديث كلمة المرور:**
```dart
Future<UserResponse> updatePassword(String newPassword) async {
  try {
    final response = await _supabase.auth.updateUser(
      UserAttributes(password: newPassword),
    );
    return response;
  } catch (e) {
    rethrow;
  }
}
```

### **3️⃣ فحص صحة الجلسة:**
```dart
Future<bool> isSessionValid() async {
  try {
    final session = _supabase.auth.currentSession;
    if (session == null) return false;
    
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
    return DateTime.now().isBefore(expiresAt);
  } catch (e) {
    return false;
  }
}
```

## 🧪 **اختبار الميزات:**

### **1️⃣ اختبار استعادة كلمة المرور:**
```bash
flutter run
# 1. اذهب لشاشة تسجيل الدخول
# 2. اضغط "نسيت كلمة المرور؟"
# 3. أدخل بريد إلكتروني صحيح
# 4. اضغط "إرسال رابط الاستعادة"
# 5. تحقق من البريد الإلكتروني
```

### **2️⃣ اختبار تحديث كلمة المرور:**
```bash
# 1. سجل دخول للتطبيق
# 2. اذهب للإعدادات
# 3. اضغط "تحديث كلمة المرور"
# 4. أدخل كلمة مرور جديدة
# 5. أكد كلمة المرور
# 6. اضغط "تحديث كلمة المرور"
```

## 📊 **الحالات المختلفة:**

### **✅ حالات النجاح:**
- إرسال رابط الاستعادة بنجاح
- تحديث كلمة المرور بنجاح
- فحص صحة الجلسة

### **❌ حالات الفشل:**
- بريد إلكتروني غير صحيح
- مستخدم غير موجود
- كلمة مرور ضعيفة
- انتهاء صلاحية الجلسة
- مشاكل الشبكة

### **⚠️ حالات خاصة:**
- طلبات كثيرة (Rate Limiting)
- جلسة منتهية الصلاحية
- مشاكل في الخادم

## 🎉 **النتيجة النهائية:**

### **✅ ميزات مكتملة:**
- 🔐 **استعادة كلمة المرور** عبر البريد الإلكتروني
- 🔒 **تحديث كلمة المرور** للمستخدم المسجل دخول
- 🛡️ **فحص الأمان** وصحة الجلسة
- 🎨 **واجهة عصرية** وسهلة الاستخدام
- 📱 **تجربة مستخدم ممتازة**

### **🚀 جاهز للاستخدام:**
- **للمطورين**: دوال جاهزة وموثقة
- **للمستخدمين**: واجهات بسيطة وواضحة
- **للأمان**: فحص شامل وحماية قوية

**🎯 نظام استعادة وتحديث كلمة المرور مكتمل واحترافي!**

---

## 📞 **للدعم:**

إذا واجهت مشاكل:
1. **تحقق من إعدادات Supabase** - Email templates
2. **تحقق من البريد المهمل** - قد يكون الرابط هناك
3. **تحقق من صحة الجلسة** - قد تحتاج تسجيل دخول جديد
4. **تحقق من الشبكة** - تأكد من الاتصال بالإنترنت

**🚀 استمتع بالميزات الجديدة!**
