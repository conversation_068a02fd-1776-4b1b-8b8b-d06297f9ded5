import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/managers/internet_status_manager.dart';

/// خدمة إعداد قاعدة البيانات للتحديثات
///
/// ⚠️ ملاحظة مهمة: هذه الخدمة تتطلب إعداد يدوي لقاعدة البيانات
/// يرجى تنفيذ الكود الموجود في ملف supabase_update_system.sql
/// في Supabase Dashboard > SQL Editor قبل استخدام هذه الخدمة
class DatabaseSetupService {
  /// إنشاء جداول نظام التحديثات
  static Future<bool> setupUpdateTables() async {
    try {
      debugPrint('🔧 [DB_SETUP] إنشاء جداول نظام التحديثات...');

      // إصلاح مشكلة العمود is_trial أولاً
      await _fixIsTrialColumn();

      // فحص إذا كانت الجداول موجودة
      final tablesExist = await _checkTablesExist();
      if (tablesExist) {
        debugPrint('✅ [DB_SETUP] جداول التحديثات موجودة بالفعل');
        return true;
      }

      // إظهار رسالة للمطور
      _showSetupInstructions();

      // فحص الجداول
      await _createAppUpdatesTable();
      await _createUserUpdateHistoryTable();
      await _createUserUpdatePreferencesTable();
      await _createUpdateStatisticsTable();

      // فحص الدوال
      await _createUpdateFunctions();

      // فحص السياسات الأمنية
      await _createSecurityPolicies();

      debugPrint('✅ [DB_SETUP] تم فحص جداول نظام التحديثات');
      return true;
    } catch (e) {
      debugPrint('❌ [DB_SETUP] خطأ في إعداد جداول التحديثات: $e');
      _showSetupInstructions();
      return false;
    }
  }

  /// فحص إذا كان الخطأ متعلق بالاتصال
  static bool _isConnectionError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('failed host lookup') ||
        errorString.contains('socketexception') ||
        errorString.contains('no address associated with hostname') ||
        errorString.contains('network is unreachable');
  }

  /// إظهار تعليمات الإعداد للمطور
  static void _showSetupInstructions() {
    debugPrint('');
    debugPrint(
      '📋 ═══════════════════════════════════════════════════════════',
    );
    debugPrint('📋 تعليمات إعداد نظام التحديثات:');
    debugPrint(
      '📋 ═══════════════════════════════════════════════════════════',
    );
    debugPrint('📋 1. افتح Supabase Dashboard');
    debugPrint('📋 2. اذهب إلى SQL Editor');
    debugPrint('📋 3. انسخ والصق محتوى ملف supabase_update_system.sql');
    debugPrint('📋 4. اضغط RUN لتنفيذ الكود');
    debugPrint('📋 5. أعد تشغيل التطبيق');
    debugPrint(
      '📋 ═══════════════════════════════════════════════════════════',
    );
    debugPrint('');
  }

  /// إصلاح مشكلة العمود is_trial المفقود
  static Future<void> _fixIsTrialColumn() async {
    try {
      if (!InternetStatusManager.isConnected) {
        debugPrint(
          '🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص العمود is_trial',
        );
        return;
      }

      debugPrint('🔧 [DB_SETUP] فحص وإصلاح العمود is_trial...');

      // محاولة فحص وجود الأعمدة عبر استعلام بسيط
      try {
        await Supabase.instance.client
            .from('user_accounts')
            .select('is_trial, trial_days, expiry_millis')
            .limit(1);
        debugPrint('✅ [DB_SETUP] جميع الأعمدة المطلوبة موجودة');
      } catch (e) {
        if (_isConnectionError(e)) {
          debugPrint(
            '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي فحص الأعمدة',
          );
          return;
        }
        debugPrint(
          '⚠️ [DB_SETUP] بعض الأعمدة مفقودة، سيتم تجاهل هذا الخطأ: $e',
        );
        // في حالة عدم وجود الأعمدة، سيتم التعامل مع هذا في الكود
      }

      debugPrint('✅ [DB_SETUP] تم فحص وإصلاح الأعمدة المطلوبة');
    } catch (e) {
      debugPrint('⚠️ [DB_SETUP] خطأ في إصلاح العمود is_trial: $e');
      // لا نرمي خطأ هنا لأن هذا قد يكون طبيعي في بعض الحالات
    }
  }

  /// فحص وجود الجداول
  static Future<bool> _checkTablesExist() async {
    try {
      // محاولة الاستعلام من جدول app_updates
      await Supabase.instance.client.from('app_updates').select('id').limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// إنشاء جدول التحديثات الرئيسي
  static Future<void> _createAppUpdatesTable() async {
    try {
      // فحص الاتصال بالإنترنت أولاً
      if (!InternetStatusManager.isConnected) {
        debugPrint(
          '🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص جدول app_updates',
        );
        return;
      }

      debugPrint('🔧 [DB_SETUP] محاولة إنشاء جدول app_updates...');

      // بدلاً من إنشاء الجدول برمجياً، سنتحقق من وجوده فقط
      await Supabase.instance.client.from('app_updates').select('id').limit(1);
      debugPrint('✅ [DB_SETUP] جدول app_updates موجود بالفعل');
    } catch (e) {
      if (_isConnectionError(e)) {
        debugPrint(
          '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي فحص الجداول',
        );
      } else {
        debugPrint(
          '⚠️ [DB_SETUP] جدول app_updates غير موجود أو لا يمكن الوصول إليه: $e',
        );
        debugPrint('📋 يرجى إنشاء الجدول يدوياً في Supabase Dashboard');
      }
    }
  }

  /// إنشاء جدول سجل تحديثات المستخدمين
  static Future<void> _createUserUpdateHistoryTable() async {
    try {
      if (!InternetStatusManager.isConnected) {
        debugPrint(
          '🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص جدول user_update_history',
        );
        return;
      }

      debugPrint('🔧 [DB_SETUP] فحص جدول user_update_history...');
      await Supabase.instance.client
          .from('user_update_history')
          .select('id')
          .limit(1);
      debugPrint('✅ [DB_SETUP] جدول user_update_history موجود بالفعل');
    } catch (e) {
      if (_isConnectionError(e)) {
        debugPrint(
          '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي فحص الجداول',
        );
      } else {
        debugPrint('⚠️ [DB_SETUP] جدول user_update_history غير موجود: $e');
        debugPrint('📋 يرجى إنشاء الجدول يدوياً في Supabase Dashboard');
      }
    }
  }

  /// إنشاء جدول تفضيلات التحديث
  static Future<void> _createUserUpdatePreferencesTable() async {
    try {
      if (!InternetStatusManager.isConnected) {
        debugPrint(
          '🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص جدول user_update_preferences',
        );
        return;
      }

      debugPrint('🔧 [DB_SETUP] فحص جدول user_update_preferences...');
      await Supabase.instance.client
          .from('user_update_preferences')
          .select('user_id')
          .limit(1);
      debugPrint('✅ [DB_SETUP] جدول user_update_preferences موجود بالفعل');
    } catch (e) {
      if (_isConnectionError(e)) {
        debugPrint(
          '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي فحص الجداول',
        );
      } else {
        debugPrint('⚠️ [DB_SETUP] جدول user_update_preferences غير موجود: $e');
        debugPrint('📋 يرجى إنشاء الجدول يدوياً في Supabase Dashboard');
      }
    }
  }

  /// إنشاء جدول إحصائيات التحديثات
  static Future<void> _createUpdateStatisticsTable() async {
    try {
      if (!InternetStatusManager.isConnected) {
        debugPrint(
          '🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص جدول update_statistics',
        );
        return;
      }

      debugPrint('🔧 [DB_SETUP] فحص جدول update_statistics...');
      await Supabase.instance.client
          .from('update_statistics')
          .select('id')
          .limit(1);
      debugPrint('✅ [DB_SETUP] جدول update_statistics موجود بالفعل');
    } catch (e) {
      if (_isConnectionError(e)) {
        debugPrint(
          '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي فحص الجداول',
        );
      } else {
        debugPrint('⚠️ [DB_SETUP] جدول update_statistics غير موجود: $e');
        debugPrint('📋 يرجى إنشاء الجدول يدوياً في Supabase Dashboard');
      }
    }
  }

  /// إنشاء الدوال المساعدة
  static Future<void> _createUpdateFunctions() async {
    try {
      if (!InternetStatusManager.isConnected) {
        debugPrint('🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص الدوال');
        return;
      }

      debugPrint('🔧 [DB_SETUP] فحص دالة get_latest_update...');
      // محاولة استدعاء الدالة للتحقق من وجودها
      await Supabase.instance.client.rpc(
        'get_latest_update',
        params: {'user_platform': 'android', 'current_build_number': 0},
      );
      debugPrint('✅ [DB_SETUP] دالة get_latest_update موجودة وتعمل');
    } catch (e) {
      if (_isConnectionError(e)) {
        debugPrint(
          '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي فحص الدوال',
        );
      } else {
        debugPrint(
          '⚠️ [DB_SETUP] دالة get_latest_update غير موجودة أو لا تعمل: $e',
        );
        debugPrint('📋 يرجى إنشاء الدالة يدوياً في Supabase Dashboard');
      }
    }
  }

  /// إنشاء السياسات الأمنية
  static Future<void> _createSecurityPolicies() async {
    try {
      if (!InternetStatusManager.isConnected) {
        debugPrint(
          '🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص السياسات الأمنية',
        );
        return;
      }

      debugPrint('🔧 [DB_SETUP] فحص السياسات الأمنية...');
      // محاولة الوصول للجداول للتحقق من وجود السياسات
      await Supabase.instance.client.from('app_updates').select('id').limit(1);
      debugPrint('✅ [DB_SETUP] السياسات الأمنية تعمل بشكل صحيح');
    } catch (e) {
      if (_isConnectionError(e)) {
        debugPrint(
          '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي فحص السياسات',
        );
      } else {
        debugPrint('⚠️ [DB_SETUP] مشكلة في السياسات الأمنية: $e');
        debugPrint(
          '📋 يرجى إعداد السياسات الأمنية يدوياً في Supabase Dashboard',
        );
      }
    }
  }

  /// إدراج تحديث تجريبي للاختبار
  static Future<void> insertSampleUpdate() async {
    try {
      if (!InternetStatusManager.isConnected) {
        debugPrint(
          '🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي إدراج التحديث التجريبي',
        );
        return;
      }

      final sampleUpdate = {
        'version': '1.1.0',
        'build_number': 2,
        'platform': 'android',
        'is_active': true,
        'is_forced': false,
        'min_compatible_version': '1.0.0',
        'database_migration_required': false,
        'release_notes': 'تحديث تجريبي مع تحسينات في الأداء وإصلاحات للأخطاء',
        'download_url': 'https://example.com/itower-v1.1.0.apk',
        'features': [
          'تحسين واجهة المستخدم',
          'إضافة ميزة البحث المتقدم',
          'تحسين سرعة التطبيق',
        ],
        'bug_fixes': [
          'إصلاح مشكلة تسجيل الدخول',
          'إصلاح مشكلة المزامنة',
          'إصلاح مشاكل الأداء',
        ],
        'security_updates': ['تحسين أمان البيانات', 'إصلاح ثغرات أمنية'],
        'published_at': DateTime.now().toIso8601String(),
      };

      await Supabase.instance.client.from('app_updates').insert(sampleUpdate);

      debugPrint('✅ [DB_SETUP] تم إدراج تحديث تجريبي');
    } catch (e) {
      if (_isConnectionError(e)) {
        debugPrint(
          '🌐 [DB_SETUP] مشكلة في الاتصال بالإنترنت - تخطي إدراج التحديث',
        );
      } else {
        debugPrint(
          '⚠️ [DB_SETUP] خطأ في إدراج التحديث التجريبي (قد يكون موجود): $e',
        );
      }
    }
  }

  /// فحص صحة إعداد قاعدة البيانات
  static Future<Map<String, bool?>> validateSetup() async {
    final results = <String, bool?>{};

    // فحص الاتصال بالإنترنت أولاً
    final hasConnection = InternetStatusManager.isConnected;
    results['internet_connection'] = hasConnection;

    if (!hasConnection) {
      debugPrint('🌐 [DB_SETUP] لا يوجد اتصال بالإنترنت - تخطي فحص الإعداد');
      // تعيين جميع النتائج كـ null لتمييزها عن false
      results['app_updates_table'] = null;
      results['user_update_history_table'] = null;
      results['user_update_preferences_table'] = null;
      results['update_statistics_table'] = null;
      results['get_latest_update_function'] = null;
      return results;
    }

    try {
      // فحص جدول app_updates
      await Supabase.instance.client.from('app_updates').select('id').limit(1);
      results['app_updates_table'] = true;
    } catch (e) {
      results['app_updates_table'] = _isConnectionError(e) ? null : false;
    }

    try {
      // فحص جدول user_update_history
      await Supabase.instance.client
          .from('user_update_history')
          .select('id')
          .limit(1);
      results['user_update_history_table'] = true;
    } catch (e) {
      results['user_update_history_table'] = _isConnectionError(e)
          ? null
          : false;
    }

    try {
      // فحص جدول user_update_preferences
      await Supabase.instance.client
          .from('user_update_preferences')
          .select('user_id')
          .limit(1);
      results['user_update_preferences_table'] = true;
    } catch (e) {
      results['user_update_preferences_table'] = _isConnectionError(e)
          ? null
          : false;
    }

    try {
      // فحص جدول update_statistics
      await Supabase.instance.client
          .from('update_statistics')
          .select('id')
          .limit(1);
      results['update_statistics_table'] = true;
    } catch (e) {
      results['update_statistics_table'] = _isConnectionError(e) ? null : false;
    }

    try {
      // فحص دالة get_latest_update
      await Supabase.instance.client.rpc(
        'get_latest_update',
        params: {'user_platform': 'android', 'current_build_number': 0},
      );
      results['get_latest_update_function'] = true;
    } catch (e) {
      results['get_latest_update_function'] = _isConnectionError(e)
          ? null
          : false;
    }

    return results;
  }
}
