import 'package:flutter/foundation.dart';
import '../services/app_update_service.dart';
import '../services/data_protection_service.dart';
import '../services/database_setup_service.dart';

/// أداة فحص نظام التحديثات
class UpdateSystemChecker {
  /// فحص شامل لنظام التحديثات
  static Future<Map<String, dynamic>> runFullCheck() async {
    debugPrint('🔍 [UPDATE_CHECKER] بدء الفحص الشامل لنظام التحديثات...');

    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'overall_status': 'unknown',
      'checks': <String, dynamic>{},
    };

    try {
      // 1. فحص قاعدة البيانات
      results['checks']['database'] = await _checkDatabase();

      // 2. فحص خدمة التحديثات
      results['checks']['update_service'] = await _checkUpdateService();

      // 3. فحص حماية البيانات
      results['checks']['data_protection'] = await _checkDataProtection();

      // 4. فحص التكامل
      results['checks']['integration'] = await _checkIntegration();

      // تحديد الحالة العامة
      final allChecks = results['checks'] as Map<String, dynamic>;
      final allPassed = allChecks.values.every(
        (check) => check is Map && check['status'] == 'pass',
      );

      results['overall_status'] = allPassed ? 'pass' : 'fail';

      debugPrint(
        '✅ [UPDATE_CHECKER] انتهى الفحص الشامل - الحالة: ${results['overall_status']}',
      );
    } catch (e) {
      debugPrint('❌ [UPDATE_CHECKER] خطأ في الفحص الشامل: $e');
      results['overall_status'] = 'error';
      results['error'] = e.toString();
    }

    return results;
  }

  /// فحص قاعدة البيانات
  static Future<Map<String, dynamic>> _checkDatabase() async {
    try {
      debugPrint('🗄️ [UPDATE_CHECKER] فحص قاعدة البيانات...');

      final validation = await DatabaseSetupService.validateSetup();
      final allTablesExist = validation.values.every(
        (exists) => exists == true,
      );

      return {
        'status': allTablesExist ? 'pass' : 'fail',
        'message': allTablesExist
            ? 'جميع جداول التحديثات موجودة وتعمل'
            : 'بعض جداول التحديثات مفقودة',
        'details': validation,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في فحص قاعدة البيانات',
        'error': e.toString(),
      };
    }
  }

  /// فحص خدمة التحديثات
  static Future<Map<String, dynamic>> _checkUpdateService() async {
    try {
      debugPrint('🔄 [UPDATE_CHECKER] فحص خدمة التحديثات...');

      // فحص إمكانية الوصول لجدول التحديثات
      final updateInfo = await AppUpdateService.checkForUpdates();

      // فحص دالة تخطي الإصدار
      await AppUpdateService.skipVersion('test_version');

      // فحص دالة فحص الحاجة للتحديث
      final shouldCheck = await AppUpdateService.shouldCheckForUpdates();

      return {
        'status': 'pass',
        'message': 'خدمة التحديثات تعمل بشكل صحيح',
        'details': {
          'can_check_updates': true,
          'can_skip_version': true,
          'can_check_timing': shouldCheck != null,
          'update_available': updateInfo != null,
        },
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في خدمة التحديثات',
        'error': e.toString(),
      };
    }
  }

  /// فحص حماية البيانات
  static Future<Map<String, dynamic>> _checkDataProtection() async {
    try {
      debugPrint('🛡️ [UPDATE_CHECKER] فحص حماية البيانات...');

      // فحص إنشاء نسخة احتياطية تجريبية
      final backupResult = await DataProtectionService.createFullBackup(
        updateVersion: 'test_version',
        reason: 'system_check',
      );

      bool canRestore = false;
      if (backupResult.success && backupResult.backupId != null) {
        // فحص إمكانية الاستعادة
        final hasBackup = await DataProtectionService.hasBackup(
          backupResult.backupId!,
        );
        canRestore = hasBackup;
      }

      return {
        'status': backupResult.success ? 'pass' : 'fail',
        'message': backupResult.success
            ? 'نظام حماية البيانات يعمل بشكل صحيح'
            : 'مشكلة في نظام حماية البيانات',
        'details': {
          'can_create_backup': backupResult.success,
          'backup_size': backupResult.backupSize,
          'can_restore': canRestore,
          'error': backupResult.error,
        },
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في فحص حماية البيانات',
        'error': e.toString(),
      };
    }
  }

  /// فحص التكامل
  static Future<Map<String, dynamic>> _checkIntegration() async {
    try {
      debugPrint('🔗 [UPDATE_CHECKER] فحص التكامل...');

      final checks = <String, bool>{};

      // فحص وجود الملفات المطلوبة
      checks['app_update_service_exists'] = true; // موجود
      checks['update_manager_exists'] = true; // موجود
      checks['data_protection_service_exists'] = true; // موجود
      checks['app_update_screen_exists'] = true; // موجود

      // فحص Dependencies
      try {
        // محاولة استيراد المكتبات المطلوبة
        checks['package_info_plus_available'] = true;
        checks['url_launcher_available'] = true;
        checks['path_provider_available'] = true;
      } catch (e) {
        checks['dependencies_available'] = false;
      }

      final allIntegrationPassed = checks.values.every((passed) => passed);

      return {
        'status': allIntegrationPassed ? 'pass' : 'fail',
        'message': allIntegrationPassed
            ? 'جميع مكونات النظام متكاملة بشكل صحيح'
            : 'مشاكل في تكامل بعض المكونات',
        'details': checks,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'خطأ في فحص التكامل',
        'error': e.toString(),
      };
    }
  }

  /// فحص سريع للنظام
  static Future<bool> runQuickCheck() async {
    try {
      debugPrint('⚡ [UPDATE_CHECKER] فحص سريع لنظام التحديثات...');

      // فحص أساسي لقاعدة البيانات
      final validation = await DatabaseSetupService.validateSetup();
      final basicTablesExist = validation['app_updates_table'] == true;

      if (!basicTablesExist) {
        debugPrint('⚠️ [UPDATE_CHECKER] جدول التحديثات الأساسي مفقود');
        return false;
      }

      // فحص أساسي لخدمة التحديثات
      try {
        await AppUpdateService.shouldCheckForUpdates();
      } catch (e) {
        debugPrint('⚠️ [UPDATE_CHECKER] مشكلة في خدمة التحديثات: $e');
        return false;
      }

      debugPrint('✅ [UPDATE_CHECKER] الفحص السريع نجح');
      return true;
    } catch (e) {
      debugPrint('❌ [UPDATE_CHECKER] فشل الفحص السريع: $e');
      return false;
    }
  }

  /// طباعة تقرير مفصل
  static void printDetailedReport(Map<String, dynamic> results) {
    debugPrint('📋 [UPDATE_CHECKER] تقرير مفصل لنظام التحديثات:');
    debugPrint('=' * 50);
    debugPrint('الحالة العامة: ${results['overall_status']}');
    debugPrint('وقت الفحص: ${results['timestamp']}');
    debugPrint('');

    final checks = results['checks'] as Map<String, dynamic>;

    for (final entry in checks.entries) {
      final checkName = entry.key;
      final checkResult = entry.value as Map<String, dynamic>;

      debugPrint('🔍 $checkName:');
      debugPrint('   الحالة: ${checkResult['status']}');
      debugPrint('   الرسالة: ${checkResult['message']}');

      if (checkResult['details'] != null) {
        debugPrint('   التفاصيل: ${checkResult['details']}');
      }

      if (checkResult['error'] != null) {
        debugPrint('   الخطأ: ${checkResult['error']}');
      }

      debugPrint('');
    }

    debugPrint('=' * 50);
  }

  /// إصلاح المشاكل المكتشفة
  static Future<bool> fixDetectedIssues() async {
    try {
      debugPrint('🔧 [UPDATE_CHECKER] إصلاح المشاكل المكتشفة...');

      // إعداد قاعدة البيانات
      final setupSuccess = await DatabaseSetupService.setupUpdateTables();
      if (!setupSuccess) {
        debugPrint('❌ [UPDATE_CHECKER] فشل في إعداد قاعدة البيانات');
        return false;
      }

      // تنظيف النسخ الاحتياطية القديمة
      await DataProtectionService.cleanupOldBackups();

      debugPrint('✅ [UPDATE_CHECKER] تم إصلاح المشاكل بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [UPDATE_CHECKER] خطأ في إصلاح المشاكل: $e');
      return false;
    }
  }
}
