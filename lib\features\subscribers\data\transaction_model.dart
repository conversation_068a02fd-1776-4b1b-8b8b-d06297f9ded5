// نموذج بيانات المعاملة

enum TransactionType {
  renewal, // تجديد
  payDebt, // تسديد دين
  addDebt, // إضافة دين
  addCredit, // إضافة رصيد
}

class Transaction {
  int? id;
  TransactionType type;
  String description;
  DateTime date;
  int? subscriberId;

  Transaction({
    this.id,
    required this.type,
    required this.description,
    required this.date,
    this.subscriberId,
  });

  factory Transaction.fromMap(Map<String, dynamic> map) {
    // تحويل القيم القديمة إلى الأنواع الجديدة
    TransactionType type;
    final typeIndex = map['type'] as int;

    switch (typeIndex) {
      case 0: // renewal (تجديد اشتراك)
        type = TransactionType.renewal;
        break;
      case 1: // payDebt (تسديد دين)
        type = TransactionType.payDebt;
        break;
      case 2: // addDebt (إضافة دين)
        type = TransactionType.addDebt;
        break;
      case 3: // addCredit (إضافة رصيد)
        type = TransactionType.addCredit;
        break;
      case 4: // other -> renewal (للعمليات القديمة)
        type = TransactionType.renewal;
        break;
      default:
        type = TransactionType.renewal; // القيمة الافتراضية
    }

    return Transaction(
      id: map['id'],
      type: type,
      description: map['description'],
      date: DateTime.parse(map['date']),
      subscriberId: map['subscriberId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.index,
      'description': description,
      'date': date.toIso8601String(),
      'subscriberId': subscriberId,
    };
  }
}
