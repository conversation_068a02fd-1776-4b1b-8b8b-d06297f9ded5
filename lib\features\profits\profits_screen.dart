import 'package:flutter/material.dart';
import '../../db_helper.dart';
import '../subscribers/data/transaction_model.dart';

class ProfitsScreen extends StatefulWidget {
  const ProfitsScreen({Key? key}) : super(key: key);

  @override
  State<ProfitsScreen> createState() => _ProfitsScreenState();
}

class _ProfitsScreenState extends State<ProfitsScreen> {
  int totalIncome = 0;
  int totalExpense = 0;
  int netProfit = 0;
  int activationsThisMonth = 0;
  bool loading = true;

  // إضافة متغير لتحديد نوع الملخص
  String _summaryType = 'شهري'; // الخيارات: يومي، اسبوعي، شهري

  @override
  void initState() {
    super.initState();
    _loadProfits();
  }

  // --- منطق أرباح شامل وذكي: يعتمد على كل معاملة مالية حقيقية ---
  Future<void> _loadProfits() async {
    setState(() {
      loading = true;
    });
    final transactions = await DBHelper.instance.getAllTransactions();
    final subscribers = await DBHelper.instance.getAllSubscribers();
    final subscriptionsRaw = await DBHelper.instance.getAllSubscriptions();
    final now = DateTime.now();
    final range = _getSummaryRange(now);
    int income = 0;
    int expense = 0;
    int activations = 0;

    final subscriberMap = {for (var s in subscribers) s.id: s};
    final subscriptionMap = {for (var sub in subscriptionsRaw) sub['id']: sub};

    for (final t in transactions) {
      if (t.date.isBefore(range.start) || t.date.isAfter(range.end)) continue;
      final subscriber = t.subscriberId != null
          ? subscriberMap[t.subscriberId]
          : null;
      final subscription =
          (subscriber != null && subscriber.subscriptionId != null)
          ? subscriptionMap[subscriber.subscriptionId]
          : null;
      final sellPrice = subscription != null
          ? _getEffectiveSellPrice(subscription)
          : (subscriber != null ? (subscriber.subscriptionPrice) : 0.0);
      // استخدام السعر المخصص إذا كان موجوداً، وإلا السعر الافتراضي
      final buyPrice = subscription != null
          ? _getEffectiveBuyPrice(subscription)
          : (subscriber != null ? (subscriber.buyPrice ?? 0.0) : 0.0);
      final desc = t.description.toLowerCase();

      // الدخل: عمليات التسديد والتجديد
      if ((t.type == TransactionType.payDebt ||
              t.type == TransactionType.renewal) &&
          sellPrice > 0) {
        income += sellPrice.round();
      }

      // المصروفات: عمليات التجديد وإضافة الديون
      if ((t.type == TransactionType.renewal ||
              t.type == TransactionType.addDebt) &&
          buyPrice > 0) {
        expense += buyPrice.round();
      }

      // عدد التفعيلات خلال الفترة المختارة: عمليات التجديد فقط
      if (t.type == TransactionType.renewal &&
          !t.date.isBefore(range.start) &&
          !t.date.isAfter(range.end)) {
        activations++;
      }
    }
    setState(() {
      totalIncome = income;
      totalExpense = expense;
      netProfit = income - expense;
      activationsThisMonth = activations;
      loading = false;
    });
  }

  // دالة لحساب بداية ونهاية الفترة حسب نوع الملخص
  DateTimeRange _getSummaryRange(DateTime now) {
    if (_summaryType == 'يومي') {
      return DateTimeRange(
        start: DateTime(now.year, now.month, now.day),
        end: DateTime(now.year, now.month, now.day, 23, 59, 59),
      );
    } else if (_summaryType == 'اسبوعي') {
      final start = now.subtract(Duration(days: now.weekday - 1));
      final end = start.add(
        const Duration(days: 6, hours: 23, minutes: 59, seconds: 59),
      );
      return DateTimeRange(
        start: DateTime(start.year, start.month, start.day),
        end: end,
      );
    } else {
      // شهري
      final start = DateTime(now.year, now.month, 1);
      final end = DateTime(
        now.year,
        now.month + 1,
        1,
      ).subtract(const Duration(seconds: 1));
      return DateTimeRange(start: start, end: end);
    }
  }

  // الحصول على سعر البيع الفعال (مخصص أو افتراضي)
  double _getEffectiveSellPrice(Map<String, dynamic> subscription) {
    final customPrice = subscription['custom_sell_price'];
    if (customPrice != null && customPrice > 0) {
      return (customPrice is num)
          ? customPrice.toDouble()
          : double.tryParse(customPrice.toString()) ?? 0.0;
    }

    final sellPrice = subscription['sellPrice'];
    if (sellPrice != null) {
      return (sellPrice is num)
          ? sellPrice.toDouble()
          : double.tryParse(sellPrice.toString()) ?? 0.0;
    }

    return 0.0;
  }

  // الحصول على سعر الشراء الفعال (مخصص أو افتراضي)
  double _getEffectiveBuyPrice(Map<String, dynamic> subscription) {
    final customPrice = subscription['custom_buy_price'];
    if (customPrice != null && customPrice > 0) {
      return (customPrice is num)
          ? customPrice.toDouble()
          : double.tryParse(customPrice.toString()) ?? 0.0;
    }

    final buyPrice = subscription['buyPrice'];
    if (buyPrice != null) {
      return (buyPrice is num)
          ? buyPrice.toDouble()
          : double.tryParse(buyPrice.toString()) ?? 0.0;
    }

    return 0.0;
  }

  // واجهة اختيار نوع الملخص (محفوظة للتوافق)
  Widget _buildSummarySelector() {
    return _buildModernSummarySelector(
      Theme.of(context).colorScheme,
      Theme.of(context).brightness == Brightness.dark,
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // محتوى الشاشة
                  loading
                      ? _buildLoadingState(colorScheme)
                      : _buildProfitsContent(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.18),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withValues(
              alpha: isDark ? 0.08 : 0.18,
            ),
            child: Icon(Icons.analytics, color: colorScheme.primary, size: 54),
          ),
        ),
        // عنوان الشاشة
        Text(
          'الأرباح والإحصائيات',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'تتبع الأرباح والمصروفات والإحصائيات',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء حالة التحميل
  Widget _buildLoadingState(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: 0.7),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: const Padding(
        padding: EdgeInsets.all(64),
        child: Center(child: CircularProgressIndicator()),
      ),
    );
  }

  // بناء محتوى الأرباح
  Widget _buildProfitsContent(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // محدد نوع الملخص
        _buildModernSummarySelector(colorScheme, isDark),
        const SizedBox(height: 24),

        // بطاقات الإحصائيات
        _buildStatsCards(colorScheme, isDark),
      ],
    );
  }

  // بناء محدد نوع الملخص العصري
  Widget _buildModernSummarySelector(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.date_range, color: colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  'فترة التقرير',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(child: _buildSummaryChip('يومي', colorScheme)),
                const SizedBox(width: 8),
                Expanded(child: _buildSummaryChip('اسبوعي', colorScheme)),
                const SizedBox(width: 8),
                Expanded(child: _buildSummaryChip('شهري', colorScheme)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // بناء شريحة اختيار الملخص
  Widget _buildSummaryChip(String type, ColorScheme colorScheme) {
    final isSelected = _summaryType == type;
    return GestureDetector(
      onTap: () {
        setState(() {
          _summaryType = type;
          _loadProfits();
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? colorScheme.primary
              : colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
        ),
        child: Text(
          type,
          style: TextStyle(
            color: isSelected ? colorScheme.onPrimary : colorScheme.primary,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // بناء بطاقات الإحصائيات
  Widget _buildStatsCards(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // بطاقة الدخل
        _buildStatCard(
          title: 'إجمالي الدخل',
          value: '$totalIncome د.ع',
          icon: Icons.trending_up,
          color: Colors.green,
          colorScheme: colorScheme,
          isDark: isDark,
        ),

        const SizedBox(height: 16),

        // بطاقة المصروفات
        _buildStatCard(
          title: 'إجمالي المصروفات',
          value: '$totalExpense د.ع',
          icon: Icons.trending_down,
          color: Colors.red,
          colorScheme: colorScheme,
          isDark: isDark,
        ),

        const SizedBox(height: 16),

        // بطاقة صافي الأرباح
        _buildStatCard(
          title: 'صافي الأرباح',
          value: '$netProfit د.ع',
          icon: Icons.attach_money,
          color: netProfit >= 0 ? Colors.blue : Colors.red,
          colorScheme: colorScheme,
          isDark: isDark,
        ),

        const SizedBox(height: 16),

        // بطاقة التفعيلات
        _buildStatCard(
          title: 'عدد التفعيلات',
          value: '$activationsThisMonth تفعيل',
          icon: Icons.people,
          color: Colors.orange,
          colorScheme: colorScheme,
          isDark: isDark,
        ),
      ],
    );
  }

  // بناء بطاقة إحصائية واحدة
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required ColorScheme colorScheme,
    required bool isDark,
  }) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Row(
          children: [
            // أيقونة الإحصائية
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(icon, color: color, size: 32),
            ),

            const SizedBox(width: 20),

            // معلومات الإحصائية
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                ],
              ),
            ),

            // مؤشر الاتجاه
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: color.withValues(alpha: 0.3)),
              ),
              child: Icon(
                icon == Icons.trending_up
                    ? Icons.arrow_upward
                    : icon == Icons.trending_down
                    ? Icons.arrow_downward
                    : Icons.info,
                color: color,
                size: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // إضافة دالة static لإعادة تحميل الأرباح من أي مكان
  static Future<void> reloadProfitsScreen(BuildContext context) async {
    final state = context.findAncestorStateOfType<_ProfitsScreenState>();
    state?._loadProfits();
  }

  // مثال: دالة لإضافة معاملة تسديد دين (payDebt)
  Future<void> addPayDebtTransaction({
    required int subscriberId,
    required double amount,
    String description = 'تسديد دين',
  }) async {
    final t = Transaction(
      type: TransactionType.payDebt,
      description: '$description $amount',
      date: DateTime.now(),
      subscriberId: subscriberId,
    );
    await DBHelper.instance.insertTransaction(t);
    // بعد الإضافة، أعد تحميل الأرباح
    if (mounted) _loadProfits();
  }

  // مثال: دالة لإضافة معاملة تجديد اشتراك
  Future<void> addActivationTransaction({
    required int subscriberId,
    String description = 'تجديد اشتراك',
  }) async {
    final t = Transaction(
      type: TransactionType.renewal,
      description: description,
      date: DateTime.now(),
      subscriberId: subscriberId,
    );
    await DBHelper.instance.insertTransaction(t);
    if (mounted) _loadProfits();
  }

  // مثال: دالة لإضافة معاملة تجديد نقدي
  Future<void> addRenewalTransaction({
    required int subscriberId,
    String description = 'تجديد نقدي',
  }) async {
    final t = Transaction(
      type: TransactionType.renewal,
      description: description,
      date: DateTime.now(),
      subscriberId: subscriberId,
    );
    await DBHelper.instance.insertTransaction(t);
    if (mounted) _loadProfits();
  }
}
