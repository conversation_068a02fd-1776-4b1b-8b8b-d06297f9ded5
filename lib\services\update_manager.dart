import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'app_update_service.dart';
import 'data_protection_service.dart';
import '../features/app_update_screen.dart';

/// مدير التحديثات الرئيسي
class UpdateManager {
  static bool _isCheckingForUpdates = false;
  static DateTime? _lastAutoCheck;

  /// فحص التحديثات تلقائياً عند بدء التطبيق
  static Future<void> checkForUpdatesOnStartup(BuildContext context) async {
    if (_isCheckingForUpdates) return;

    try {
      _isCheckingForUpdates = true;
      debugPrint('🚀 [UPDATE_MANAGER] فحص التحديثات عند بدء التطبيق...');

      // فحص إذا كان يحتاج فحص
      final shouldCheck = await AppUpdateService.shouldCheckForUpdates();
      if (!shouldCheck) {
        debugPrint('ℹ️ [UPDATE_MANAGER] لا يحتاج فحص تحديثات الآن');
        return;
      }

      // فحص التحديثات
      final updateInfo = await AppUpdateService.checkForUpdates();
      if (updateInfo == null) {
        debugPrint('✅ [UPDATE_MANAGER] لا توجد تحديثات متاحة');
        return;
      }

      debugPrint('📋 [UPDATE_MANAGER] تحديث متاح: ${updateInfo.latestVersion}');

      // عرض شاشة التحديث
      if (context.mounted) {
        await _showUpdateDialog(context, updateInfo);
      }
    } catch (e) {
      debugPrint('❌ [UPDATE_MANAGER] خطأ في فحص التحديثات: $e');
    } finally {
      _isCheckingForUpdates = false;
      _lastAutoCheck = DateTime.now();
    }
  }

  /// فحص التحديثات يدوياً
  static Future<void> checkForUpdatesManually(BuildContext context) async {
    if (_isCheckingForUpdates) {
      _showMessage(context, 'جاري فحص التحديثات...');
      return;
    }

    try {
      _isCheckingForUpdates = true;
      
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      debugPrint('🔍 [UPDATE_MANAGER] فحص التحديثات يدوياً...');

      final updateInfo = await AppUpdateService.checkForUpdates();
      
      // إخفاء مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      if (updateInfo == null) {
        if (context.mounted) {
          _showMessage(context, 'التطبيق محدث إلى أحدث إصدار');
        }
        return;
      }

      // عرض شاشة التحديث
      if (context.mounted) {
        await _showUpdateDialog(context, updateInfo);
      }
    } catch (e) {
      debugPrint('❌ [UPDATE_MANAGER] خطأ في فحص التحديثات: $e');
      
      // إخفاء مؤشر التحميل
      if (context.mounted) {
        Navigator.of(context).pop();
        _showMessage(context, 'خطأ في فحص التحديثات: $e');
      }
    } finally {
      _isCheckingForUpdates = false;
    }
  }

  /// عرض شاشة التحديث
  static Future<void> _showUpdateDialog(BuildContext context, UpdateInfo updateInfo) async {
    if (updateInfo.isForced) {
      // تحديث إجباري - شاشة كاملة
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => AppUpdateScreen(
            updateInfo: updateInfo,
            canSkip: false,
          ),
          settings: const RouteSettings(name: '/forced_update'),
        ),
      );
    } else {
      // تحديث اختياري - حوار
      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.system_update, color: Theme.of(context).colorScheme.primary),
              const SizedBox(width: 8),
              const Text('تحديث متاح'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('الإصدار الجديد: ${updateInfo.latestVersion}'),
              const SizedBox(height: 8),
              if (updateInfo.releaseNotes.isNotEmpty) ...[
                const Text('ملاحظات الإصدار:', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text(updateInfo.releaseNotes),
                const SizedBox(height: 8),
              ],
              if (updateInfo.features.isNotEmpty) ...[
                const Text('الميزات الجديدة:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...updateInfo.features.take(3).map((feature) => Text('• $feature')),
                if (updateInfo.features.length > 3)
                  Text('... و ${updateInfo.features.length - 3} ميزات أخرى'),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('لاحقاً'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await AppUpdateService.skipVersion(updateInfo.latestVersion);
              },
              child: const Text('تخطي'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => AppUpdateScreen(
                      updateInfo: updateInfo,
                      canSkip: true,
                    ),
                  ),
                );
              },
              child: const Text('عرض التفاصيل'),
            ),
          ],
        ),
      );
    }
  }

  /// إنشاء نسخة احتياطية قبل التحديث
  static Future<bool> createPreUpdateBackup(String updateVersion) async {
    try {
      debugPrint('💾 [UPDATE_MANAGER] إنشاء نسخة احتياطية قبل التحديث...');

      final result = await DataProtectionService.createFullBackup(
        updateVersion: updateVersion,
        reason: 'pre_update_backup',
      );

      if (result.success) {
        debugPrint('✅ [UPDATE_MANAGER] تم إنشاء النسخة الاحتياطية بنجاح');
        return true;
      } else {
        debugPrint('❌ [UPDATE_MANAGER] فشل في إنشاء النسخة الاحتياطية: ${result.error}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [UPDATE_MANAGER] خطأ في إنشاء النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// استعادة البيانات بعد التحديث الفاشل
  static Future<bool> restoreAfterFailedUpdate(String backupId) async {
    try {
      debugPrint('🔄 [UPDATE_MANAGER] استعادة البيانات بعد التحديث الفاشل...');

      final result = await DataProtectionService.restoreFromBackup(backupId);

      if (result.success) {
        debugPrint('✅ [UPDATE_MANAGER] تم استعادة البيانات بنجاح');
        return true;
      } else {
        debugPrint('❌ [UPDATE_MANAGER] فشل في استعادة البيانات: ${result.error}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ [UPDATE_MANAGER] خطأ في استعادة البيانات: $e');
      return false;
    }
  }

  /// فحص التوافق مع قاعدة البيانات
  static Future<CompatibilityResult> checkDatabaseCompatibility(UpdateInfo updateInfo) async {
    try {
      debugPrint('🔍 [UPDATE_MANAGER] فحص توافق قاعدة البيانات...');

      if (!updateInfo.databaseMigrationRequired) {
        return CompatibilityResult(
          isCompatible: true,
          message: 'لا يتطلب ترحيل قاعدة البيانات',
        );
      }

      // فحص إمكانية الترحيل
      // يمكن إضافة فحوصات أكثر تفصيلاً هنا
      
      return CompatibilityResult(
        isCompatible: true,
        message: 'قاعدة البيانات متوافقة مع التحديث',
        requiresMigration: true,
      );
    } catch (e) {
      debugPrint('❌ [UPDATE_MANAGER] خطأ في فحص التوافق: $e');
      return CompatibilityResult(
        isCompatible: false,
        message: 'خطأ في فحص التوافق: $e',
      );
    }
  }

  /// تنظيف النسخ الاحتياطية القديمة
  static Future<void> cleanupOldBackups() async {
    try {
      await DataProtectionService.cleanupOldBackups(keepCount: 3);
      debugPrint('🧹 [UPDATE_MANAGER] تم تنظيف النسخ الاحتياطية القديمة');
    } catch (e) {
      debugPrint('❌ [UPDATE_MANAGER] خطأ في تنظيف النسخ الاحتياطية: $e');
    }
  }

  /// إظهار رسالة للمستخدم
  static void _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// فحص إذا كان هناك فحص تحديثات قيد التشغيل
  static bool get isCheckingForUpdates => _isCheckingForUpdates;

  /// وقت آخر فحص تلقائي
  static DateTime? get lastAutoCheck => _lastAutoCheck;

  /// إعادة تعيين حالة الفحص (للاختبار)
  static void resetCheckingState() {
    _isCheckingForUpdates = false;
    _lastAutoCheck = null;
  }
}

/// نتيجة فحص التوافق
class CompatibilityResult {
  final bool isCompatible;
  final String message;
  final bool requiresMigration;

  CompatibilityResult({
    required this.isCompatible,
    required this.message,
    this.requiresMigration = false,
  });
}
