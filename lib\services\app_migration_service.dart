import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'session_service.dart';
import 'stats_service.dart';
import 'account_service.dart';
import 'multi_device_service.dart';
import 'legacy_cleanup_service.dart';
import 'system_audit_service.dart';

/// خدمة ترحيل التطبيق للنظام الجديد
class AppMigrationService {
  static bool _isMigrated = false;

  /// تهيئة التطبيق مع النظام الجديد
  static Future<bool> initializeNewSystem() async {
    try {
      debugPrint('🚀 [MIGRATION] بدء تهيئة النظام الجديد...');

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('ℹ️ [MIGRATION] لا يوجد مستخدم مسجل دخول');
        return true; // لا حاجة للترحيل
      }

      // 1. التحقق من وجود الحساب في النظام الجديد
      await _ensureUserAccountExists(user);

      // 2. ترحيل الأجهزة للنظام الجديد
      await _migrateUserDevices(user.id);

      // 3. تهيئة الجلسة النشطة
      await _initializeActiveSession();

      // 4. تحديث الإحصائيات اليومية
      await _updateDailyStats();

      // 5. تنظيف البقايا القديمة
      await _cleanupLegacyData(user.id);

      _isMigrated = true;
      debugPrint('✅ [MIGRATION] تم تهيئة النظام الجديد بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في تهيئة النظام الجديد: $e');
      return false;
    }
  }

  /// التأكد من وجود حساب المستخدم في النظام الجديد
  static Future<void> _ensureUserAccountExists(User user) async {
    try {
      debugPrint('👤 [MIGRATION] التحقق من حساب المستخدم: ${user.id}');

      // التحقق من وجود الحساب في النظام الجديد
      final existingAccount = await AccountService.getAccountDataV2(user.id);

      if (existingAccount == null) {
        debugPrint('📝 [MIGRATION] إنشاء حساب جديد في النظام المحدث');

        // إنشاء حساب جديد
        final displayName =
            user.userMetadata?['display_name'] ??
            user.email?.split('@')[0] ??
            'مستخدم';

        try {
          await AccountService.createAccountV2(
            user.id,
            displayName,
            user.email ?? '',
          );
          debugPrint('✅ [MIGRATION] تم إنشاء الحساب في النظام الجديد');
        } catch (e) {
          // ✅ فحص إذا كان الخطأ يتطلب إغلاق التطبيق
          if (e.toString().contains('FORCE_EXIT')) {
            debugPrint('🚪 [MIGRATION] إغلاق التطبيق بسبب عدم وجود المستخدم');
            // إنهاء الجلسة
            await Supabase.instance.client.auth.signOut();
            rethrow; // إعادة رمي الخطأ للمعالجة في المستوى الأعلى
          }
          rethrow;
        }
      } else {
        debugPrint('✅ [MIGRATION] الحساب موجود في النظام الجديد');
      }
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في التحقق من الحساب: $e');
      rethrow;
    }
  }

  /// ترحيل الأجهزة للنظام الجديد
  static Future<void> _migrateUserDevices(String userId) async {
    try {
      debugPrint('📱 [MIGRATION] ترحيل الأجهزة للنظام الجديد');

      // الحصول على معرف الجهاز الحالي
      final currentDeviceId = await MultiDeviceService.getDeviceId();

      // التحقق من وجود الجهاز في النظام الجديد
      final existingDevices = await MultiDeviceService.getUserDevicesV2(userId);
      final deviceExists = existingDevices.any(
        (device) => device['device_id'] == currentDeviceId,
      );

      if (!deviceExists) {
        debugPrint('🔗 [MIGRATION] ربط الجهاز الحالي بالنظام الجديد');

        // ربط الجهاز الحالي
        final deviceLinked = await MultiDeviceService.linkDeviceV2(
          userId,
          currentDeviceId,
          deviceName: 'جهاز مرحل',
        );

        if (deviceLinked) {
          debugPrint('✅ [MIGRATION] تم ربط الجهاز بالنظام الجديد');
        } else {
          debugPrint('⚠️ [MIGRATION] فشل في ربط الجهاز');
        }
      } else {
        debugPrint('✅ [MIGRATION] الجهاز موجود في النظام الجديد');
      }
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في ترحيل الأجهزة: $e');
    }
  }

  /// تهيئة الجلسة النشطة
  static Future<void> _initializeActiveSession() async {
    try {
      debugPrint('🔄 [MIGRATION] تهيئة الجلسة النشطة');

      // تهيئة خدمة الجلسات
      final sessionInitialized = await SessionService.initialize();

      if (sessionInitialized) {
        debugPrint('✅ [MIGRATION] تم تهيئة الجلسة النشطة');
      } else {
        debugPrint('⚠️ [MIGRATION] فشل في تهيئة الجلسة النشطة');
      }
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في تهيئة الجلسة: $e');
    }
  }

  /// تحديث الإحصائيات اليومية
  static Future<void> _updateDailyStats() async {
    try {
      debugPrint('📊 [MIGRATION] تحديث الإحصائيات اليومية');

      final statsUpdated = await StatsService.updateDailyStats();

      if (statsUpdated) {
        debugPrint('✅ [MIGRATION] تم تحديث الإحصائيات اليومية');
      } else {
        debugPrint('⚠️ [MIGRATION] فشل في تحديث الإحصائيات');
      }
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في تحديث الإحصائيات: $e');
    }
  }

  /// تنظيف البقايا القديمة
  static Future<void> _cleanupLegacyData(String userId) async {
    try {
      debugPrint('🧹 [MIGRATION] تنظيف البقايا القديمة للمستخدم: $userId');

      // فحص وجود بقايا قديمة
      final hasLegacy = await LegacyCleanupService.hasLegacyData();

      if (hasLegacy) {
        debugPrint('🔍 [MIGRATION] تم العثور على بقايا قديمة، بدء التنظيف...');

        // تنظيف شامل
        final cleanupResults = await LegacyCleanupService.performFullCleanup(
          userId: userId,
        );

        final cleanedCount = cleanupResults['total_cleaned'] ?? 0;
        final errors = cleanupResults['errors'] ?? [];

        if (cleanedCount > 0) {
          debugPrint('✅ [MIGRATION] تم تنظيف $cleanedCount مفتاح قديم');
        }

        if (errors.isNotEmpty) {
          debugPrint(
            '⚠️ [MIGRATION] حدثت ${errors.length} أخطاء أثناء التنظيف',
          );
        }
      } else {
        debugPrint('✅ [MIGRATION] لا توجد بقايا قديمة للتنظيف');
      }
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في تنظيف البقايا القديمة: $e');
    }
  }

  /// تنظيف البيانات القديمة (اختياري)
  static Future<void> cleanupOldData() async {
    try {
      debugPrint('🧹 [MIGRATION] تنظيف البيانات القديمة');

      // تنظيف الجلسات المنتهية
      final cleanedSessions = await SessionService.cleanupExpiredSessions();
      debugPrint('🧹 [MIGRATION] تم تنظيف $cleanedSessions جلسة منتهية');

      debugPrint('✅ [MIGRATION] تم تنظيف البيانات القديمة');
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في تنظيف البيانات: $e');
    }
  }

  /// التحقق من حالة الترحيل
  static bool get isMigrated => _isMigrated;

  /// إعادة تعيين حالة الترحيل (للاختبار)
  static void resetMigrationStatus() {
    _isMigrated = false;
  }

  /// جلب معلومات النظام الجديد
  static Future<Map<String, dynamic>> getSystemInfo() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        return {'error': 'لا يوجد مستخدم مسجل دخول'};
      }

      // جلب معلومات الحساب
      final accountData = await AccountService.getAccountDataV2(user.id);

      // جلب معلومات الأجهزة
      final devices = await MultiDeviceService.getUserDevicesV2(user.id);

      // جلب معلومات الجلسة
      final sessionInfo = SessionService.getCurrentSessionInfo();

      // جلب الإحصائيات الشخصية
      final liveStats = await StatsService.getMyPersonalStats();

      return {
        'migration_status': _isMigrated,
        'account_data': accountData,
        'devices_count': devices.length,
        'session_active': sessionInfo != null,
        'live_stats': liveStats,
        'system_version': '2.0',
      };
    } catch (e) {
      return {'error': e.toString()};
    }
  }

  /// اختبار النظام الجديد
  static Future<Map<String, bool>> testNewSystem() async {
    final results = <String, bool>{};

    try {
      // اختبار الاتصال بقاعدة البيانات
      final dbTest = await Supabase.instance.client
          .from('user_accounts')
          .select('count')
          .count(CountOption.exact);
      results['database_connection'] = dbTest.count >= 0;

      // اختبار الدوال المخصصة
      try {
        await StatsService.getMyPersonalStats();
        results['custom_functions'] = true;
      } catch (e) {
        results['custom_functions'] = false;
      }

      // اختبار خدمة الجلسات
      results['session_service'] = SessionService.isInitialized;

      // اختبار الإحصائيات
      try {
        await StatsService.updateDailyStats();
        results['stats_service'] = true;
      } catch (e) {
        results['stats_service'] = false;
      }
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في اختبار النظام: $e');
    }

    return results;
  }

  /// إجراء مراجعة شاملة للنظام
  static Future<Map<String, dynamic>> performSystemAudit() async {
    try {
      debugPrint('🔍 [MIGRATION] إجراء مراجعة شاملة للنظام...');

      final auditResults = await SystemAuditService.performFullAudit();

      // طباعة التقرير المفصل
      final report = SystemAuditService.generateDetailedReport(auditResults);
      debugPrint('📋 [MIGRATION] تقرير المراجعة:\n$report');

      return auditResults;
    } catch (e) {
      debugPrint('❌ [MIGRATION] خطأ في المراجعة الشاملة: $e');
      return {'error': e.toString()};
    }
  }
}
