import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppIntroductionScreen extends StatefulWidget {
  final VoidCallback onCompleted;

  const AppIntroductionScreen({super.key, required this.onCompleted});

  @override
  State<AppIntroductionScreen> createState() => _AppIntroductionScreenState();
}

class _AppIntroductionScreenState extends State<AppIntroductionScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  int _currentPage = 0;

  final List<IntroPage> _pages = [
    IntroPage(
      title: 'مرحباً بك في iTower',
      description:
          'نظام إدارة شامل لمقدمي خدمات الإنترنت\nيساعدك في إدارة المشتركين والشبكات بكفاءة عالية',
      icon: Icons.cell_tower,
      color: Colors.blue,
    ),
    IntroPage(
      title: 'إدارة المشتركين',
      description:
          'تتبع جميع المشتركين وحساباتهم\nإدارة الديون والمدفوعات بسهولة\nتجديد الاشتراكات تلقائياً',
      icon: Icons.people_outline,
      color: Colors.green,
    ),
    IntroPage(
      title: 'ربط SAS',
      description:
          'نظام إدارة مشتركين متكامل\nمزامنة تلقائية مع قاعدة البيانات\nإدارة شاملة للحسابات والمدفوعات',
      icon: Icons.router_outlined,
      color: Colors.orange,
    ),
    IntroPage(
      title: 'النسخ الاحتياطي',
      description:
          'حفظ البيانات في السحابة\nاستعادة البيانات بأمان\nمزامنة تلقائية ',
      icon: Icons.cloud_upload_outlined,
      color: Colors.purple,
    ),
    IntroPage(
      title: 'التقارير والإحصائيات',
      description:
          'تقارير مفصلة عن الأرباح\nإحصائيات المشتركين\nتحليل الأداء المالي',
      icon: Icons.analytics_outlined,
      color: Colors.teal,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeIntroduction();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipIntroduction() {
    _completeIntroduction();
  }

  Future<void> _completeIntroduction() async {
    // حفظ أن المستخدم شاهد الاستعراض
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('has_seen_introduction', true);

    // استدعاء callback للانتقال للشاشة التالية
    widget.onCompleted();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: isDark
                ? [
                    colorScheme.surface,
                    colorScheme.surface.withValues(alpha: 0.8),
                  ]
                : [
                    colorScheme.primary.withValues(alpha: 0.1),
                    colorScheme.surface,
                  ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط علوي مع زر التخطي
              _buildTopBar(),

              // محتوى الصفحات
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentPage = index;
                    });
                    _animationController.reset();
                    _animationController.forward();
                  },
                  itemCount: _pages.length,
                  itemBuilder: (context, index) {
                    return _buildPage(_pages[index]);
                  },
                ),
              ),

              // مؤشر الصفحات
              _buildPageIndicator(),

              // أزرار التنقل
              _buildNavigationButtons(),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // شعار التطبيق
          Row(
            children: [
              Icon(
                Icons.cell_tower,
                size: 32,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'iTower',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),

          // زر التخطي
          if (_currentPage < _pages.length - 1)
            TextButton(
              onPressed: _skipIntroduction,
              child: Text(
                'تخطي',
                style: TextStyle(
                  color: Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPage(IntroPage page) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة الصفحة
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: page.color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
                border: Border.all(
                  color: page.color.withValues(alpha: 0.3),
                  width: 2,
                ),
              ),
              child: Icon(page.icon, size: 60, color: page.color),
            ),

            const SizedBox(height: 48),

            // عنوان الصفحة
            Text(
              page.title,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),

            // وصف الصفحة
            Text(
              page.description,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withValues(alpha: 0.8),
                height: 1.6,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _pages.length,
        (index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: _currentPage == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: _currentPage == index
                ? Theme.of(context).colorScheme.primary
                : Theme.of(
                    context,
                  ).colorScheme.onSurface.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // زر السابق
          if (_currentPage > 0)
            OutlinedButton.icon(
              onPressed: _previousPage,
              icon: const Icon(Icons.arrow_back),
              label: const Text('السابق'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            )
          else
            const SizedBox(width: 100),

          // زر التالي/البدء
          ElevatedButton.icon(
            onPressed: _nextPage,
            icon: Icon(
              _currentPage == _pages.length - 1
                  ? Icons.check
                  : Icons.arrow_forward,
            ),
            label: Text(
              _currentPage == _pages.length - 1 ? 'ابدأ الآن' : 'التالي',
            ),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }
}

class IntroPage {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  IntroPage({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}
