import 'package:flutter/material.dart';

class GeneralSettingsScreen extends StatefulWidget {
  const GeneralSettingsScreen({Key? key}) : super(key: key);

  @override
  State<GeneralSettingsScreen> createState() => _GeneralSettingsScreenState();
}

class _GeneralSettingsScreenState extends State<GeneralSettingsScreen> {
  bool notificationsEnabled = true;
  bool darkModeEnabled = false;

  int durationType = 0;
  int dateType = 0;
  int startType = 0;
  bool showNano = true;
  String nanoIp = '';
  String nanoUser = '';
  String nanoPass = '';

  @override
  void initState() {
    super.initState();
    // يمكن لاحقاً تحميل القيم من قاعدة البيانات أو SharedPreferences
  }

  // دالة لحساب تاريخ انتهاء الاشتراك بناءً على نوع المدة (اختياري)
  DateTime calculateSubscriptionEndDate(DateTime startDate, int durationType) {
    if (durationType == 0) {
      int year = startDate.year;
      int month = startDate.month + 1;
      if (month > 12) {
        month = 1;
        year += 1;
      }
      int day = startDate.day;
      int lastDayOfNextMonth = DateTime(year, month + 1, 0).day;
      if (day > lastDayOfNextMonth) day = lastDayOfNextMonth;
      return DateTime(
        year,
        month,
        day,
        startDate.hour,
        startDate.minute,
        startDate.second,
      );
    } else {
      return startDate.add(const Duration(days: 30));
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    return Scaffold(
      appBar: AppBar(title: const Text(' '), centerTitle: true),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          _settingsSection(context, 'اعدادات النانو الافتراضية', [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    textAlign: TextAlign.start,
                    decoration: InputDecoration(
                      labelText: 'User',
                      fillColor: colorScheme.surfaceVariant,
                      filled: true,
                    ),
                    onChanged: (v) => nanoUser = v,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    textAlign: TextAlign.start,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'Password',
                      fillColor: colorScheme.surfaceVariant,
                      filled: true,
                    ),
                    onChanged: (v) => nanoPass = v,
                  ),
                ),
              ],
            ),
          ]),
        ],
      ),
    );
  }

  Widget _settingsSection(
    BuildContext context,
    String title,
    List<Widget> children,
  ) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(18),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withOpacity(0.07),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }
}
