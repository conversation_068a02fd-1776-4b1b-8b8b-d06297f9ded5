import 'package:flutter/material.dart';
import '../domain/subscribers_repository.dart';
import '../data/subscriber_model.dart';
import 'subscriber_details_screen.dart';

class SubscribersSearchDelegate extends SearchDelegate {
  final SubscribersRepository repository;
  SubscribersSearchDelegate(this.repository);

  List<Subscriber> _results = [];

  @override
  String get searchFieldLabel => 'بحث بالاسم أو الرقم أو اليوزر';

  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(icon: const Icon(Icons.clear), onPressed: () => query = ''),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, null),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return FutureBuilder<List<Subscriber>>(
      future: repository.getAllSubscribers(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }
        final results = snapshot.data!
            .where(
              (sub) =>
                  sub.name.contains(query) ||
                  sub.phone.contains(query) ||
                  sub.user.contains(query),
            )
            .toList();
        if (results.isEmpty) {
          return const Center(child: Text('لا يوجد نتائج.'));
        }
        return ListView.builder(
          itemCount: results.length,
          itemBuilder: (context, i) {
            final sub = results[i];
            return ListTile(
              title: Text(sub.name),
              subtitle: Text('رقم: ${sub.phone} | يوزر: ${sub.user}'),
              onTap: () {
                close(context, null);
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (_) => SubscriberDetailsScreen(
                      subscriber: sub,
                      repository: repository,
                    ),
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return const Center(child: Text('ابحث عن مشترك...'));
    }
    return buildResults(context);
  }
}
