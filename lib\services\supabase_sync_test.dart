import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../core/managers/internet_status_manager.dart';

/// خدمة اختبار وضمان المزامنة مع Supabase
class SupabaseSyncTest {
  static const String _lastSyncKey = 'last_sync_timestamp';
  static const String _syncStatusKey = 'sync_status';

  /// اختبار شامل للاتصال والمزامنة
  static Future<SyncTestResult> performFullSyncTest() async {
    debugPrint('🧪 [SYNC_TEST] بدء اختبار المزامنة الشامل...');

    final result = SyncTestResult();

    try {
      // 1. اختبار الاتصال بالإنترنت
      result.internetConnection = InternetStatusManager.isConnected;
      if (!result.internetConnection) {
        result.overallSuccess = false;
        result.errorMessage = 'لا يوجد اتصال بالإنترنت';
        return result;
      }

      // 2. اختبار جلسة Supabase
      result.supabaseSession = await _testSupabaseSession();
      if (!result.supabaseSession) {
        result.overallSuccess = false;
        result.errorMessage = 'جلسة Supabase غير صالحة';
        return result;
      }

      // 3. اختبار الوصول لقاعدة البيانات
      result.databaseAccess = await _testDatabaseAccess();
      if (!result.databaseAccess) {
        result.overallSuccess = false;
        result.errorMessage = 'لا يمكن الوصول لقاعدة البيانات';
        return result;
      }

      // 4. اختبار مزامنة البيانات
      result.dataSyncTest = await _testDataSync();
      if (!result.dataSyncTest) {
        result.overallSuccess = false;
        result.errorMessage = 'فشل في مزامنة البيانات';
        return result;
      }

      // 5. اختبار سلامة البيانات
      result.dataIntegrity = await _testDataIntegrity();

      result.overallSuccess = true;
      result.completedAt = DateTime.now();

      // حفظ نتائج الاختبار
      await _saveSyncTestResult(result);

      debugPrint('✅ [SYNC_TEST] اكتمل الاختبار بنجاح');
      return result;
    } catch (e) {
      debugPrint('❌ [SYNC_TEST] خطأ في الاختبار: $e');
      result.overallSuccess = false;
      result.errorMessage = e.toString();
      return result;
    }
  }

  /// اختبار جلسة Supabase
  static Future<bool> _testSupabaseSession() async {
    try {
      debugPrint('🔐 [SYNC_TEST] اختبار جلسة Supabase...');

      final session = Supabase.instance.client.auth.currentSession;
      if (session == null) {
        debugPrint('❌ [SYNC_TEST] لا توجد جلسة نشطة');
        return false;
      }

      // فحص انتهاء صلاحية التوكن
      final expiresAt = DateTime.fromMillisecondsSinceEpoch(
        session.expiresAt! * 1000,
      );
      final now = DateTime.now();
      final isExpired = expiresAt.isBefore(now);

      if (isExpired) {
        debugPrint('⚠️ [SYNC_TEST] الجلسة منتهية الصلاحية، محاولة التجديد...');

        try {
          await Supabase.instance.client.auth.refreshSession();
          final newSession = Supabase.instance.client.auth.currentSession;

          if (newSession == null) {
            debugPrint('❌ [SYNC_TEST] فشل في تجديد الجلسة');
            return false;
          }

          debugPrint('✅ [SYNC_TEST] تم تجديد الجلسة بنجاح');
        } catch (e) {
          debugPrint('❌ [SYNC_TEST] خطأ في تجديد الجلسة: $e');
          return false;
        }
      }

      debugPrint('✅ [SYNC_TEST] جلسة Supabase صالحة');
      return true;
    } catch (e) {
      debugPrint('❌ [SYNC_TEST] خطأ في اختبار الجلسة: $e');
      return false;
    }
  }

  /// اختبار الوصول لقاعدة البيانات
  static Future<bool> _testDatabaseAccess() async {
    try {
      debugPrint('🗄️ [SYNC_TEST] اختبار الوصول لقاعدة البيانات...');

      // اختبار قراءة بسيط
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('count')
          .count(CountOption.exact);

      debugPrint(
        '✅ [SYNC_TEST] الوصول لقاعدة البيانات ناجح - عدد السجلات: ${response.count}',
      );
      return true;
    } catch (e) {
      debugPrint('❌ [SYNC_TEST] خطأ في الوصول لقاعدة البيانات: $e');
      return false;
    }
  }

  /// اختبار مزامنة البيانات
  static Future<bool> _testDataSync() async {
    try {
      debugPrint('🔄 [SYNC_TEST] اختبار مزامنة البيانات...');

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('❌ [SYNC_TEST] لا يوجد مستخدم مسجل دخول');
        return false;
      }

      // اختبار قراءة بيانات المستخدم
      final accountData = await Supabase.instance.client
          .from('user_accounts')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle();

      if (accountData == null) {
        debugPrint('❌ [SYNC_TEST] لا توجد بيانات حساب للمستخدم');
        return false;
      }

      // اختبار تحديث بسيط (تحديث timestamp)
      await Supabase.instance.client
          .from('user_accounts')
          .update({'updated_at': DateTime.now().toIso8601String()})
          .eq('user_id', user.id);

      debugPrint('✅ [SYNC_TEST] مزامنة البيانات ناجحة');
      return true;
    } catch (e) {
      debugPrint('❌ [SYNC_TEST] خطأ في مزامنة البيانات: $e');
      return false;
    }
  }

  /// اختبار سلامة البيانات
  static Future<bool> _testDataIntegrity() async {
    try {
      debugPrint('🔍 [SYNC_TEST] اختبار سلامة البيانات...');

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return false;

      // جلب البيانات من السيرفر
      final serverData = await Supabase.instance.client
          .from('user_accounts')
          .select('*')
          .eq('user_id', user.id)
          .single();

      // جلب البيانات المحلية
      final prefs = await SharedPreferences.getInstance();
      final localDataJson = prefs.getString('cached_account_data_${user.id}');

      if (localDataJson != null) {
        final localData = jsonDecode(localDataJson);

        // مقارنة البيانات الأساسية
        final serverDisplayName = serverData['display_name'] ?? '';
        final localDisplayName = localData['display_name'] ?? '';

        if (serverDisplayName != localDisplayName) {
          debugPrint('⚠️ [SYNC_TEST] عدم تطابق في display_name');
          debugPrint('   السيرفر: $serverDisplayName');
          debugPrint('   المحلي: $localDisplayName');
        }

        final serverIsTrial = serverData['is_trial'] ?? true;
        final localIsTrial = localData['is_trial'] ?? true;

        if (serverIsTrial != localIsTrial) {
          debugPrint('⚠️ [SYNC_TEST] عدم تطابق في is_trial');
          debugPrint('   السيرفر: $serverIsTrial');
          debugPrint('   المحلي: $localIsTrial');
        }
      }

      debugPrint('✅ [SYNC_TEST] اختبار سلامة البيانات مكتمل');
      return true;
    } catch (e) {
      debugPrint('❌ [SYNC_TEST] خطأ في اختبار سلامة البيانات: $e');
      return false;
    }
  }

  /// حفظ نتائج الاختبار
  static Future<void> _saveSyncTestResult(SyncTestResult result) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_syncStatusKey, jsonEncode(result.toJson()));
      await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      debugPrint('❌ [SYNC_TEST] خطأ في حفظ نتائج الاختبار: $e');
    }
  }

  /// جلب آخر نتائج الاختبار
  static Future<SyncTestResult?> getLastSyncTestResult() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final resultJson = prefs.getString(_syncStatusKey);

      if (resultJson != null) {
        return SyncTestResult.fromJson(jsonDecode(resultJson));
      }

      return null;
    } catch (e) {
      debugPrint('❌ [SYNC_TEST] خطأ في جلب نتائج الاختبار: $e');
      return null;
    }
  }

  /// فحص إذا كان الاختبار مطلوب
  static Future<bool> isSyncTestNeeded() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSync = prefs.getInt(_lastSyncKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // إجراء اختبار كل 30 دقيقة
      const testInterval = 30 * 60 * 1000; // 30 دقيقة بالميلي ثانية

      return (now - lastSync) > testInterval;
    } catch (e) {
      return true; // في حالة الخطأ، قم بالاختبار
    }
  }
}

/// نتائج اختبار المزامنة
class SyncTestResult {
  bool internetConnection = false;
  bool supabaseSession = false;
  bool databaseAccess = false;
  bool dataSyncTest = false;
  bool dataIntegrity = false;
  bool overallSuccess = false;
  String? errorMessage;
  DateTime? completedAt;

  Map<String, dynamic> toJson() {
    return {
      'internetConnection': internetConnection,
      'supabaseSession': supabaseSession,
      'databaseAccess': databaseAccess,
      'dataSyncTest': dataSyncTest,
      'dataIntegrity': dataIntegrity,
      'overallSuccess': overallSuccess,
      'errorMessage': errorMessage,
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  static SyncTestResult fromJson(Map<String, dynamic> json) {
    final result = SyncTestResult();
    result.internetConnection = json['internetConnection'] ?? false;
    result.supabaseSession = json['supabaseSession'] ?? false;
    result.databaseAccess = json['databaseAccess'] ?? false;
    result.dataSyncTest = json['dataSyncTest'] ?? false;
    result.dataIntegrity = json['dataIntegrity'] ?? false;
    result.overallSuccess = json['overallSuccess'] ?? false;
    result.errorMessage = json['errorMessage'];
    result.completedAt = json['completedAt'] != null
        ? DateTime.parse(json['completedAt'])
        : null;
    return result;
  }
}
