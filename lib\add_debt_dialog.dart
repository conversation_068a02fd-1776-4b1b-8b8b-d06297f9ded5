import 'package:flutter/material.dart';
import 'features/subscribers/data/subscriber_model.dart';
import 'features/subscribers/data/transaction_model.dart' as MyTrans;
import 'features/subscribers/domain/subscribers_repository.dart';
import 'features/subscribers/domain/transaction_repository.dart';

class AddDebtDialog extends StatefulWidget {
  final Subscriber subscriber;
  final SubscribersRepository subscribersRepository;
  final TransactionRepository transactionRepository;
  const AddDebtDialog({
    super.key,
    required this.subscriber,
    required this.subscribersRepository,
    required this.transactionRepository,
  });

  @override
  State<AddDebtDialog> createState() => _AddDebtDialogState();
}

class _AddDebtDialogState extends State<AddDebtDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController(
    text: '000',
  );
  final TextEditingController _noteController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      child: Padding(
        padding: const EdgeInsets.all(18.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _amountController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'الدين',
                  suffixIcon: Icon(Icons.numbers),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                ),
                validator: (v) =>
                    v == null || v.isEmpty || v == '000' ? 'الدين مطلوب' : null,
              ),
              const SizedBox(height: 14),
              TextFormField(
                controller: _noteController,
                decoration: InputDecoration(
                  labelText: 'ملاحظة',
                  suffixIcon: Icon(Icons.copy),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                ),
              ),
              const SizedBox(height: 18),
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () async {
                    if (!_formKey.currentState!.validate()) return;
                    final amount =
                        double.tryParse(
                          _amountController.text.replaceAll(',', ''),
                        ) ??
                        0.0;
                    final updated = Subscriber(
                      id: widget.subscriber.id,
                      name: widget.subscriber.name,
                      totalDebt: widget.subscriber.totalDebt + amount,
                      user: widget.subscriber.user,
                      subscriptionPrice: widget.subscriber.subscriptionPrice,
                      subscriptionType: widget.subscriber.subscriptionType,
                      startDate: widget.subscriber.startDate,
                      endDate: widget.subscriber.endDate,
                      phone: widget.subscriber.phone,
                      notes: widget.subscriber.notes,
                      ip: widget.subscriber.ip,
                      subscriptionId: widget.subscriber.subscriptionId,
                      buyPrice: widget.subscriber.buyPrice,
                      boardId: widget.subscriber.boardId,
                      contract: widget.subscriber.contract,
                      status: widget.subscriber.status,
                      onlineStatus: widget.subscriber.onlineStatus,
                      isDeleted: widget
                          .subscriber
                          .isDeleted, // إضافة هذا السطر للحفاظ على حالة الحذف
                    );
                    await widget.subscribersRepository.updateSubscriber(
                      updated,
                    );
                    await widget.transactionRepository.insertTransaction(
                      MyTrans.Transaction(
                        type: MyTrans.TransactionType.addDebt,
                        description:
                            'إضافة دين ${amount.toStringAsFixed(0)}${_noteController.text.isNotEmpty ? ' - ${_noteController.text}' : ''}',
                        date: DateTime.now(),
                        subscriberId: widget.subscriber.id,
                      ),
                    );
                    if (!mounted) return;
                    Navigator.of(
                      context,
                    ).pop(true); // استخدم قيمة منطقية بدل النص
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                  ),
                  child: const Text('إضافة', style: TextStyle(fontSize: 18)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
