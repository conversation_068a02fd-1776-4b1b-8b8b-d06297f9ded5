import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import '../../db_helper.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _plugin =
      FlutterLocalNotificationsPlugin();
  bool _initialized = false;

  Future<void> init() async {
    if (_initialized) return;

    // تهيئة timezone
    tz_data.initializeTimeZones();
    tz.setLocalLocation(tz.getLocation('Asia/Baghdad'));

    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const InitializationSettings initSettings = InitializationSettings(
      android: androidSettings,
    );
    await _plugin.initialize(initSettings);
    _initialized = true;
  }

  /// إشعار انتهاء الاشتراك
  Future<void> showEndNotification(
    String subscriberName,
    DateTime endDate,
  ) async {
    await init();

    final formattedDate = '${endDate.day}/${endDate.month}/${endDate.year}';

    await _plugin.show(
      3001 + DateTime.now().millisecondsSinceEpoch % 1000000,
      'انتهاء اشتراك',
      'انتهى اشتراك $subscriberName بتاريخ $formattedDate',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'end_channel',
          'تنبيهات انتهاء الاشتراك',
          channelDescription: 'تنبيه بانتهاء اشتراك المشترك',
          importance: Importance.max,
          priority: Priority.high,
        ),
      ),
    );
  }

  // تم إلغاء إشعارات الديون - الاحتفاظ بالدالة للتوافق مع الكود القديم
  @Deprecated('تم إلغاء إشعارات الديون')
  Future<void> showDebtNotification(String subscriberName, num debt) async {
    // لا تفعل شيئاً - تم إلغاء إشعارات الديون
    debugPrint('تم إلغاء إشعار الدين للمشترك: $subscriberName');
  }

  /// جدولة إشعار مسبق لانتهاء اشتراك
  Future<void> scheduleEndNotification({
    required int subscriberId,
    required String subscriberName,
    required DateTime endDate,
  }) async {
    await init();

    try {
      // إلغاء أي إشعار مجدول مسبقاً لنفس المشترك
      await _plugin.cancel(subscriberId);

      // جدولة إشعار جديد
      final scheduledDate = tz.TZDateTime.from(endDate, tz.local);

      // التأكد من أن التاريخ في المستقبل
      if (scheduledDate.isAfter(tz.TZDateTime.now(tz.local))) {
        await _plugin.zonedSchedule(
          subscriberId,
          'انتهاء اشتراك مشترك',
          'انتهى اشتراك $subscriberName اليوم',
          scheduledDate,
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'end_channel',
              'تنبيهات انتهاء الاشتراك',
              channelDescription: 'تنبيه بانتهاء اشتراك مشترك',
              importance: Importance.max,
              priority: Priority.high,
            ),
          ),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        );

        debugPrint('تم جدولة إشعار انتهاء اشتراك $subscriberName في $endDate');
      } else {
        debugPrint(
          'تاريخ انتهاء اشتراك $subscriberName في الماضي - لن يتم جدولة إشعار',
        );
      }
    } catch (e) {
      debugPrint('خطأ في جدولة إشعار انتهاء الاشتراك: $e');
    }
  }

  /// إلغاء إشعار مجدول لمشترك
  Future<void> cancelScheduledNotification(int subscriberId) async {
    await init();

    try {
      await _plugin.cancel(subscriberId);
      debugPrint('تم إلغاء الإشعار المجدول للمشترك $subscriberId');
    } catch (e) {
      debugPrint('خطأ في إلغاء الإشعار المجدول: $e');
    }
  }

  /// فحص الإشعارات المفقودة وإرسالها
  Future<void> checkMissedNotifications() async {
    try {
      final db = await DBHelper.instance.database;
      final now = DateTime.now();

      // البحث عن الاشتراكات المنتهية التي لم يتم إرسال إشعار لها
      final expiredSubscriptions = await db.rawQuery(
        '''
        SELECT s.*, sub.name
        FROM subscriptions s
        LEFT JOIN subscribers sub ON s.subscriber_id = sub.id
        WHERE s.end_date <= ?
        AND s.subscriber_id NOT IN (
          SELECT DISTINCT CAST(SUBSTR(name, INSTR(name, '_') + 1) AS INTEGER)
          FROM me_end_notifications
          WHERE DATE(sent_at) = DATE(?)
        )
      ''',
        [now.toIso8601String(), now.toIso8601String()],
      );

      // إرسال إشعارات للاشتراكات المنتهية
      for (final sub in expiredSubscriptions) {
        final subscriberName = sub['name']?.toString() ?? 'مشترك غير معروف';
        final endDateStr = sub['end_date']?.toString() ?? '';

        if (endDateStr.isNotEmpty) {
          final endDate = DateTime.parse(endDateStr);
          await showEndNotification(subscriberName, endDate);
        }
      }

      print('تم فحص ${expiredSubscriptions.length} اشتراك منتهي');
    } catch (e) {
      print('خطأ في فحص الإشعارات المفقودة: $e');
    }
  }
}
