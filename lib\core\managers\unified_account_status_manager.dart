import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/unified_account_model.dart';
import '../services/unified_data_flow_service.dart';

/// مدير حالة الحساب الموحد - المصدر الوحيد للحقيقة
/// يعتمد على UnifiedDataFlowService لجلب البيانات
/// ويوفر واجهة بسيطة وموثوقة لباقي النظام
class UnifiedAccountStatusManager {
  static const String _tag = '[UNIFIED_ACCOUNT_MANAGER]';
  
  // المتحكم في حالة الحساب
  static final StreamController<UnifiedAccountModel?> _statusController = 
      StreamController<UnifiedAccountModel?>.broadcast();
  
  // البيانات الحالية
  static UnifiedAccountModel? _currentStatus;
  static String? _currentUserId;
  
  // حالة النظام
  static bool _isInitialized = false;
  static StreamSubscription<UnifiedAccountModel?>? _dataSubscription;
  
  // الخصائص العامة
  static Stream<UnifiedAccountModel?> get statusStream => _statusController.stream;
  static UnifiedAccountModel? get currentStatus => _currentStatus;
  static bool get isInitialized => _isInitialized;
  
  // خصائص سريعة للوصول
  static bool get isActive => _currentStatus?.isActive ?? false;
  static bool get isRestricted => _currentStatus?.isRestricted ?? true;
  static bool get isTrial => _currentStatus?.isTrial ?? false;
  static bool get isExpired => _currentStatus?.isExpired ?? true;
  static int get trialDaysRemaining => _currentStatus?.trialDaysRemaining ?? 0;
  static AccountStatus get accountStatus => _currentStatus?.status ?? AccountStatus.unknown;
  
  /// تهيئة المدير
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    debugPrint('$_tag تهيئة مدير حالة الحساب الموحد...');
    
    try {
      // تهيئة خدمة تدفق البيانات
      await UnifiedDataFlowService.initialize();
      
      // الاشتراك في تدفق البيانات
      _dataSubscription = UnifiedDataFlowService.dataStream.listen(
        _onDataChanged,
        onError: _onDataError,
      );
      
      _isInitialized = true;
      debugPrint('$_tag تم تهيئة المدير بنجاح');
      
    } catch (e) {
      debugPrint('$_tag خطأ في تهيئة المدير: $e');
      rethrow;
    }
  }
  
  /// تحديث معرف المستخدم
  static Future<void> updateUserId(String? userId) async {
    if (!_isInitialized) await initialize();
    
    if (_currentUserId == userId) return;
    
    debugPrint('$_tag تحديث معرف المستخدم: $userId');
    
    _currentUserId = userId;
    
    // تحديث خدمة تدفق البيانات
    await UnifiedDataFlowService.updateUserId(userId);
  }
  
  /// إعادة تحميل حالة الحساب
  static Future<void> refreshStatus() async {
    if (!_isInitialized) {
      debugPrint('$_tag المدير غير مهيأ - تهيئة أولاً');
      await initialize();
    }
    
    debugPrint('$_tag إعادة تحميل حالة الحساب');
    await UnifiedDataFlowService.refreshData();
  }
  
  /// فحص إذا كان يمكن الوصول لميزة معينة
  static bool canAccessFeature(String featureName) {
    if (_currentStatus == null) {
      debugPrint('$_tag لا توجد بيانات حساب - منع الوصول لـ $featureName');
      return false;
    }
    
    switch (_currentStatus!.status) {
      case AccountStatus.active:
        return true; // الحساب النشط يمكنه الوصول لكل شيء
        
      case AccountStatus.trial:
        return _canTrialAccessFeature(featureName);
        
      case AccountStatus.expired:
      case AccountStatus.banned:
      case AccountStatus.suspended:
      case AccountStatus.locked:
        debugPrint('$_tag حساب مقيد (${_currentStatus!.status.name}) - منع الوصول لـ $featureName');
        return false;
        
      case AccountStatus.unknown:
        debugPrint('$_tag حالة غير معروفة - منع الوصول لـ $featureName');
        return false;
    }
  }
  
  /// فحص إذا كان الحساب التجريبي يمكنه الوصول للميزة
  static bool _canTrialAccessFeature(String featureName) {
    // فحص إذا انتهت الفترة التجريبية
    if (_currentStatus!.isTrialExpired) {
      debugPrint('$_tag الفترة التجريبية منتهية - منع الوصول لـ $featureName');
      return false;
    }
    
    // الميزات المسموحة للحساب التجريبي
    const allowedFeatures = {
      'board_connection',
      'data_sync',
      'subscribers',
      'devices',
      'basic_settings',
    };
    
    final isAllowed = allowedFeatures.contains(featureName);
    
    if (!isAllowed) {
      debugPrint('$_tag ميزة غير مسموحة للحساب التجريبي: $featureName');
    }
    
    return isAllowed;
  }
  
  /// الحصول على رسالة القيد للميزة
  static String? getRestrictionMessage(String featureName) {
    if (_currentStatus == null) {
      return 'لا يمكن تحديد حالة الحساب';
    }
    
    switch (_currentStatus!.status) {
      case AccountStatus.active:
        return null; // لا توجد قيود
        
      case AccountStatus.trial:
        if (_currentStatus!.isTrialExpired) {
          return 'انتهت فترتك التجريبية. يرجى تفعيل حسابك للمتابعة.';
        }
        if (!_canTrialAccessFeature(featureName)) {
          return 'هذه الميزة غير متاحة في الحساب التجريبي. '
                 'يتبقى ${_currentStatus!.trialDaysRemaining} أيام من فترتك التجريبية.';
        }
        return null;
        
      case AccountStatus.expired:
        return 'حسابك منتهي الصلاحية. يرجى تجديد اشتراكك للمتابعة.';
        
      case AccountStatus.banned:
        return 'تم حظر حسابك. يرجى التواصل مع الدعم الفني.';
        
      case AccountStatus.suspended:
        return 'تم إيقاف حسابك مؤقتاً. يرجى التواصل مع الدعم الفني.';
        
      case AccountStatus.locked:
        final reason = _currentStatus!.lockReason ?? 'غير محدد';
        return 'تم قفل حسابك. السبب: $reason';
        
      case AccountStatus.unknown:
        return 'حالة الحساب غير معروفة. يرجى إعادة تسجيل الدخول.';
    }
  }
  
  /// الحصول على معلومات الحساب للعرض
  static Map<String, dynamic> getAccountInfo() {
    if (_currentStatus == null) {
      return {
        'status': 'unknown',
        'message': 'لا توجد بيانات حساب',
        'canAccess': false,
      };
    }
    
    final status = _currentStatus!;
    
    return {
      'status': status.status.name,
      'type': status.type.name,
      'isTrial': status.isTrial,
      'trialDaysRemaining': status.trialDaysRemaining,
      'isActive': status.isActive,
      'isRestricted': status.isRestricted,
      'isExpired': status.isExpired,
      'displayName': status.displayName,
      'email': status.email,
      'lastUpdated': status.updatedAt.toIso8601String(),
      'dataSource': status.dataSource,
      'message': _getStatusMessage(status),
    };
  }
  
  /// الحصول على رسالة الحالة
  static String _getStatusMessage(UnifiedAccountModel status) {
    switch (status.status) {
      case AccountStatus.active:
        return 'حسابك نشط ويمكنك الوصول لجميع الميزات';
        
      case AccountStatus.trial:
        if (status.isTrialExpired) {
          return 'انتهت فترتك التجريبية';
        }
        return 'حساب تجريبي - يتبقى ${status.trialDaysRemaining} أيام';
        
      case AccountStatus.expired:
        return 'حسابك منتهي الصلاحية';
        
      case AccountStatus.banned:
        return 'تم حظر حسابك';
        
      case AccountStatus.suspended:
        return 'تم إيقاف حسابك مؤقتاً';
        
      case AccountStatus.locked:
        return 'تم قفل حسابك';
        
      case AccountStatus.unknown:
        return 'حالة الحساب غير معروفة';
    }
  }
  
  /// معالج تغيير البيانات
  static void _onDataChanged(UnifiedAccountModel? newData) {
    final oldStatus = _currentStatus?.status;
    _currentStatus = newData;
    
    // إشعار المستمعين
    _statusController.add(_currentStatus);
    
    // تسجيل التغيير
    if (newData != null) {
      debugPrint('$_tag تم تحديث حالة الحساب: ${newData.status.name} '
                '(مصدر: ${newData.dataSource})');
      
      if (oldStatus != null && oldStatus != newData.status) {
        debugPrint('$_tag تغيير في حالة الحساب: $oldStatus → ${newData.status.name}');
      }
    } else {
      debugPrint('$_tag تم مسح بيانات الحساب');
    }
  }
  
  /// معالج خطأ البيانات
  static void _onDataError(dynamic error) {
    debugPrint('$_tag خطأ في تدفق البيانات: $error');
    
    // في حالة الخطأ، احتفظ بالبيانات الحالية ولا تغيرها
    // هذا يمنع فقدان البيانات بسبب أخطاء مؤقتة
  }
  
  /// إنهاء المدير
  static void dispose() {
    debugPrint('$_tag إنهاء مدير حالة الحساب');
    
    _dataSubscription?.cancel();
    _statusController.close();
    
    _currentStatus = null;
    _currentUserId = null;
    _isInitialized = false;
  }
  
  /// إحصائيات المدير
  static Map<String, dynamic> getManagerStats() {
    return {
      'isInitialized': _isInitialized,
      'hasStatus': _currentStatus != null,
      'currentUserId': _currentUserId,
      'accountStatus': _currentStatus?.status.name,
      'dataSource': _currentStatus?.dataSource,
      'lastUpdate': _currentStatus?.updatedAt.toIso8601String(),
      'dataFlowStats': UnifiedDataFlowService.getSystemStats(),
    };
  }
}
