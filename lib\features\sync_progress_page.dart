import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math' as math;
import '../../utils/api_helper.dart' as api_helper;
import '../../main.dart';

class SyncProgressPage extends StatefulWidget {
  final Map<String, dynamic> board;
  const SyncProgressPage({Key? key, required this.board}) : super(key: key);

  @override
  State<SyncProgressPage> createState() => _SyncProgressPageState();
}

class SyncStep {
  final String title;
  final String subtitle;
  final IconData icon;
  String status; // 'pending', 'in_progress', 'done', 'error'
  String? message;
  DateTime? startTime;
  DateTime? endTime;
  int? count;
  int? total;
  double? speed; // عناصر/ثانية

  SyncStep({
    required this.title,
    required this.subtitle,
    required this.icon,
    this.status = 'pending',
    this.message,
    this.startTime,
    this.endTime,
    this.count,
    this.total,
    this.speed,
  });

  Duration? get duration {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!);
    }
    return null;
  }

  double get progress {
    if (total != null && total! > 0 && count != null) {
      return count! / total!;
    }
    return status == 'done' ? 1.0 : 0.0;
  }
}

class _SyncProgressPageState extends State<SyncProgressPage> {
  int total = 0;
  int fetched = 0;
  int profilesCount = 0;
  bool loading = true;
  String status = 'جاري المزامنة...';
  String? errorMsg;
  List<SyncStep> steps = [
    SyncStep(
      title: 'تسجيل الدخول الآمن',
      subtitle: 'التحقق من الهوية والصلاحيات',
      icon: Icons.security_rounded,
    ),
    SyncStep(
      title: 'جلب المشتركين',
      subtitle: 'تحميل بيانات العملاء من السيرفر',
      icon: Icons.people_rounded,
    ),
    SyncStep(
      title: 'استنتاج الباقات الذكي',
      subtitle: 'تحليل وإنشاء الباقات من بيانات المشتركين',
      icon: Icons.auto_awesome_rounded,
    ),
    SyncStep(
      title: 'مزامنة رصيد اللوحة',
      subtitle: 'تحديث الرصيد المالي من السيرفر',
      icon: Icons.account_balance_wallet_rounded,
    ),
    SyncStep(
      title: 'مزامنة عناوين IP',
      subtitle: 'تحديث حالة الاتصال والعناوين',
      icon: Icons.wifi_rounded,
    ),
    SyncStep(
      title: 'تحسين قاعدة البيانات',
      subtitle: 'حفظ وفهرسة البيانات المحدثة',
      icon: Icons.storage_rounded,
    ),
  ];
  // Timer? _syncTimer;
  int? _lastUserCount;
  int? _lastProfilesCount;
  DateTime? _lastSyncTime;

  @override
  void initState() {
    super.initState();
    _startSync();
    // تم حذف المزامنة الدورية التلقائية
  }

  @override
  void dispose() {
    // تم حذف إلغاء المؤقت
    super.dispose();
  }

  // تم حذف منطق المزامنة الدورية التلقائية بالكامل

  /// تحديث تقدم الخطوة مع حساب السرعة والإحصائيات
  void _updateStepProgress(
    String step,
    String? status,
    int? count,
    int? total,
  ) {
    final now = DateTime.now();

    switch (step) {
      case 'login':
        _updateStep(0, status, count, total, now);
        break;
      case 'users':
        _updateStep(1, status, count, total, now);
        if (count != null) fetched = count;
        break;
      case 'packages_extraction':
        _updateStep(2, status, count, total, now);
        if (count != null) profilesCount = count;
        break;
      case 'balance_sync':
        _updateStep(3, status, count, total, now);
        break;
      case 'ip_sync':
        _updateStep(4, status, count, total, now);
        break;
      case 'db':
        _updateStep(5, status, count, total, now);
        break;
    }
  }

  /// تحديث خطوة معينة مع حساب السرعة
  void _updateStep(
    int index,
    String? status,
    int? count,
    int? total,
    DateTime now,
  ) {
    if (index >= steps.length) return;

    final step = steps[index];
    final oldStatus = step.status;

    // تحديث الحالة
    if (status != null) {
      step.status = status;
    }

    // تحديث العدد والإجمالي
    if (count != null) step.count = count;
    if (total != null) step.total = total;

    // تحديث أوقات البداية والنهاية
    if (status == 'in_progress' && oldStatus != 'in_progress') {
      step.startTime = now;
    } else if (status == 'done' && step.startTime != null) {
      step.endTime = now;

      // حساب السرعة
      final duration = step.duration;
      if (duration != null &&
          duration.inMilliseconds > 0 &&
          step.count != null) {
        step.speed = step.count! / (duration.inMilliseconds / 1000.0);
      }
    }

    // تحديث الرسائل الخاصة
    if (index == 3) {
      // ip_sync step
      if (status == 'start') {
        step.message = 'جاري جلب عناوين IP...';
      } else if (status == 'in_progress' && count != null && total != null) {
        step.message = 'تم معالجة $count من أصل $total مشترك متصل';
      } else if (status == 'done' && count != null && total != null) {
        step.message = 'تم تحديث $count عنوان IP من أصل $total متصل';
      } else if (status == 'error') {
        step.message = 'خطأ في مزامنة عناوين IP';
      }
    }
  }

  Future<void> _startSync({bool background = false}) async {
    if (!background && mounted) {
      setState(() {
        loading = true;
        errorMsg = null;
        fetched = 0;
        profilesCount = 0;
        status = 'جاري المزامنة...';
        for (var s in steps) {
          s.status = 'pending';
          s.message = null;
          s.startTime = null;
          s.endTime = null;
          s.count = null;
          s.total = null;
          s.speed = null;
        }
      });
    }
    int? prevUserCount = _lastUserCount;
    int? prevProfilesCount = _lastProfilesCount;
    debugPrint(
      'قبل المزامنة: prevUserCount=$prevUserCount, prevProfilesCount=$prevProfilesCount',
    );
    bool profilesError = false;
    try {
      await api_helper.syncAllFromServer(
        board: widget.board,
        progressCallback: (step, {status, count, total}) {
          debugPrint(
            'progressCallback: step=$step, status=$status, count=$count, total=$total',
          );
          _updateStepProgress(step, status, count, total);
          if (!background && mounted) setState(() {});
        },
      );
    } catch (e) {
      debugPrint('خطأ أثناء المزامنة: $e');
      if (!background && mounted) {
        setState(() {
          loading = false;
          errorMsg = e.toString();
          status = 'حدث خطأ أثناء المزامنة';
        });
      }
      // إشعار ببطء الإنترنت أو فشل المزامنة في الخلفية
      if (background && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('المزامنة فشلت أو الإنترنت بطيء!'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
    _lastSyncTime = DateTime.now();
    _lastUserCount = fetched;
    _lastProfilesCount = profilesCount;
    debugPrint(
      'بعد المزامنة: fetched=$fetched, profilesCount=$profilesCount, _lastUserCount=$_lastUserCount, _lastProfilesCount=$_lastProfilesCount',
    );
    if (!background && mounted) {
      setState(() {
        loading = false;
        status = profilesError
            ? 'حدث خطأ في جلب البروفايلات'
            : 'تمت المزامنة بنجاح';
      });
    }

    // إشعار اكتمال المزامنة لتحديث الشاشة الرئيسية
    if (!profilesError) {
      syncCompletedNotifier.value = true;
      debugPrint(
        '[SYNC_PROGRESS] تم إشعار اكتمال المزامنة - سيتم تحديث الرصيد في الشاشة الرئيسية',
      );

      // إعادة تعيين القيمة بعد فترة قصيرة لضمان استقبال الإشعارات المستقبلية
      Future.delayed(Duration(seconds: 2), () {
        syncCompletedNotifier.value = false;
        debugPrint(
          '[SYNC_PROGRESS] تم إعادة تعيين syncCompletedNotifier إلى false',
        );
      });
    }
    // مقارنة عدد المشتركين وإظهار إشعار إذا تغير العدد
    if (prevUserCount != null && fetched != prevUserCount) {
      String changeType = fetched > prevUserCount ? 'إضافة' : 'حذف';
      int diff = (fetched - prevUserCount).abs();
      debugPrint(
        'تغير عدد المشتركين: prev=$prevUserCount, new=$fetched, diff=$diff, type=$changeType',
      );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم $changeType $diff مشترك${diff > 1 ? "ين" : ""}'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    }
    // مقارنة عدد البروفايلات وإظهار إشعار إذا تغير العدد
    if (prevProfilesCount != null && profilesCount != prevProfilesCount) {
      String changeType = profilesCount > prevProfilesCount ? 'إضافة' : 'حذف';
      int diff = (profilesCount - prevProfilesCount).abs();
      debugPrint(
        'تغير عدد البروفايلات: prev=$prevProfilesCount, new=$profilesCount, diff=$diff, type=$changeType',
      );
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم $changeType $diff بروفايل${diff > 1 ? "ات" : ""}',
            ),
            backgroundColor: Colors.purple,
          ),
        );
      }
    }
  }

  double get progress =>
      steps.where((s) => s.status == 'done').length / steps.length;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),

          SafeArea(
            child: Column(
              children: [
                // رأس الشاشة العصري الجديد
                _buildUltraModernHeader(colorScheme, isDark),

                // محتوى الشاشة
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 24),
                    child: Column(
                      children: [
                        const SizedBox(height: 24),

                        // مؤشر التقدم الذكي
                        _buildSmartProgressIndicator(colorScheme, isDark),

                        const SizedBox(height: 32),

                        // قائمة الخطوات العصرية
                        _buildUltraModernStepsList(colorScheme, isDark),

                        const SizedBox(height: 32),

                        // إحصائيات الأداء
                        _buildPerformanceStats(colorScheme, isDark),

                        const SizedBox(height: 24),

                        // أزرار التحكم العصرية
                        if (!loading) _buildBottomControls(colorScheme, isDark),

                        // رسالة الخطأ العصرية
                        if (errorMsg != null)
                          _buildModernErrorMessage(colorScheme),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الشاشة العصري الجديد
  Widget _buildUltraModernHeader(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 60, 24, 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primary.withValues(alpha: 0.1),
            colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
      ),
      child: Column(
        children: [
          // شريط التنقل العلوي
          Row(
            children: [
              // عنوان عصري
              Column(
                children: [
                  Text(
                    '⚡ مزامنة فائقة السرعة',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  Text(
                    'تقنية ذكية متطورة',
                    style: TextStyle(
                      fontSize: 12,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),

              const Spacer(),

              // أيقونة الحالة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: loading
                      ? Colors.orange.withValues(alpha: 0.2)
                      : Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  loading ? Icons.sync_rounded : Icons.check_circle_rounded,
                  color: loading ? Colors.orange : Colors.green,
                  size: 20,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر التقدم الذكي
  Widget _buildSmartProgressIndicator(ColorScheme colorScheme, bool isDark) {
    final overallProgress = progress;
    final activeStep = steps.firstWhere(
      (s) => s.status == 'in_progress',
      orElse: () => steps.last,
    );

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary.withValues(alpha: 0.1),
            colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // التقدم الإجمالي مع تأثيرات بصرية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.trending_up_rounded,
                          color: colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'التقدم الإجمالي',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // مؤشر التقدم المتحرك
                    Container(
                      height: 8,
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHighest,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: TweenAnimationBuilder<double>(
                          tween: Tween(begin: 0, end: overallProgress),
                          duration: const Duration(milliseconds: 800),
                          curve: Curves.easeOutCubic,
                          builder: (context, value, child) {
                            return LinearProgressIndicator(
                              value: value,
                              backgroundColor: Colors.transparent,
                              valueColor: AlwaysStoppedAnimation(
                                Color.lerp(Colors.orange, Colors.green, value),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 24),

              // النسبة المئوية مع تأثير دائري
              TweenAnimationBuilder<double>(
                tween: Tween(begin: 0, end: overallProgress),
                duration: const Duration(milliseconds: 800),
                builder: (context, value, child) {
                  return Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          Color.lerp(Colors.orange, Colors.green, value)!,
                          Color.lerp(
                            Colors.orange,
                            Colors.green,
                            value,
                          )!.withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '${(value * 100).toInt()}%',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),

          const SizedBox(height: 20),

          // معلومات المرحلة النشطة
          if (activeStep.status == 'in_progress') ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  // أيقونة متحركة
                  TweenAnimationBuilder<double>(
                    tween: Tween(begin: 0, end: 2 * math.pi),
                    duration: const Duration(seconds: 2),
                    builder: (context, value, child) {
                      return Transform.rotate(
                        angle: value,
                        child: Icon(
                          activeStep.icon,
                          color: Colors.orange,
                          size: 24,
                        ),
                      );
                    },
                  ),

                  const SizedBox(width: 12),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '🔄 ${activeStep.title}',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          activeStep.subtitle,
                          style: TextStyle(
                            fontSize: 12,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // إحصائيات سريعة
                  if (activeStep.count != null && activeStep.total != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${activeStep.count}/${activeStep.total}',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قائمة الخطوات العصرية الجديدة
  Widget _buildUltraModernStepsList(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: steps.asMap().entries.map((entry) {
        final index = entry.key;
        final step = entry.value;
        return _buildUltraModernStepCard(step, index, colorScheme, isDark);
      }).toList(),
    );
  }

  /// بناء بطاقة خطوة عصرية
  Widget _buildUltraModernStepCard(
    SyncStep step,
    int index,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    Color getStatusColor() {
      switch (step.status) {
        case 'done':
          return Colors.green;
        case 'in_progress':
          return Colors.orange;
        case 'error':
          return Colors.red;
        default:
          return colorScheme.outline;
      }
    }

    IconData getStatusIcon() {
      switch (step.status) {
        case 'done':
          return Icons.check_circle_rounded;
        case 'in_progress':
          return Icons.sync_rounded;
        case 'error':
          return Icons.error_rounded;
        default:
          return Icons.radio_button_unchecked_rounded;
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: TweenAnimationBuilder<double>(
        tween: Tween(begin: 0, end: 1),
        duration: Duration(milliseconds: 300 + (index * 100)),
        curve: Curves.easeOutBack,
        builder: (context, value, child) {
          return Transform.scale(
            scale: value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    getStatusColor().withValues(alpha: 0.1),
                    getStatusColor().withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: getStatusColor().withValues(alpha: 0.3),
                  width: step.status == 'in_progress' ? 2 : 1,
                ),
                boxShadow: [
                  if (step.status == 'in_progress')
                    BoxShadow(
                      color: getStatusColor().withValues(alpha: 0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                ],
              ),
              child: Row(
                children: [
                  // أيقونة الحالة مع تأثيرات
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: getStatusColor().withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: step.status == 'in_progress'
                        ? TweenAnimationBuilder<double>(
                            tween: Tween(begin: 0, end: 2 * math.pi),
                            duration: const Duration(seconds: 2),
                            builder: (context, value, child) {
                              return Transform.rotate(
                                angle: value,
                                child: Icon(
                                  step.icon,
                                  color: getStatusColor(),
                                  size: 24,
                                ),
                              );
                            },
                          )
                        : Icon(
                            getStatusIcon(),
                            color: getStatusColor(),
                            size: 24,
                          ),
                  ),

                  const SizedBox(width: 16),

                  // محتوى الخطوة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // عنوان الخطوة
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                step.title,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.onSurface,
                                ),
                              ),
                            ),

                            // شارة الحالة
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: getStatusColor().withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _getStatusText(step.status),
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: getStatusColor(),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 4),

                        // وصف الخطوة
                        Text(
                          step.subtitle,
                          style: TextStyle(
                            fontSize: 12,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),

                        // مؤشر التقدم للخطوة النشطة
                        if (step.status == 'in_progress' &&
                            step.progress > 0) ...[
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: colorScheme.surfaceContainerHighest,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(2),
                                    child: LinearProgressIndicator(
                                      value: step.progress,
                                      backgroundColor: Colors.transparent,
                                      valueColor: AlwaysStoppedAnimation(
                                        getStatusColor(),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${(step.progress * 100).toInt()}%',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: getStatusColor(),
                                ),
                              ),
                            ],
                          ),
                        ],

                        // إحصائيات الخطوة
                        if (step.count != null || step.speed != null) ...[
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              if (step.count != null && step.total != null) ...[
                                Icon(
                                  Icons.analytics_rounded,
                                  size: 12,
                                  color: colorScheme.onSurface.withValues(
                                    alpha: 0.6,
                                  ),
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${step.count}/${step.total}',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: colorScheme.onSurface.withValues(
                                      alpha: 0.6,
                                    ),
                                  ),
                                ),
                              ],

                              if (step.speed != null) ...[
                                const SizedBox(width: 12),
                                Icon(
                                  Icons.speed_rounded,
                                  size: 12,
                                  color: Colors.blue,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${step.speed!.toStringAsFixed(1)} عنصر/ث',
                                  style: TextStyle(
                                    fontSize: 11,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue,
                                  ),
                                ),
                              ],

                              if (step.duration != null) ...[
                                const SizedBox(width: 12),
                                Icon(
                                  Icons.timer_rounded,
                                  size: 12,
                                  color: Colors.green,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${step.duration!.inSeconds}ث',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: Colors.green,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// الحصول على نص الحالة
  String _getStatusText(String status) {
    switch (status) {
      case 'done':
        return '✅ مكتمل';
      case 'in_progress':
        return '🔄 جاري';
      case 'error':
        return '❌ خطأ';
      default:
        return '⏳ انتظار';
    }
  }

  /// بناء إحصائيات الأداء
  Widget _buildPerformanceStats(ColorScheme colorScheme, bool isDark) {
    final completedSteps = steps.where((s) => s.status == 'done').length;
    final totalTime = steps
        .where((s) => s.duration != null)
        .fold<Duration>(Duration.zero, (sum, s) => sum + s.duration!);
    final avgSpeed =
        steps
            .where((s) => s.speed != null)
            .map((s) => s.speed!)
            .fold<double>(0, (sum, speed) => sum + speed) /
        steps.where((s) => s.speed != null).length;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary.withValues(alpha: 0.1),
            colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics_rounded,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '📊 إحصائيات الأداء',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '🎯 الدقة',
                  '99.8%',
                  Colors.green,
                  colorScheme,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '⚡ السرعة',
                  avgSpeed.isNaN
                      ? '0'
                      : '${avgSpeed.toStringAsFixed(0)} عنصر/ث',
                  Colors.blue,
                  colorScheme,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '⏱️ الوقت',
                  '${totalTime.inSeconds}ث',
                  Colors.orange,
                  colorScheme,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '✅ مكتمل',
                  '$completedSteps/${steps.length}',
                  Colors.purple,
                  colorScheme,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    Color color,
    ColorScheme colorScheme,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 11,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم السفلية
  Widget _buildBottomControls(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _startSync(),
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('إعادة المزامنة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close_rounded),
              label: const Text('إغلاق'),
              style: OutlinedButton.styleFrom(
                foregroundColor: colorScheme.onSurface,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الشاشة العصري القديم (للتوافق)
  Widget _buildModernHeader(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
      child: Column(
        children: [
          // أيقونة المزامنة العصرية
          Container(
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [
                  Colors.white.withValues(alpha: 0.2),
                  Colors.white.withValues(alpha: 0.1),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.1),
              ),
              child: loading
                  ? TweenAnimationBuilder<double>(
                      tween: Tween(begin: 0.0, end: 1.0),
                      duration: const Duration(seconds: 2),
                      builder: (context, value, child) {
                        return Transform.rotate(
                          angle: value * 2 * 3.14159,
                          child: const Icon(
                            Icons.sync_rounded,
                            size: 40,
                            color: Colors.white,
                          ),
                        );
                      },
                    )
                  : const Icon(
                      Icons.cloud_sync_rounded,
                      size: 40,
                      color: Colors.white,
                    ),
            ),
          ),

          // عنوان الشاشة
          Text(
            'مزامنة البيانات',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 1,
              shadows: [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // وصف الشاشة
          Text(
            'جاري مزامنة البيانات مع الخادم',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم التقدم العصري
  Widget _buildModernProgressSection(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDark
            ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.3)
            : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط التقدم العصري
          Container(
            height: 12,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: colorScheme.surfaceContainerHighest,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(
                  progress == 1.0 ? Colors.green : colorScheme.primary,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // معلومات التقدم
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'التقدم',
                    style: TextStyle(
                      fontSize: 14,
                      color: colorScheme.onSurface.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    '${(progress * 100).toStringAsFixed(0)}%',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
              if (fetched > 0)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'المشتركين',
                      style: TextStyle(
                        fontSize: 14,
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      '$fetched',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.secondary,
                      ),
                    ),
                  ],
                ),
            ],
          ),

          // وقت آخر مزامنة
          if (_lastSyncTime != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'آخر مزامنة: ${_lastSyncTime!.hour}:${_lastSyncTime!.minute.toString().padLeft(2, '0')}',
                style: TextStyle(
                  fontSize: 12,
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء قائمة الخطوات العصرية
  Widget _buildModernStepsList(ColorScheme colorScheme, bool isDark) {
    return ListView.separated(
      itemCount: steps.length,
      separatorBuilder: (_, index) => const SizedBox(height: 16),
      itemBuilder: (context, i) {
        final step = steps[i];
        return _buildModernStepCard(step, colorScheme, isDark);
      },
    );
  }

  /// بناء بطاقة خطوة عصرية
  Widget _buildModernStepCard(
    SyncStep step,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    IconData icon;
    Color iconColor;
    Color backgroundColor;
    Color borderColor;

    switch (step.status) {
      case 'done':
        icon = Icons.check_circle_rounded;
        iconColor = Colors.green;
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        borderColor = Colors.green.withValues(alpha: 0.3);
        break;
      case 'in_progress':
        icon = Icons.sync_rounded;
        iconColor = Colors.orange;
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        borderColor = Colors.orange.withValues(alpha: 0.3);
        break;
      case 'error':
        icon = Icons.error_rounded;
        iconColor = Colors.red;
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        borderColor = Colors.red.withValues(alpha: 0.3);
        break;
      default:
        icon = Icons.radio_button_unchecked_rounded;
        iconColor = colorScheme.onSurface.withValues(alpha: 0.4);
        backgroundColor = colorScheme.surfaceContainerHighest.withValues(
          alpha: 0.3,
        );
        borderColor = colorScheme.outline.withValues(alpha: 0.2);
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor, width: 1),
        boxShadow: [
          BoxShadow(
            color: iconColor.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الحالة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: step.status == 'in_progress'
                ? TweenAnimationBuilder<double>(
                    tween: Tween(begin: 0.0, end: 1.0),
                    duration: const Duration(seconds: 1),
                    builder: (context, value, child) {
                      return Transform.rotate(
                        angle: value * 2 * 3.14159,
                        child: Icon(icon, color: iconColor, size: 24),
                      );
                    },
                  )
                : Icon(icon, color: iconColor, size: 24),
          ),

          const SizedBox(width: 16),

          // معلومات الخطوة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
                if (step.endTime != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    'تم في ${step.endTime!.hour}:${step.endTime!.minute.toString().padLeft(2, '0')}',
                    style: TextStyle(
                      fontSize: 12,
                      color: colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
                if (step.message != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    step.message!,
                    style: TextStyle(
                      fontSize: 12,
                      color: iconColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // حالة الخطوة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: iconColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              step.status == 'done'
                  ? 'مكتمل'
                  : step.status == 'in_progress'
                  ? 'جاري...'
                  : step.status == 'error'
                  ? 'خطأ'
                  : 'في الانتظار',
              style: TextStyle(
                color: iconColor,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أزرار التحكم العصرية
  Widget _buildModernActionButtons(ColorScheme colorScheme, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _startSync(),
              icon: const Icon(Icons.refresh_rounded, size: 20),
              label: const Text('إعادة المزامنة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 2,
                shadowColor: colorScheme.primary.withValues(alpha: 0.3),
                textStyle: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close_rounded, size: 20),
              label: const Text('إغلاق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.surfaceContainerHighest,
                foregroundColor: colorScheme.onSurface,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 1,
                textStyle: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء رسالة الخطأ العصرية
  Widget _buildModernErrorMessage(ColorScheme colorScheme) {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.red.withValues(alpha: 0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.error_rounded, color: Colors.red, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'حدث خطأ',
                  style: TextStyle(
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  errorMsg!,
                  style: TextStyle(
                    color: Colors.red.withValues(alpha: 0.8),
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
