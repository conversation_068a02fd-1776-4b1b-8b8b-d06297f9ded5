import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'daily_sync_limits.dart';

/// خدمة الإشعارات الذكية للمزامنة
class SyncNotificationService {
  
  /// إشعار عند نجاح المزامنة اليدوية
  static Future<void> showManualSyncSuccessNotification(
    BuildContext context,
    Map<String, dynamic> syncResult,
  ) async {
    try {
      final stats = await DailySyncLimits.getTodayStats();
      final remaining = stats['manualRemaining'] as int;
      
      String message = '✅ تمت المزامنة الشاملة بنجاح!\n';
      
      if (remaining > 0) {
        message += '📊 يمكنك إجراء $remaining مزامنة أخرى اليوم';
      } else {
        message += '⏰ تم استنفاد المزامنات اليدوية اليوم\n';
        message += '🔄 المزامنة التلقائية ستعمل كالمعتاد';
      }
      
      // إضافة تفاصيل المزامنة
      if (syncResult.containsKey('compressionRatio')) {
        message += '\n📈 نسبة الضغط: ${syncResult['compressionRatio']}%';
      }
      
      _showSnackBar(context, message, Colors.green);
      
      debugPrint('📱 [SYNC_NOTIFICATION] إشعار نجاح المزامنة اليدوية');
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في إشعار نجاح المزامنة: $e');
    }
  }
  
  /// إشعار عند نجاح المزامنة التلقائية
  static Future<void> showAutoSyncSuccessNotification(
    BuildContext context,
    Map<String, dynamic> syncResult,
  ) async {
    try {
      final stats = await DailySyncLimits.getTodayStats();
      final manualRemaining = stats['manualRemaining'] as int;
      
      String message = '🤖 تمت المزامنة التلقائية بنجاح!\n';
      message += '📊 المزامنات اليدوية المتبقية: $manualRemaining/${stats['maxManualSyncs']}';
      
      _showSnackBar(context, message, Colors.blue);
      
      debugPrint('📱 [SYNC_NOTIFICATION] إشعار نجاح المزامنة التلقائية');
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في إشعار المزامنة التلقائية: $e');
    }
  }
  
  /// إشعار عند الوصول للحد الأقصى للمزامنات اليدوية
  static Future<void> showManualLimitReachedNotification(
    BuildContext context,
  ) async {
    try {
      final resetTime = DailySyncLimits.getNextResetTime();
      
      const message = '⚠️ تم استنفاد المزامنات اليدوية اليوم\n'
                     '🔄 المزامنة التلقائية ستعمل كالمعتاد\n'
                     '⏰ إعادة تعيين غداً في منتصف الليل';
      
      _showSnackBar(context, '$message\n⏱️ $resetTime', Colors.orange);
      
      debugPrint('📱 [SYNC_NOTIFICATION] إشعار الوصول للحد الأقصى');
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في إشعار الحد الأقصى: $e');
    }
  }
  
  /// إشعار عند فشل المزامنة
  static void showSyncFailureNotification(
    BuildContext context,
    String errorMessage,
    {bool isManual = true}
  ) {
    try {
      final syncType = isManual ? 'اليدوية' : 'التلقائية';
      final message = '❌ فشلت المزامنة $syncType\n$errorMessage';
      
      _showSnackBar(context, message, Colors.red);
      
      debugPrint('📱 [SYNC_NOTIFICATION] إشعار فشل المزامنة: $syncType');
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في إشعار فشل المزامنة: $e');
    }
  }
  
  /// إشعار معلومات المزامنة
  static Future<void> showSyncInfoNotification(
    BuildContext context,
  ) async {
    try {
      final stats = await DailySyncLimits.getTodayStats();
      
      String message = '📊 إحصائيات المزامنة اليوم:\n';
      message += '🖱️ يدوية: ${stats['manualSyncs']}/${stats['maxManualSyncs']}\n';
      message += '🤖 تلقائية: ${stats['autoSyncs']}/${stats['maxAutoSyncs']}\n';
      message += '⏱️ ${DailySyncLimits.getNextResetTime()}';
      
      _showSnackBar(context, message, Colors.blue);
      
      debugPrint('📱 [SYNC_NOTIFICATION] إشعار معلومات المزامنة');
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في إشعار معلومات المزامنة: $e');
    }
  }
  
  /// إشعار تحذيري قبل المزامنة الأخيرة
  static Future<void> showLastSyncWarningNotification(
    BuildContext context,
  ) async {
    try {
      const message = '⚠️ هذه آخر مزامنة يدوية متاحة اليوم\n'
                     '💡 تأكد من أن بياناتك محدثة قبل المزامنة';
      
      _showSnackBar(context, message, Colors.amber);
      
      debugPrint('📱 [SYNC_NOTIFICATION] إشعار تحذير المزامنة الأخيرة');
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في إشعار التحذير: $e');
    }
  }
  
  /// عرض SnackBar مخصص
  static void _showSnackBar(
    BuildContext context,
    String message,
    Color backgroundColor,
  ) {
    if (!context.mounted) return;
    
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            message,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          action: SnackBarAction(
            label: 'حسناً',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
            },
          ),
        ),
      );
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في عرض SnackBar: $e');
    }
  }
  
  /// حوار تأكيد المزامنة الأخيرة
  static Future<bool> showLastSyncConfirmationDialog(
    BuildContext context,
  ) async {
    try {
      return await showDialog<bool>(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('⚠️ المزامنة الأخيرة'),
            content: const Text(
              'هذه آخر مزامنة يدوية متاحة اليوم.\n'
              'هل تريد المتابعة؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('متابعة'),
              ),
            ],
          );
        },
      ) ?? false;
    } catch (e) {
      debugPrint('❌ [SYNC_NOTIFICATION] خطأ في حوار التأكيد: $e');
      return true; // السماح بالمزامنة في حالة الخطأ
    }
  }
  
  /// إشعار صامت للسجلات فقط
  static void logSyncAttempt(String syncType, bool success, String details) {
    final status = success ? 'نجحت' : 'فشلت';
    debugPrint('📝 [SYNC_LOG] المزامنة $syncType $status: $details');
  }
}
