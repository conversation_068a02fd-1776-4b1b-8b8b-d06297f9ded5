import 'package:flutter/material.dart';
import '../data/subscriber_model.dart';
import '../domain/subscribers_repository.dart';
import '../../../db_helper.dart';
import '../../../auto_notifications_screen.dart' as auto_notify;
import 'dart:convert';
import '../../../boards_list_screen.dart'; // لاستخدام encryptWithOpenSSL
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import '../../../utils/api_helper.dart' as api_helper;
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:crypto/crypto.dart';
import '../data/transaction_model.dart' as MyTrans;
import 'package:url_launcher/url_launcher.dart';

/// نتيجة العمليات المالية
class FinancialResult {
  final double newDebt;
  final double newWallet;
  final double debtAdded;
  final double walletUsed;
  final double excessToWallet;
  final String description;

  FinancialResult({
    required this.newDebt,
    required this.newWallet,
    required this.debtAdded,
    required this.walletUsed,
    required this.excessToWallet,
    required this.description,
  });
}

class RenewSubscriptionBottomSheet extends StatefulWidget {
  final Subscriber subscriber;
  final SubscribersRepository repository;
  const RenewSubscriptionBottomSheet({
    super.key,
    required this.subscriber,
    required this.repository,
  });

  @override
  State<RenewSubscriptionBottomSheet> createState() =>
      _RenewSubscriptionBottomSheetState();
}

class _RenewSubscriptionBottomSheetState
    extends State<RenewSubscriptionBottomSheet> {
  bool isLoading = false;
  String? errorMsg;
  bool isPaid = false;
  bool isPaidArrived = true; // متغير لتحديد إذا كان المبلغ واصل
  bool isAmountEditable = false; // للتحكم في تعديل المبلغ المدفوع
  final TextEditingController paidAmountController =
      TextEditingController(); // المبلغ المدفوع فعلياً
  final TextEditingController notesController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController payloadController =
      TextEditingController(); // حقل إدخال البايلود
  List<Map<String, dynamic>> subscriptions = [];
  Map<String, dynamic>? selectedBundle;

  String? _decryptedText; // متغير لتخزين النص المفكوك

  @override
  void initState() {
    super.initState();
    _loadSubscriptions();
    isPaidArrived = false; // القيمة الافتراضية دائماً غير مؤشر
    _initBundleFromDB();
  }

  /// الحصول على سعر البيع الفعال (مخصص أو افتراضي)
  double _getEffectiveSellPrice(Map<String, dynamic> subscription) {
    final customPrice = subscription['custom_sell_price'];
    if (customPrice != null && customPrice > 0) {
      return (customPrice is num)
          ? customPrice.toDouble()
          : double.tryParse(customPrice.toString()) ?? 0.0;
    }

    final sellPrice = subscription['sellPrice'];
    if (sellPrice != null) {
      return (sellPrice is num)
          ? sellPrice.toDouble()
          : double.tryParse(sellPrice.toString()) ?? 0.0;
    }

    return 0.0;
  }

  /// التحقق من وجود سعر مخصص للباقة المختارة
  bool _isCustomPrice() {
    if (selectedBundle == null) return false;
    final customPrice = selectedBundle!['custom_sell_price'];
    return customPrice != null && customPrice > 0;
  }

  /// بناء نص التسمية للحقل
  String _buildPriceLabelText() {
    if (selectedBundle?['name'] != null) {
      final priceType = _isCustomPrice() ? ' (سعر مخصص)' : ' (سعر افتراضي)';
      return 'سعر الباقة (${selectedBundle?['name']})$priceType';
    }
    return 'سعر الباقة';
  }

  /// بناء نص المساعدة للحقل
  String? _buildPriceHelperText() {
    if (!_isCustomPrice()) return null;

    final defaultPrice = selectedBundle?['sellPrice'];
    if (defaultPrice != null) {
      return 'السعر الافتراضي: $defaultPrice د.ع';
    }
    return 'تم تخصيص سعر لهذه الباقة';
  }

  /// فحص رصيد اللوحة قبل التجديد
  Future<bool> _checkBoardBalance(double subscriptionPrice) async {
    try {
      // جلب رصيد اللوحة النشطة
      final balanceData = await DBHelper.instance.getActiveBoardBalance();

      if (balanceData == null) {
        setState(() {
          errorMsg = 'لم يتم العثور على بيانات رصيد اللوحة';
        });
        return false;
      }

      final currentBalance = balanceData['balance'] as double? ?? 0.0;
      debugPrint(
        '[RENEW] فحص الرصيد: الرصيد الحالي = $currentBalance، سعر الباقة = $subscriptionPrice',
      );

      // فحص إذا كان الرصيد أقل من سعر الباقة
      if (currentBalance < subscriptionPrice) {
        final balanceText =
            balanceData['balance_text'] ?? '$currentBalance د.ع';
        final boardName = balanceData['board_name'] ?? 'اللوحة';
        setState(() {
          errorMsg =
              '⚠️ الرصيد غير كافي للتجديد\n\n'
              '💰 الرصيد الحالي في $boardName: $balanceText\n'
              '💳 سعر الباقة المطلوبة: ${subscriptionPrice.toStringAsFixed(0)} د.ع\n'
              '➕ المطلوب إضافة: ${(subscriptionPrice - currentBalance).toStringAsFixed(0)} د.ع\n\n'
              'يرجى شحن الرصيد أولاً ثم المحاولة مرة أخرى';
        });
        return false;
      }

      // الرصيد كافي للتجديد
      final balanceText = balanceData['balance_text'] ?? '$currentBalance د.ع';
      final boardName = balanceData['board_name'] ?? 'اللوحة';
      debugPrint(
        '[RENEW] ✅ الرصيد كافي للتجديد - الرصيد: $balanceText، سعر الباقة: ${subscriptionPrice.toStringAsFixed(0)} د.ع',
      );

      return true;
    } catch (e) {
      debugPrint('[RENEW] خطأ في فحص الرصيد: $e');
      setState(() {
        errorMsg = 'خطأ في فحص رصيد اللوحة: $e';
      });
      return false;
    }
  }

  Future<void> _initBundleFromDB() async {
    final subId = widget.subscriber.subscriptionId?.toString();
    if (subId != null && subId.isNotEmpty) {
      final bundle = await DBHelper.instance.getSubscriptionById(subId);
      if (bundle != null) {
        setState(() {
          selectedBundle = bundle;
          // استخدام السعر المخصص إذا كان موجوداً
          final effectivePrice = _getEffectiveSellPrice(bundle);
          priceController.text = effectivePrice.toString();
        });
        return;
      }
    }

    // احتياطي: البحث بالاسم إذا لم يوجد بالـ ID
    final bundles = await DBHelper.instance.getAllSubscriptions();
    final matchingBundle = bundles.firstWhere(
      (bundle) => bundle['name'] == widget.subscriber.subscriptionType,
      orElse: () => <String, dynamic>{},
    );

    if (matchingBundle.isNotEmpty) {
      setState(() {
        selectedBundle = matchingBundle;
        // استخدام السعر المخصص إذا كان موجوداً
        final effectivePrice = _getEffectiveSellPrice(matchingBundle);
        priceController.text = effectivePrice.toString();
      });
      return;
    }

    // احتياطي أخير: استخدم بيانات المشترك إذا لم توجد الباقة في قاعدة البيانات
    setState(() {
      selectedBundle = {
        'id': widget.subscriber.subscriptionId,
        'name': widget.subscriber.subscriptionType,
        'sellPrice': widget.subscriber.subscriptionPrice,
      };
      priceController.text = widget.subscriber.subscriptionPrice.toString();
    });
  }

  Future<void> _loadSubscriptions() async {
    final list = await DBHelper.instance.getAllSubscriptions();
    setState(() {
      subscriptions = list;
    });
  }

  @override
  void dispose() {
    notesController.dispose();
    priceController.dispose();
    payloadController.dispose(); // التخلص من الكنترولر
    super.dispose();
  }

  /// تجديد المشترك اليدوي محلياً (إضافة 30 يوم)
  Future<void> _renewManualSubscriberLocally() async {
    try {
      print('[RENEW_LOCAL] بدء التجديد المحلي للمشترك اليدوي...');

      // فحص الرصيد أولاً للمشتركين اليدويين أيضاً
      final subscriptionPrice = double.tryParse(priceController.text) ?? 0.0;
      final balanceCheckResult = await _checkBoardBalance(subscriptionPrice);
      if (!balanceCheckResult) {
        return; // تم عرض رسالة الخطأ في الدالة
      }

      // حساب تاريخ الانتهاء الجديد (إضافة 30 يوم)
      final currentEndDate = widget.subscriber.endDate;
      final now = DateTime.now();

      // إذا كان الاشتراك منتهي، نبدأ من اليوم
      // إذا كان ما زال فعال، نضيف 30 يوم على تاريخ الانتهاء الحالي
      final newEndDate = currentEndDate.isAfter(now)
          ? currentEndDate.add(const Duration(days: 30))
          : now.add(const Duration(days: 30));

      print('[RENEW_LOCAL] تاريخ الانتهاء الحالي: $currentEndDate');
      print('[RENEW_LOCAL] تاريخ الانتهاء الجديد: $newEndDate');

      // تحديث سعر الاشتراك إذا تم تغييره
      double localSubscriptionPrice = widget.subscriber.subscriptionPrice;
      if (priceController.text.isNotEmpty) {
        localSubscriptionPrice =
            double.tryParse(priceController.text) ?? localSubscriptionPrice;
      }

      // تحديث نوع الاشتراك إذا تم تغييره
      String subscriptionType = widget.subscriber.subscriptionType;
      if (selectedBundle != null) {
        subscriptionType = selectedBundle!['name'] ?? subscriptionType;
      }

      // تحديث المشترك
      final updatedSubscriber = widget.subscriber.copyWith(
        endDate: newEndDate,
        subscriptionPrice: localSubscriptionPrice,
        subscriptionType: subscriptionType,
      );

      await widget.repository.updateSubscriber(updatedSubscriber);
      print('[RENEW_LOCAL] تم تحديث المشترك في قاعدة البيانات');

      // إدارة الدفعات والديون بالنظام الاحترافي
      final financialResult = await _processRenewalFinancials(
        localSubscriptionPrice,
      );

      // تطبيق النتائج المالية
      final financiallyUpdatedSubscriber = widget.subscriber.copyWith(
        totalDebt: financialResult.newDebt,
        walletBalance: financialResult.newWallet,
      );
      await widget.repository.updateSubscriber(financiallyUpdatedSubscriber);

      print('[RENEW_FINANCIAL] ${financialResult.description}');

      // إضافة سجل عملية التجديد
      await DBHelper.instance.insertTransaction(
        MyTrans.Transaction(
          type: MyTrans.TransactionType.renewal,
          description:
              'تجديد محلي لمشترك يدوي - $subscriptionType بسعر ${subscriptionPrice.toStringAsFixed(0)} د.ع${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
          date: DateTime.now(),
          subscriberId: widget.subscriber.id,
        ),
      );

      print('[RENEW_LOCAL] تم إكمال التجديد المحلي بنجاح');

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تجديد الاشتراك محلياً بنجاح (30 يوم)'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      print('[RENEW_LOCAL][ERROR] خطأ في التجديد المحلي: $e');
      setState(() {
        errorMsg = 'خطأ في التجديد المحلي: $e';
      });
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  /// النظام المالي الاحترافي للتجديد
  Future<FinancialResult> _processRenewalFinancials(
    double subscriptionPrice,
  ) async {
    final currentDebt = widget.subscriber.totalDebt;
    final currentWallet = widget.subscriber.walletBalance;

    if (!isPaidArrived) {
      // السيناريو 1: لم يتم استلام المبلغ - إضافة كدين
      return FinancialResult(
        newDebt: currentDebt + subscriptionPrice,
        newWallet: currentWallet,
        debtAdded: subscriptionPrice,
        walletUsed: 0.0,
        excessToWallet: 0.0,
        description:
            'تم إضافة مبلغ الباقة ${subscriptionPrice.toStringAsFixed(0)} د.ع كدين',
      );
    }

    final paidAmount =
        double.tryParse(paidAmountController.text) ?? subscriptionPrice;

    // السيناريو 2: تم استلام مبلغ
    return _calculateSmartFinancials(
      subscriptionPrice: subscriptionPrice,
      paidAmount: paidAmount,
      currentDebt: currentDebt,
      currentWallet: currentWallet,
    );
  }

  /// حساب ذكي للعمليات المالية
  FinancialResult _calculateSmartFinancials({
    required double subscriptionPrice,
    required double paidAmount,
    required double currentDebt,
    required double currentWallet,
  }) {
    double newDebt = currentDebt;
    double newWallet = currentWallet;
    double walletUsed = 0.0;
    double debtAdded = 0.0;
    double excessToWallet = 0.0;
    String description = '';

    // الخطوة 1: استخدام المحفظة أولاً لسداد الديون الموجودة
    if (currentDebt > 0 && currentWallet > 0) {
      walletUsed = currentWallet.clamp(0.0, currentDebt);
      newDebt -= walletUsed;
      newWallet -= walletUsed;
      description +=
          'تم استخدام ${walletUsed.toStringAsFixed(0)} د.ع من المحفظة لسداد الديون. ';
    }

    // الخطوة 2: معالجة المبلغ المدفوع
    if (paidAmount >= subscriptionPrice) {
      // المبلغ يكفي للاشتراك
      final excess = paidAmount - subscriptionPrice;

      if (newDebt > 0 && excess > 0) {
        // استخدام الفائض لسداد الديون المتبقية
        final debtPayment = excess.clamp(0.0, newDebt);
        newDebt -= debtPayment;
        final remainingExcess = excess - debtPayment;

        if (remainingExcess > 0) {
          // إضافة الباقي للمحفظة
          newWallet += remainingExcess;
          excessToWallet = remainingExcess;
        }

        description +=
            'تم دفع ${subscriptionPrice.toStringAsFixed(0)} د.ع للاشتراك، ';
        description += 'سداد ${debtPayment.toStringAsFixed(0)} د.ع من الديون';
        if (excessToWallet > 0) {
          description +=
              '، إضافة ${excessToWallet.toStringAsFixed(0)} د.ع للمحفظة';
        }
      } else if (excess > 0) {
        // لا توجد ديون، إضافة الفائض للمحفظة
        newWallet += excess;
        excessToWallet = excess;
        description +=
            'تم دفع ${subscriptionPrice.toStringAsFixed(0)} د.ع للاشتراك، ';
        description += 'إضافة ${excess.toStringAsFixed(0)} د.ع للمحفظة';
      } else {
        description +=
            'تم دفع ${subscriptionPrice.toStringAsFixed(0)} د.ع للاشتراك بالضبط';
      }
    } else {
      // المبلغ أقل من سعر الباقة
      final shortage = subscriptionPrice - paidAmount;

      if (newWallet >= shortage) {
        // المحفظة تغطي النقص
        newWallet -= shortage;
        walletUsed += shortage;
        description += 'تم دفع ${paidAmount.toStringAsFixed(0)} د.ع، ';
        description +=
            'استخدام ${shortage.toStringAsFixed(0)} د.ع من المحفظة لتغطية النقص';
      } else if (newWallet > 0) {
        // المحفظة تغطي جزء من النقص
        final remainingShortage = shortage - newWallet;
        walletUsed += newWallet;
        newWallet = 0.0;
        newDebt += remainingShortage;
        debtAdded = remainingShortage;
        description += 'تم دفع ${paidAmount.toStringAsFixed(0)} د.ع، ';
        description +=
            'استخدام ${walletUsed.toStringAsFixed(0)} د.ع من المحفظة، ';
        description += 'إضافة ${remainingShortage.toStringAsFixed(0)} د.ع كدين';
      } else {
        // لا توجد محفظة، إضافة النقص كدين
        newDebt += shortage;
        debtAdded = shortage;
        description += 'تم دفع ${paidAmount.toStringAsFixed(0)} د.ع، ';
        description += 'إضافة ${shortage.toStringAsFixed(0)} د.ع كدين';
      }
    }

    return FinancialResult(
      newDebt: newDebt,
      newWallet: newWallet,
      debtAdded: debtAdded,
      walletUsed: walletUsed,
      excessToWallet: excessToWallet,
      description: description,
    );
  }

  /// ترجع تاريخ الانتهاء الجديد من الموقع أو null عند الفشل
  Future<DateTime?> _renewOnServer({
    required String username,
    int? profileId, // جعل profileId اختياري
    required DateTime startDate,
    required String token,
    required int userId, // إضافة userId
  }) async {
    print('[RENEW] بدء عملية التجديد على الموقع...');

    // جلب اللوحة النشطة للحصول على الدومين
    final board = await DBHelper.instance.getActiveBoard();
    if (board == null ||
        board['url'] == null ||
        board['url'].toString().trim().isEmpty) {
      print('[RENEW][ERROR] لا توجد لوحة نشطة أو الدومين فارغ');
      return null;
    }

    final domain = board['url'].toString().trim();
    print('[RENEW][DEBUG] الدومين المستخدم: $domain');
    print('[RENEW][DEBUG] --- تفاصيل عملية التجديد ---');
    print('[RENEW][DEBUG] user_id: $userId');
    print('[RENEW][DEBUG] username: $username');
    print('[RENEW][DEBUG] profile_id: $profileId');
    print('[RENEW][DEBUG] start_date: ${startDate.toIso8601String()}');
    print('[RENEW][DEBUG] activation_method: Manager Balance');
    // توليد transaction_id بتنسيق صحيح
    final now = DateTime.now();
    final transactionId =
        '[#${now.millisecondsSinceEpoch.toString().substring(7)}]';

    // بناء البايلود الاحترافي
    final Map<String, dynamic> data = {
      'method': 'credit',
      'pin': '',
      'user_id': userId,
      'money_collected': 1,
      'comments': null,
      'user_price': int.tryParse(priceController.text) ?? 0,
      'issue_invoice': 0,
      'transaction_id': transactionId,
      'activation_units': 1,
      'username': username,
      'start_date': startDate
          .toIso8601String()
          .replaceFirst('T', ' ')
          .split('.')
          .first,
      'activation_method': 'Manager Balance',
    };

    // إضافة profile_id فقط إذا تم تمريره
    if (profileId != null) {
      data['profile_id'] = profileId;
    }
    print('[RENEW][DEBUG] JSON قبل التشفير: ${jsonEncode(data)}');
    final payload = encryptWithOpenSSL(
      jsonEncode(data),
      'abcdefghijuklmno0123456789012345',
    );
    print('[RENEW][DEBUG] payload المشفر: $payload');
    final dio = Dio();
    (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
        (client) {
          client.badCertificateCallback = (cert, host, port) {
            print('[RENEW][SSL] تجاوز تحقق الشهادة لـ $host:$port');
            return true;
          };
          return client;
        };
    // بناء الرابط الديناميكي
    final apiUrl = domain.startsWith('http')
        ? '$domain/admin/api/index.php/api/user/activate'
        : 'https://$domain/admin/api/index.php/api/user/activate';

    final headers = {
      'authorization': 'Bearer $token',
      'content-type': 'application/json',
      'accept': 'application/json, text/plain, */*',
      'origin': domain.startsWith('http') ? domain : 'https://$domain',
      'referer': domain.startsWith('http') ? '$domain/' : 'https://$domain/',
      'user-agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'accept-language': 'en,en-US;q=0.9,ar;q=0.8',
      'host': domain.replaceAll(RegExp(r'https?://'), ''),
      'sec-ch-ua':
          '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
    };
    print('[RENEW][DEBUG] الرابط المستخدم: $apiUrl');
    print('[RENEW][DEBUG] headers المرسلة: ${headers.toString()}');
    final response = await dio.post(
      apiUrl,
      data: jsonEncode({'payload': payload}),
      options: Options(headers: headers, validateStatus: (status) => true),
    );
    print('[RENEW][DEBUG] كود الاستجابة: ${response.statusCode}');
    print('[RENEW][DEBUG] محتوى الرد: ${response.data}');
    if (response.statusCode == 200) {
      if (response.data == null ||
          (response.data is String && response.data.trim().isEmpty)) {
        print('[RENEW][ERROR] الاستجابة من السيرفر فارغة!');
        return null;
      }
      final resp = response.data is String
          ? jsonDecode(response.data)
          : response.data;
      print('[RENEW] الاستجابة المفككة: $resp');
      // محاولة استخراج تاريخ الانتهاء الجديد من الرد
      if (resp is Map<String, dynamic>) {
        final endDateStr =
            resp['end_date'] ?? resp['expire_date'] ?? resp['expireDate'];
        if (endDateStr != null &&
            endDateStr is String &&
            endDateStr.isNotEmpty) {
          try {
            final endDate = DateTime.parse(endDateStr);
            return endDate;
          } catch (e) {
            print('[RENEW][ERROR] فشل تحويل تاريخ الانتهاء: $e');
          }
        }
      }
      return null;
    } else {
      print('[RENEW][ERROR] كود استجابة غير متوقع: ${response.statusCode}');
    }
    return null;
  }

  /// حساب تاريخ الانتهاء الجديد بناءً على الباقة المختارة
  DateTime _calculateNewEndDate() {
    final currentEndDate = widget.subscriber.endDate;
    final now = DateTime.now();

    // الحصول على مدة الباقة (افتراضي 30 يوم)
    int packageDays = 30;

    if (selectedBundle != null) {
      // محاولة الحصول على المدة من بيانات الباقة
      packageDays = selectedBundle!['duration_days'] ?? 30;

      // إذا لم تكن متوفرة، استخدم منطق ذكي بناءً على اسم الباقة
      if (packageDays == 30) {
        packageDays = _extractDaysFromPackageName(
          selectedBundle!['name'] ?? '',
        );
      }
    }

    // إذا كان الاشتراك ما زال فعال، أضف على التاريخ الحالي
    if (currentEndDate.isAfter(now)) {
      return currentEndDate.add(Duration(days: packageDays));
    } else {
      // إذا كان منتهي، ابدأ من اليوم
      return now.add(Duration(days: packageDays));
    }
  }

  /// استخراج عدد الأيام من اسم الباقة بشكل ذكي
  int _extractDaysFromPackageName(String packageName) {
    final name = packageName.toLowerCase();

    // أنماط شائعة للباقات
    if (name.contains('سنة') ||
        name.contains('year') ||
        name.contains('سنوي')) {
      return 365;
    } else if (name.contains('6') &&
        (name.contains('شهر') || name.contains('month'))) {
      return 180;
    } else if (name.contains('3') &&
        (name.contains('شهر') || name.contains('month'))) {
      return 90;
    } else if (name.contains('شهر') ||
        name.contains('month') ||
        name.contains('شهري')) {
      return 30;
    } else if (name.contains('أسبوع') || name.contains('week')) {
      return 7;
    }

    // افتراضي
    return 30;
  }

  Future<String?> _getValidToken() async {
    // جلب بيانات اللوحة النشطة
    final board = await DBHelper.instance.getActiveBoard();
    if (board == null) return null;

    String? token = board['token'];

    // فحص صالحية التوكن أولاً
    if (!api_helper.isTokenValid(token)) {
      print('[RENEW] التوكن غير صالح، محاولة تسجيل دخول جديد...');
      // تسجيل الدخول وجلب التوكن
      final loginResult = await api_helper.tryConnect(board);
      if (loginResult['success'] == true && loginResult['token'] != null) {
        token = loginResult['token'];
        // تحديث التوكن في قاعدة البيانات
        await DBHelper.instance.updateBoard(board['id'], {
          ...board,
          'token': token,
        });
        print('[RENEW] تم تحديث التوكن بنجاح');
      } else {
        print('[RENEW] فشل في تسجيل الدخول وجلب توكن جديد');
      }
    } else {
      print('[RENEW] التوكن الحالي صالح');
    }
    return token;
  }

  /// دالة تجديد سريعة تركز على نجاح العملية فقط
  Future<bool> _renewOnServerQuick({
    required String username,
    required DateTime startDate,
    required String? token,
    required int userId,
  }) async {
    if (token == null) {
      print('[RENEW][ERROR] لا يوجد توكن صالح');
      return false;
    }

    final board = await DBHelper.instance.getActiveBoard();
    if (board == null) {
      print('[RENEW][ERROR] لا توجد لوحة نشطة');
      return false;
    }

    final domain = board['url'].toString().trim();
    print('[RENEW][DEBUG] الدومين المستخدم: $domain');
    print('[RENEW][DEBUG] --- تفاصيل عملية التجديد ---');
    print('[RENEW][DEBUG] user_id: $userId');
    print('[RENEW][DEBUG] username: $username');
    print('[RENEW][DEBUG] start_date: ${startDate.toIso8601String()}');

    // توليد transaction_id بتنسيق صحيح
    final now = DateTime.now();
    final transactionId =
        '[#${now.millisecondsSinceEpoch.toString().substring(7)}]';

    // بناء البايلود الكامل بالتنسيق الصحيح (حسب المعايير الأصلية)
    final Map<String, dynamic> data = {
      'method': 'credit',
      'pin': '',
      'user_id': userId,
      'money_collected': 1,
      'comments': null,
      'user_price': int.tryParse(priceController.text) ?? 0,
      'issue_invoice': 0,
      'transaction_id': transactionId,
      'activation_units': 1,
      'username': username,
      'start_date': startDate
          .toIso8601String()
          .replaceFirst('T', ' ')
          .split('.')
          .first,
      'activation_method': 'Manager Balance',
    };

    // إضافة profile_id (إما الباقة الجديدة أو الحالية)
    if (selectedBundle != null &&
        selectedBundle!['id'] != widget.subscriber.subscriptionType) {
      data['profile_id'] = selectedBundle!['id'];
    } else {
      // استخدام الباقة الحالية إذا لم يتم تغييرها
      data['profile_id'] = widget.subscriber.subscriptionId ?? 0;
    }

    print('[RENEW][DEBUG] البايلود المرسل: $data');
    print('[RENEW][DEBUG] JSON قبل التشفير: ${jsonEncode(data)}');

    try {
      // استخدام نفس منطق الدالة الأصلية مع تبسيط
      final payload = encryptWithOpenSSL(
        jsonEncode(data),
        'abcdefghijuklmno0123456789012345',
      );
      print('[RENEW][DEBUG] payload المشفر: $payload');

      final dio = Dio();
      (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
          (client) {
            client.badCertificateCallback = (cert, host, port) => true;
            return client;
          };

      final apiUrl = domain.startsWith('http')
          ? '$domain/admin/api/index.php/api/user/activate'
          : 'https://$domain/admin/api/index.php/api/user/activate';

      final response = await dio.post(
        apiUrl,
        data: jsonEncode({'payload': payload}),
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
            'Accept': 'application/json, text/plain, */*',
            'User-Agent':
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Origin': domain.startsWith('http') ? domain : 'https://$domain',
            'Referer':
                '${domain.startsWith('http') ? domain : 'https://$domain'}/',
          },
          validateStatus: (status) => true,
        ),
      );

      print('[RENEW][DEBUG] كود الاستجابة: ${response.statusCode}');
      print('[RENEW][DEBUG] محتوى الرد: ${response.data}');

      if (response.statusCode == 200) {
        // التحقق من وجود محتوى في الرد
        if (response.data == null ||
            (response.data is String &&
                response.data.toString().trim().isEmpty)) {
          print('[RENEW][SUCCESS] تم التجديد بنجاح (رد فارغ من السيرفر)');
          return true; // السيرفر يرد بـ 200 ومحتوى فارغ يعني نجاح العملية
        }

        try {
          // تحويل الاستجابة إلى Map إذا كانت String
          final resp = response.data is String
              ? jsonDecode(response.data)
              : response.data;

          print('[RENEW][DEBUG] الاستجابة المفككة: $resp');

          // تحديث للتحقق من الاستجابة الصحيحة حسب المعايير الجديدة
          if (resp is Map<String, dynamic> &&
              resp['status'] == 200 &&
              resp['message'] == 'rsp_success') {
            print('[RENEW][SUCCESS] تم التجديد بنجاح');
            return true;
          } else {
            print(
              '[RENEW][ERROR] فشل التجديد: ${resp is Map ? resp['message'] ?? 'خطأ غير معروف' : 'استجابة غير صالحة'}',
            );
            return false;
          }
        } catch (e) {
          print('[RENEW][ERROR] خطأ في تحليل الاستجابة: $e');
          // إذا كان كود الاستجابة 200 لكن فشل تحليل JSON، نعتبرها نجاح
          print(
            '[RENEW][SUCCESS] تم التجديد بنجاح (خطأ في تحليل JSON لكن كود 200)',
          );
          return true;
        }
      } else {
        print(
          '[RENEW][ERROR] استجابة غير صالحة من السيرفر - كود: ${response.statusCode}',
        );
        return false;
      }
    } catch (e) {
      print('[RENEW][ERROR] خطأ في الاتصال: $e');
      return false;
    }
  }

  /// جلب بيانات المشترك المحدثة بعد التجديد الناجح
  Future<Map<String, dynamic>?> _fetchUpdatedSubscriberData({
    required String? token,
    required int userId,
  }) async {
    if (token == null) {
      print('[FETCH][ERROR] لا يوجد توكن صالح');
      return null;
    }

    final board = await DBHelper.instance.getActiveBoard();
    if (board == null) {
      print('[FETCH][ERROR] لا توجد لوحة نشطة');
      return null;
    }

    final domain = board['url'].toString().trim();

    try {
      final dio = Dio();
      (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
          (client) {
            client.badCertificateCallback = (cert, host, port) => true;
            return client;
          };

      final fetchUrl = domain.startsWith('http')
          ? '$domain/admin/api/index.php/api/user/$userId'
          : 'https://$domain/admin/api/index.php/api/user/$userId';

      print('[FETCH][DEBUG] جلب البيانات من: $fetchUrl');

      final response = await dio.get(
        fetchUrl,
        options: Options(
          headers: {
            'Authorization': 'Bearer $token',
            'Accept': 'application/json, text/plain, */*',
          },
        ),
      );

      print('[FETCH][DEBUG] استجابة جلب البيانات: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        // تحويل الاستجابة إلى Map إذا كانت String
        final resp = response.data is String
            ? jsonDecode(response.data)
            : response.data;

        if (resp is Map<String, dynamic> &&
            resp['status'] == 200 &&
            resp['data'] != null) {
          print('[FETCH][SUCCESS] تم جلب البيانات المحدثة بنجاح');
          return resp['data'] as Map<String, dynamic>;
        } else {
          print(
            '[FETCH][ERROR] استجابة غير صالحة: ${resp is Map ? resp['status'] : 'ليس Map'}',
          );
          return null;
        }
      } else {
        print(
          '[FETCH][ERROR] فشل في الحصول على استجابة صالحة - كود: ${response.statusCode}',
        );
        return null;
      }
    } catch (e) {
      print('[FETCH][ERROR] خطأ في جلب البيانات: $e');
      return null;
    }
  }

  Future<void> _renew() async {
    setState(() {
      isLoading = true;
      errorMsg = null;
    });
    try {
      print('[RENEW] بدء منطق التجديد...');

      // فحص الرصيد أولاً قبل التجديد
      final subscriptionPrice = double.tryParse(priceController.text) ?? 0.0;
      final balanceCheckResult = await _checkBoardBalance(subscriptionPrice);
      if (!balanceCheckResult) {
        return; // تم عرض رسالة الخطأ في الدالة
      }

      // فحص نوع المشترك أولاً
      if (widget.subscriber.isManualSubscriber) {
        print('[RENEW] مشترك يدوي - تنفيذ التجديد المحلي...');
        await _renewManualSubscriberLocally();
        return;
      }

      // للمشتركين الآخرين (SAS) - التجديد عبر السيرفر
      print('[RENEW] مشترك SAS - بدء منطق التجديد عبر السيرفر...');
      final token = await _getValidToken() ?? '';
      print('[RENEW] التوكن المستخدم: $token');
      if (token.isEmpty) {
        setState(() {
          errorMsg = 'لم يتم العثور على توكن صالح. تأكد من بيانات الدخول.';
        });
        print('[RENEW][ERROR] لم يتم العثور على توكن صالح.');
        return;
      }
      final oldProfileId = widget.subscriber.subscriptionId?.toString() ?? '';
      final newProfileId = selectedBundle?['id']?.toString() ?? '';
      final isProfileChanged =
          oldProfileId != newProfileId && newProfileId.isNotEmpty;
      final isExpired = widget.subscriber.endDate.isBefore(DateTime.now());
      print(
        '[RENEW] isProfileChanged: $isProfileChanged, isExpired: $isExpired',
      );
      // منطق تغيير الباقة
      if (isProfileChanged) {
        if (!isExpired) {
          setState(() {
            errorMsg =
                'لا يمكن تغيير نوع الباقة إلا بعد انتهاء الاشتراك الحالي.';
          });
          print('[RENEW][ERROR] محاولة تغيير باقة لمشترك فعال');
          return;
        }
        // إذا كان الاشتراك منتهي، نفذ طلب تغيير الباقة أولاً
        // جلب بيانات اللوحة النشطة
        final board = await DBHelper.instance.getActiveBoard();
        if (board == null) {
          setState(() {
            errorMsg = 'لم يتم العثور على بيانات اللوحة النشطة.';
          });
          print('[RENEW][ERROR] لم يتم العثور على بيانات اللوحة النشطة.');
          return;
        }
        // إذا كان الاشتراك منتهي، نفذ طلب تغيير الباقة أولاً
        final changeResult = await api_helper.changeProfileOnServer(
          board: board,
          username: widget.subscriber.user,
          profileId: int.tryParse(newProfileId) ?? 0,
          userId:
              widget.subscriber.remoteId ?? 0, // تمرير رقم المشترك من السيرفر
        );
        if (!(changeResult['success'] ?? false)) {
          setState(() {
            errorMsg =
                changeResult['message'] ?? 'فشل تغيير الباقة على السيرفر.';
          });
          print(
            '[RENEW][ERROR] فشل تغيير الباقة على السيرفر: \\${changeResult['message']}',
          );
          return;
        }
        print('[RENEW] تم تغيير الباقة بنجاح، جاري التجديد...');
      }
      // تنفيذ طلب التجديد دائماً إذا لم يكن هناك تغيير باقة أو إذا تم تغييرها بنجاح
      final renewalSuccess = await _renewOnServerQuick(
        username: widget.subscriber.user,
        startDate: DateTime.now(),
        token: token,
        userId: widget.subscriber.remoteId ?? 0,
      );

      if (!renewalSuccess) {
        setState(() {
          errorMsg = 'فشل في تجديد الاشتراك على السيرفر';
        });
        return;
      }

      print('[RENEW] تم التجديد بنجاح، جاري جلب البيانات المحدثة...');

      // جلب بيانات المشترك المحدثة من السيرفر بعد التجديد الناجح
      final updatedData = await _fetchUpdatedSubscriberData(
        token: token,
        userId: widget.subscriber.remoteId ?? 0,
      );

      DateTime finalEndDate;

      if (updatedData != null && updatedData['expiration'] != null) {
        // استخدام تاريخ الانتهاء من السيرفر (الأدق)
        try {
          finalEndDate = DateTime.parse(updatedData['expiration']);
          print(
            '[RENEW] تم الحصول على تاريخ الانتهاء من السيرفر: $finalEndDate',
          );
        } catch (e) {
          print('[RENEW] خطأ في تحليل تاريخ الانتهاء من السيرفر: $e');
          finalEndDate = _calculateNewEndDate();
        }
      } else {
        // fallback للحساب المحلي
        print(
          '[RENEW] لم يتم الحصول على البيانات من السيرفر، استخدام الحساب المحلي',
        );
        finalEndDate = _calculateNewEndDate();
      }

      // تحديث المشترك محلياً بالتاريخ الصحيح
      final updated = widget.subscriber.copyWith(endDate: finalEndDate);
      await widget.repository.updateSubscriber(updated);

      print('[RENEW] تم تحديث تاريخ الانتهاء النهائي: $finalEndDate');
      // تم إلغاء مزامنة البيانات لتحسين الأداء - الاعتماد على البيانات المحدثة من السيرفر فقط

      // تم إلغاء المزامنة الشاملة لتحسين الأداء

      // إضافة سجل عملية التجديد
      await DBHelper.instance.insertTransaction(
        MyTrans.Transaction(
          type: MyTrans.TransactionType.renewal,
          description:
              'تجديد اشتراك ${selectedBundle?['name'] ?? 'باقة'} بسعر ${priceController.text} د.ع${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
          date: DateTime.now(),
          subscriberId: widget.subscriber.id,
        ),
      );

      // النظام المالي الاحترافي الجديد
      final financialResult = await _processRenewalFinancials(
        subscriptionPrice,
      );

      // تطبيق النتائج المالية مع الحفاظ على تاريخ الانتهاء الصحيح من السيرفر
      final updatedSubscriber = widget.subscriber.copyWith(
        totalDebt: financialResult.newDebt,
        walletBalance: financialResult.newWallet,
        endDate: finalEndDate, // الحفاظ على التاريخ الصحيح من السيرفر
      );
      await widget.repository.updateSubscriber(updatedSubscriber);

      print('[RENEW_FINANCIAL] ${financialResult.description}');

      // إضافة سجل المعاملة المناسب
      if (!isPaidArrived) {
        // سجل إضافة دين
        await DBHelper.instance.insertTransaction(
          MyTrans.Transaction(
            type: MyTrans.TransactionType.addDebt,
            description:
                'إضافة دين عند التجديد ${financialResult.debtAdded.toStringAsFixed(0)}${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
            date: DateTime.now(),
            subscriberId: widget.subscriber.id,
          ),
        );
      } else {
        // سجل دفعة
        final paidAmount = double.tryParse(paidAmountController.text) ?? 0.0;
        if (paidAmount > 0) {
          await DBHelper.instance.insertTransaction(
            MyTrans.Transaction(
              type: MyTrans.TransactionType.addCredit,
              description:
                  'دفعة عند التجديد ${paidAmount.toStringAsFixed(0)}${notesController.text.trim().isNotEmpty ? ' - ${notesController.text.trim()}' : ''}',
              date: DateTime.now(),
              subscriberId: widget.subscriber.id,
            ),
          );
        }

        // سجل استخدام المحفظة إذا تم استخدامها
        if (financialResult.walletUsed > 0) {
          await DBHelper.instance.insertTransaction(
            MyTrans.Transaction(
              type: MyTrans.TransactionType.addDebt,
              description:
                  'استخدام المحفظة عند التجديد ${financialResult.walletUsed.toStringAsFixed(0)}',
              date: DateTime.now(),
              subscriberId: widget.subscriber.id,
            ),
          );
        }

        // سجل إضافة للمحفظة إذا تم إضافة مبلغ
        if (financialResult.excessToWallet > 0) {
          await DBHelper.instance.insertTransaction(
            MyTrans.Transaction(
              type: MyTrans.TransactionType.addCredit,
              description:
                  'إضافة للمحفظة عند التجديد ${financialResult.excessToWallet.toStringAsFixed(0)}',
              date: DateTime.now(),
              subscriberId: widget.subscriber.id,
            ),
          );
        }

        // سجل إضافة دين إذا تم إضافة دين
        if (financialResult.debtAdded > 0) {
          await DBHelper.instance.insertTransaction(
            MyTrans.Transaction(
              type: MyTrans.TransactionType.addDebt,
              description:
                  'دين عند التجديد ${financialResult.debtAdded.toStringAsFixed(0)}',
              date: DateTime.now(),
              subscriberId: widget.subscriber.id,
            ),
          );
        }
      }
      // إرسال رسالة واتساب بعد التجديد باستخدام تاريخ الانتهاء الجديد أو المزامن أو بعد المزامنة الشاملة
      try {
        final renewMsg = await DBHelper.instance.getMessage(
          'start_msg',
          'مرحباً {الاسم}، تم تجديد اشتراكك بنجاح. نوع الاشتراك: {نوع_الاشتراك}، السعر: {سعر_الاشتراك}، الدين الحالي: {الدين}، تاريخ البدء: {تاريخ_البدء}، تاريخ الانتهاء: {تاريخ_الانتهاء}. شكراً لاختيارك لنا.',
        );
        String phone = widget.subscriber.phone.trim();
        if (phone.startsWith('0')) {
          phone = '964${phone.substring(1)}';
        }
        phone = phone.replaceAll(RegExp(r'[^0-9]'), '');
        // استخدم تاريخ الانتهاء بعد المزامنة الشاملة إذا توفر، وإلا fallback للمنطق السابق
        DateTime endDateForMsg = finalEndDate;
        // لا ترفق startDate إلا إذا كان القالب يحتوي عليه، وبتنسيق التاريخ مع الوقت إذا لزم
        String? startDateStr;
        if (renewMsg.contains('{تاريخ_البدء}')) {
          final d = DateTime.now();
          // استخدم نفس منطق تنسيق تاريخ الانتهاء
          startDateStr = await auto_notify.formatDateForMessage(d);
        }
        String msg = await auto_notify.fillMessageVars(
          renewMsg,
          name: widget.subscriber.name,
          phone: phone,
          type:
              selectedBundle?['name']?.toString() ??
              widget.subscriber.subscriptionType,
          price: priceController.text,
          debt: financialResult.newDebt.toString(),
          startDate: startDateStr,
          endDate: endDateForMsg,
        );
        final whatsappUrl = Uri.parse(
          'https://wa.me/$phone?text=${Uri.encodeComponent(msg)}',
        );

        try {
          final launched = await launchUrl(
            whatsappUrl,
            mode: LaunchMode.externalApplication,
          );

          if (launched) {
            debugPrint('[RENEW][WHATSAPP] تم فتح واتساب بنجاح');
          } else {
            debugPrint(
              '[RENEW][WHATSAPP] فشل فتح واتساب - launchUrl returned false',
            );
          }
        } catch (e) {
          debugPrint('[RENEW][WHATSAPP] فشل إرسال رسالة واتساب: $e');

          // محاولة ثانية بالبروتوكول المباشر
          try {
            final directUri = Uri.parse(
              'whatsapp://send?phone=$phone&text=${Uri.encodeComponent(msg)}',
            );
            final launched = await launchUrl(
              directUri,
              mode: LaunchMode.externalApplication,
            );

            if (launched) {
              debugPrint(
                '[RENEW][WHATSAPP] تم فتح واتساب بنجاح عبر البروتوكول المباشر',
              );
            }
          } catch (e2) {
            debugPrint(
              '[RENEW][WHATSAPP] فشل فتح واتساب عبر البروتوكول المباشر: $e2',
            );
          }
        }
      } catch (e) {
        debugPrint('[RENEW][WHATSAPP] خطأ عام في إرسال واتساب: $e');
      }
      if (mounted) {
        Navigator.of(context).pop(true); // فقط إغلاق الشاشة مع إرجاع true
      }
    } catch (e) {
      setState(() {
        errorMsg = 'حدث خطأ أثناء التجديد';
      });
      debugPrint('[RENEW][ERROR] استثناء أثناء التجديد: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
      debugPrint('[RENEW] انتهاء منطق التجديد.');
    }
  }

  // دالة فك تشفير متوافقة مع OpenSSL/CryptoJS AES CBC PKCS7
  String decryptWithOpenSSL(String encryptedBase64, String passphrase) {
    final encrypted = base64.decode(encryptedBase64);
    if (utf8.decode(encrypted.sublist(0, 8)) != 'Salted__') {
      throw Exception('Missing Salted__ prefix');
    }
    final salt = encrypted.sublist(8, 16);
    final keyAndIV = _evpBytesToKey(passphrase.codeUnits, salt, 32, 16);
    final key = keyAndIV.sublist(0, 32);
    final iv = Uint8List.fromList(keyAndIV.sublist(32, 48));
    final cipher = encrypt.Encrypter(
      encrypt.AES(
        encrypt.Key(Uint8List.fromList(key)),
        mode: encrypt.AESMode.cbc,
        padding: null,
      ),
    );
    final decrypted = cipher.decryptBytes(
      encrypt.Encrypted(encrypted.sublist(16)),
      iv: encrypt.IV(iv),
    );
    // إزالة PKCS7 padding
    final pad = decrypted.last;
    final unpadded = decrypted.sublist(0, decrypted.length - pad);
    return utf8.decode(unpadded);
  }

  // دالة evpBytesToKey المطلوبة لفك التشفير (نفس دالة التشفير)
  List<int> _evpBytesToKey(
    List<int> password,
    List<int> salt,
    int keyLen,
    int ivLen,
  ) {
    final totalLen = keyLen + ivLen;
    var derived = <int>[];
    var block = <int>[];
    while (derived.length < totalLen) {
      final hasher = md5.convert([...block, ...password, ...salt]);
      block = hasher.bytes;
      derived.addAll(block);
    }
    return derived.sublist(0, totalLen);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // رأس الشاشة العصري
          _buildModernHeader(colorScheme),

          // المحتوى القابل للتمرير
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // بطاقة معلومات المشترك
                  _buildSubscriberInfoCard(colorScheme),
                  const SizedBox(height: 20),

                  // بطاقة اختيار الباقة والسعر
                  _buildPackageSelectionCard(colorScheme),
                  const SizedBox(height: 20),

                  // حقل الملاحظات
                  _buildNotesCard(colorScheme),
                  const SizedBox(height: 20),

                  // رسالة توضيحية للمشتركين اليدويين
                  if (widget.subscriber.isManualSubscriber)
                    _buildManualSubscriberNotice(colorScheme),

                  const SizedBox(height: 20),

                  // زر التجديد العصري
                  _buildRenewButton(colorScheme),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر نوع المشترك
  Widget _buildSubscriberTypeChip(ColorScheme colorScheme) {
    String typeText;
    Color typeColor;
    IconData typeIcon;

    if (widget.subscriber.isManualSubscriber) {
      typeText = 'يدوي';
      typeColor = Colors.blue;
      typeIcon = Icons.person_add;
    } else if (widget.subscriber.isSasSubscriber) {
      typeText = 'SAS4';
      typeColor = Colors.green;
      typeIcon = Icons.dns;
    } else if (widget.subscriber.isServerSubscriber) {
      typeText = 'سيرفر';
      typeColor = Colors.purple;
      typeIcon = Icons.storage;
    } else if (widget.subscriber.isEarthlinkSubscriber) {
      typeText = 'Earthlink';
      typeColor = Colors.orange;
      typeIcon = Icons.language;
    } else {
      typeText = 'SAS4'; // افتراضي للمشتركين القدامى
      typeColor = Colors.green;
      typeIcon = Icons.dns;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: typeColor.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(typeIcon, size: 12, color: typeColor),
          const SizedBox(width: 4),
          Text(
            typeText,
            style: TextStyle(
              color: typeColor,
              fontWeight: FontWeight.bold,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  // رأس الشاشة العصري
  Widget _buildModernHeader(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primary.withValues(alpha: 0.1),
            colorScheme.secondary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // مؤشر السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: colorScheme.outline.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          // أيقونة وعنوان
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.refresh_rounded,
                  color: colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'تجديد الاشتراك',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          widget.subscriber.name,
                          style: TextStyle(
                            fontSize: 16,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(width: 8),
                        // مؤشر نوع المشترك
                        _buildSubscriberTypeChip(colorScheme),
                      ],
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: Icon(
                  Icons.close_rounded,
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بطاقة معلومات المشترك
  Widget _buildSubscriberInfoCard(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.person_rounded,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'معلومات المشترك',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow('الاسم', widget.subscriber.name, colorScheme),
            _buildInfoRow('اسم المستخدم', widget.subscriber.user, colorScheme),
            _buildInfoRow(
              'الباقة الحالية',
              widget.subscriber.subscriptionType,
              colorScheme,
            ),
            _buildInfoRow(
              'تاريخ الانتهاء',
              '${widget.subscriber.endDate.day}/${widget.subscriber.endDate.month}/${widget.subscriber.endDate.year}',
              colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  // صف معلومات
  Widget _buildInfoRow(String label, String value, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بطاقة اختيار الباقة والسعر
  Widget _buildPackageSelectionCard(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.inventory_2_rounded,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل التجديد',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // حقل اختيار الباقة والسعر
            TextFormField(
              controller: priceController,
              decoration: InputDecoration(
                labelText: _buildPriceLabelText(),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: Icon(
                  Icons.attach_money_rounded,
                  color: colorScheme.primary,
                ),
                suffixText: 'د.ع',
                helperText: _buildPriceHelperText(),
                helperStyle: TextStyle(
                  fontSize: 12,
                  color: _isCustomPrice() ? Colors.blue : Colors.grey,
                  fontWeight: _isCustomPrice()
                      ? FontWeight.w500
                      : FontWeight.normal,
                ),
              ),
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              onTap: subscriptions.isEmpty ? null : _showPackageSelector,
            ),
            const SizedBox(height: 16),

            // حقل المبلغ المدفوع (يظهر فقط عند تفعيل "تم الاستلام")
            if (isPaidArrived) ...[
              TextFormField(
                controller: paidAmountController,
                decoration: InputDecoration(
                  labelText: 'المبلغ المدفوع فعلياً',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  prefixIcon: Icon(
                    Icons.payments_rounded,
                    color: colorScheme.primary,
                  ),
                  suffixText: 'د.ع',
                  helperText: 'أدخل المبلغ الذي تم دفعه فعلياً',
                ),
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                readOnly: !isAmountEditable,
                onTap: !isAmountEditable
                    ? () {
                        setState(() {
                          isAmountEditable = true;
                          // تعيين القيمة الافتراضية لسعر الباقة
                          if (paidAmountController.text.isEmpty) {
                            paidAmountController.text = priceController.text;
                          }
                        });
                      }
                    : null,
                onChanged: (value) {
                  // يمكن إضافة منطق تحديث فوري هنا إذا لزم الأمر
                },
              ),
              const SizedBox(height: 8),
              // مؤشر توضيحي للمبلغ
              if (paidAmountController.text.isNotEmpty &&
                  priceController.text.isNotEmpty)
                _buildPaymentSummary(colorScheme),
              const SizedBox(height: 16),
            ],

            // مؤشر حالة الدفع
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isPaidArrived
                    ? colorScheme.primary.withValues(alpha: 0.1)
                    : colorScheme.error.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isPaidArrived
                      ? colorScheme.primary.withValues(alpha: 0.3)
                      : colorScheme.error.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isPaidArrived
                        ? Icons.check_circle_rounded
                        : Icons.pending_rounded,
                    color: isPaidArrived
                        ? colorScheme.primary
                        : colorScheme.error,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'حالة الدفع',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          isPaidArrived
                              ? 'تم استلام المبلغ'
                              : 'لم يتم استلام المبلغ',
                          style: TextStyle(
                            fontSize: 12,
                            color: colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Switch(
                    value: isPaidArrived,
                    onChanged: (val) {
                      setState(() {
                        isPaidArrived = val;
                        // إعادة تعيين القيم عند تغيير حالة الدفع
                        if (val) {
                          // إذا تم تفعيل "تم الاستلام"، تعيين المبلغ المدفوع لسعر الباقة
                          paidAmountController.text = priceController.text;
                          isAmountEditable = false;
                        } else {
                          // إذا تم إلغاء "تم الاستلام"، مسح المبلغ المدفوع
                          paidAmountController.clear();
                          isAmountEditable = false;
                        }
                      });
                    },
                    activeColor: colorScheme.primary,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عرض منتقي الباقات
  Future<void> _showPackageSelector() async {
    final selected = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (ctx) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'اختر باقة الاشتراك',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
                color: Theme.of(ctx).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: subscriptions.length,
                separatorBuilder: (_, index) => const SizedBox(height: 8),
                itemBuilder: (ctx, idx) {
                  final sub = subscriptions[idx];
                  final isSelected = selectedBundle?['id'] == sub['id'];
                  return Card(
                    elevation: 0,
                    color: isSelected
                        ? Theme.of(
                            ctx,
                          ).colorScheme.primary.withValues(alpha: 0.1)
                        : Theme.of(ctx).colorScheme.surface,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: isSelected
                            ? Theme.of(ctx).colorScheme.primary
                            : Theme.of(
                                ctx,
                              ).colorScheme.outline.withValues(alpha: 0.2),
                      ),
                    ),
                    child: ListTile(
                      leading: Icon(
                        Icons.star_rounded,
                        color: isSelected
                            ? Theme.of(ctx).colorScheme.primary
                            : Colors.amber,
                      ),
                      title: Text(
                        sub['name'] ?? '',
                        style: TextStyle(
                          fontWeight: isSelected
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${_getEffectiveSellPrice(sub)} د.ع',
                            style: TextStyle(
                              color: Theme.of(ctx).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (sub['custom_sell_price'] != null &&
                              sub['custom_sell_price'] > 0)
                            Container(
                              margin: const EdgeInsets.only(top: 2),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 1,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.blue.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                'سعر مخصص',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check_circle_rounded,
                              color: Theme.of(ctx).colorScheme.primary,
                            )
                          : null,
                      onTap: () => Navigator.of(ctx).pop(sub),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );

    if (selected != null) {
      setState(() {
        selectedBundle = selected;
        // استخدام السعر المخصص إذا كان موجوداً، وإلا السعر الافتراضي
        final effectivePrice = _getEffectiveSellPrice(selected);
        priceController.text = effectivePrice.toString();
      });
    }
  }

  // بطاقة الملاحظات
  Widget _buildNotesCard(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.note_rounded, color: colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'الملاحظات',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // حقل الملاحظات
            TextFormField(
              controller: notesController,
              decoration: InputDecoration(
                labelText: 'ملاحظات',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                prefixIcon: Icon(
                  Icons.note_rounded,
                  color: colorScheme.primary,
                ),
                hintText: 'أضف ملاحظات حول عملية التجديد...',
              ),
              maxLines: 3,
              minLines: 1,
              textInputAction: TextInputAction.done,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء رسالة توضيحية للمشتركين اليدويين
  Widget _buildManualSubscriberNotice(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'تجديد محلي',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'سيتم تجديد هذا المشترك محلياً (إضافة 30 يوم) بدون الحاجة للاتصال بالسيرفر',
                  style: TextStyle(
                    color: Colors.blue.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // زر التجديد العصري
  Widget _buildRenewButton(ColorScheme colorScheme) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primary.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: isLoading ? null : _renew,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: isLoading
            ? SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    colorScheme.onPrimary,
                  ),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.refresh_rounded,
                    color: colorScheme.onPrimary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    widget.subscriber.isManualSubscriber
                        ? 'تجديد محلي (30 يوم)'
                        : 'تجديد الاشتراك',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // ملخص الدفعة
  Widget _buildPaymentSummary(ColorScheme colorScheme) {
    final paidAmount = double.tryParse(paidAmountController.text) ?? 0.0;
    final subscriptionPrice = double.tryParse(priceController.text) ?? 0.0;
    final currentDebt = widget.subscriber.totalDebt;
    final currentWallet = widget.subscriber.walletBalance;

    if (paidAmount <= 0 || subscriptionPrice <= 0) {
      return const SizedBox.shrink();
    }

    // استخدام النظام المالي الجديد للحصول على النتائج
    final financialResult = _calculateSmartFinancials(
      subscriptionPrice: subscriptionPrice,
      paidAmount: paidAmount,
      currentDebt: currentDebt,
      currentWallet: currentWallet,
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate_rounded,
                color: colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'ملخص الدفعة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildSummaryRow(
            'المبلغ المدفوع:',
            '${paidAmount.toStringAsFixed(0)} د.ع',
            colorScheme,
          ),
          _buildSummaryRow(
            'سعر الاشتراك:',
            '${subscriptionPrice.toStringAsFixed(0)} د.ع',
            colorScheme,
          ),
          if (financialResult.debtAdded > 0)
            _buildSummaryRow(
              'دين مضاف:',
              '${financialResult.debtAdded.toStringAsFixed(0)} د.ع',
              colorScheme,
              color: Colors.red,
            ),
          if (financialResult.walletUsed > 0)
            _buildSummaryRow(
              'استخدام المحفظة:',
              '${financialResult.walletUsed.toStringAsFixed(0)} د.ع',
              colorScheme,
              color: Colors.orange,
            ),
          if (financialResult.excessToWallet > 0)
            _buildSummaryRow(
              'إضافة للمحفظة:',
              '${financialResult.excessToWallet.toStringAsFixed(0)} د.ع',
              colorScheme,
              color: Colors.green,
            ),
          const Divider(height: 16),
          _buildSummaryRow(
            'الدين بعد التجديد:',
            '${financialResult.newDebt.toStringAsFixed(0)} د.ع',
            colorScheme,
            isBold: true,
            color: financialResult.newDebt > 0 ? Colors.red : Colors.green,
          ),
          _buildSummaryRow(
            'رصيد المحفظة الجديد:',
            '${financialResult.newWallet.toStringAsFixed(0)} د.ع',
            colorScheme,
            isBold: true,
          ),
        ],
      ),
    );
  }

  // صف في ملخص الدفعة
  Widget _buildSummaryRow(
    String label,
    String value,
    ColorScheme colorScheme, {
    Color? color,
    bool isBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 13,
              color: colorScheme.onSurface.withValues(alpha: 0.8),
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 13,
              color: color ?? colorScheme.onSurface,
              fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
