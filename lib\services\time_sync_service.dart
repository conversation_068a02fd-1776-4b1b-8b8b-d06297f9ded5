import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

class TimeSyncService {
  static const String _lastSyncKey = 'last_time_sync';
  static const String _serverTimeOffsetKey = 'server_time_offset';

  // الحصول على الوقت الحقيقي من الخادم
  static Future<DateTime?> getServerTime() async {
    try {
      // استعلام بسيط للحصول على الوقت من Supabase
      final response = await Supabase.instance.client
          .rpc('get_server_time')
          .single();

      if (response != null && response['server_time'] != null) {
        return DateTime.parse(response['server_time']);
      }
    } catch (e) {
      debugPrint('خطأ في جلب الوقت من الخادم (سيتم استخدام الوقت المحلي): $e');
      // إرجاع الوقت المحلي كبديل
      return DateTime.now();
    }
    return DateTime.now();
  }

  // مزامنة الوقت مع الخادم
  static Future<bool> syncTimeWithServer() async {
    try {
      final serverTime = await getServerTime();
      if (serverTime == null) return false;

      final localTime = DateTime.now();
      final offset = serverTime.difference(localTime).inMilliseconds;

      // حفظ الإزاحة الزمنية
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_serverTimeOffsetKey, offset);
      await prefs.setInt(_lastSyncKey, localTime.millisecondsSinceEpoch);

      debugPrint('تم مزامنة الوقت - الإزاحة: ${offset}ms');
      return true;
    } catch (e) {
      debugPrint('خطأ في مزامنة الوقت: $e');
      return false;
    }
  }

  // الحصول على الوقت الصحيح (محلي + إزاحة الخادم)
  static Future<DateTime> getCurrentTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final offset = prefs.getInt(_serverTimeOffsetKey) ?? 0;
      final lastSync = prefs.getInt(_lastSyncKey) ?? 0;

      // إذا مر أكثر من ساعة على آخر مزامنة، حاول المزامنة مرة أخرى
      final now = DateTime.now();
      if (lastSync > 0) {
        final lastSyncTime = DateTime.fromMillisecondsSinceEpoch(lastSync);
        final timeSinceSync = now.difference(lastSyncTime);

        if (timeSinceSync.inHours >= 1) {
          await syncTimeWithServer();
          // إعادة قراءة الإزاحة المحدثة
          final newOffset = prefs.getInt(_serverTimeOffsetKey) ?? offset;
          return now.add(Duration(milliseconds: newOffset));
        }
      }

      return now.add(Duration(milliseconds: offset));
    } catch (e) {
      debugPrint('خطأ في الحصول على الوقت الحالي: $e');
      return DateTime.now();
    }
  }

  // التحقق من صحة الوقت المحلي
  static Future<bool> validateLocalTime() async {
    try {
      final serverTime = await getServerTime();
      if (serverTime == null) {
        // لا يوجد اتصال بالإنترنت، نقبل الوقت المحلي
        return true;
      }

      final localTime = DateTime.now();
      final difference = (serverTime.difference(localTime)).abs();

      // إذا كان الفرق أكثر من 5 دقائق، فالوقت المحلي غير صحيح
      if (difference.inMinutes > 5) {
        debugPrint('تم اكتشاف تلاعب في الوقت المحلي');
        debugPrint('الوقت المحلي: $localTime');
        debugPrint('وقت الخادم: $serverTime');
        debugPrint('الفرق: ${difference.inMinutes} دقيقة');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('خطأ في التحقق من صحة الوقت: $e');
      // في حالة الخطأ، نقبل الوقت المحلي
      return true;
    }
  }

  // فحص انتهاء الفترة التجريبية بالوقت الصحيح
  static Future<bool> isTrialExpired() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryMillis = prefs.getInt('expiry_millis') ?? 0;

      if (expiryMillis == 0) return false;

      final currentTime = await getCurrentTime();
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(expiryMillis);

      return currentTime.isAfter(expiryTime);
    } catch (e) {
      debugPrint('خطأ في فحص انتهاء الفترة التجريبية: $e');
      return false;
    }
  }

  // حساب الأيام المتبقية بالوقت الصحيح
  static Future<int> getDaysLeft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final expiryMillis = prefs.getInt('expiry_millis') ?? 0;

      if (expiryMillis == 0) return 0;

      final currentTime = await getCurrentTime();
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(expiryMillis);

      final difference = expiryTime.difference(currentTime);
      return difference.inDays.clamp(0, 999);
    } catch (e) {
      debugPrint('خطأ في حساب الأيام المتبقية: $e');
      return 0;
    }
  }

  // جلب بيانات المستخدم من Supabase
  static Future<Map<String, dynamic>?> fetchUserData(String userId) async {
    try {
      final response = await Supabase.instance.client
          .from('user_accounts')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      return response;
    } catch (e) {
      debugPrint('خطأ في جلب بيانات المستخدم: $e');
      return null;
    }
  }

  // تحديث بيانات المستخدم في Supabase
  static Future<bool> updateUserData(
    String userId,
    Map<String, dynamic> data,
  ) async {
    try {
      await Supabase.instance.client.from('user_accounts').upsert({
        'user_id': userId,
        'updated_at': DateTime.now().toIso8601String(),
        ...data,
      });

      return true;
    } catch (e) {
      debugPrint('خطأ في تحديث بيانات المستخدم: $e');
      return false;
    }
  }

  // مزامنة بيانات الحساب مع Supabase
  static Future<void> syncAccountData() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;

      final prefs = await SharedPreferences.getInstance();

      // جلب البيانات من Supabase
      final serverData = await fetchUserData(user.id);

      if (serverData != null) {
        // تحديث البيانات المحلية
        if (serverData['is_trial'] != null) {
          await prefs.setBool('is_trial', serverData['is_trial']);
        }

        if (serverData['expiry_millis'] != null) {
          await prefs.setInt('expiry_millis', serverData['expiry_millis']);
        }

        if (serverData['creation_millis'] != null) {
          await prefs.setInt('creation_millis', serverData['creation_millis']);
        }

        if (serverData['activation_millis'] != null) {
          await prefs.setInt(
            'activation_millis',
            serverData['activation_millis'],
          );
        }

        if (serverData['active_package'] != null) {
          await prefs.setString('active_package', serverData['active_package']);
        }

        debugPrint('تم مزامنة بيانات الحساب من Supabase');
      } else {
        debugPrint('لا توجد بيانات في Supabase، سيتم استخدام البيانات المحلية');
      }
    } catch (e) {
      debugPrint('خطأ في مزامنة بيانات الحساب (سيتم تجاهله): $e');
      // لا نوقف التطبيق بسبب خطأ المزامنة
    }
  }
}
