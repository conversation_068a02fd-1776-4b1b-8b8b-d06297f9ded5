import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import '../db_helper.dart';
import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

/// خدمة حماية البيانات أثناء التحديثات
class DataProtectionService {
  static const String _backupVersionKey = 'backup_version';
  static const String _lastBackupKey = 'last_backup_timestamp';
  static const String _migrationStatusKey = 'migration_status';

  /// إنشاء نسخة احتياطية شاملة قبل التحديث
  static Future<DataBackupResult> createFullBackup({
    required String updateVersion,
    String? reason,
  }) async {
    try {
      debugPrint('💾 [DATA_PROTECTION] بدء إنشاء النسخة الاحتياطية الشاملة...');

      final backupId = DateTime.now().millisecondsSinceEpoch.toString();
      final backupData = <String, dynamic>{
        'backup_id': backupId,
        'backup_version': '2.0',
        'created_at': DateTime.now().toIso8601String(),
        'update_version': updateVersion,
        'reason': reason ?? 'app_update',
        'app_version': await _getCurrentAppVersion(),
      };

      // 1. نسخ احتياطية من قاعدة البيانات المحلية
      final localDbBackup = await _backupLocalDatabase();
      backupData['local_database'] = localDbBackup;

      // 2. نسخ احتياطية من البيانات السحابية
      final cloudBackup = await _backupCloudData();
      backupData['cloud_data'] = cloudBackup;

      // 3. نسخ احتياطية من الإعدادات المحلية
      final settingsBackup = await _backupLocalSettings();
      backupData['local_settings'] = settingsBackup;

      // 4. نسخ احتياطية من الملفات المهمة
      final filesBackup = await _backupImportantFiles();
      backupData['files'] = filesBackup;

      // 5. حفظ النسخة الاحتياطية
      final saveResult = await _saveBackupData(backupId, backupData);
      
      if (saveResult.success) {
        await _updateBackupMetadata(backupId, updateVersion);
        debugPrint('✅ [DATA_PROTECTION] تم إنشاء النسخة الاحتياطية بنجاح');
        
        return DataBackupResult(
          success: true,
          backupId: backupId,
          backupSize: saveResult.size,
          message: 'تم إنشاء النسخة الاحتياطية بنجاح',
        );
      } else {
        throw Exception('فشل في حفظ النسخة الاحتياطية');
      }
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في إنشاء النسخة الاحتياطية: $e');
      return DataBackupResult(
        success: false,
        error: e.toString(),
        message: 'فشل في إنشاء النسخة الاحتياطية',
      );
    }
  }

  /// نسخ احتياطية من قاعدة البيانات المحلية
  static Future<Map<String, dynamic>> _backupLocalDatabase() async {
    try {
      debugPrint('🗄️ [DATA_PROTECTION] نسخ قاعدة البيانات المحلية...');

      final db = await DBHelper.instance.database;
      final backup = <String, dynamic>{};

      // جلب جميع الجداول
      final tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );

      for (final table in tables) {
        final tableName = table['name'] as String;
        try {
          final tableData = await db.query(tableName);
          backup[tableName] = tableData;
          debugPrint('📋 [DATA_PROTECTION] تم نسخ جدول: $tableName (${tableData.length} سجل)');
        } catch (e) {
          debugPrint('⚠️ [DATA_PROTECTION] خطأ في نسخ جدول $tableName: $e');
          backup['${tableName}_error'] = e.toString();
        }
      }

      // حفظ schema الجداول
      final schema = await db.rawQuery(
        "SELECT sql FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      );
      backup['_schema'] = schema;

      return {
        'success': true,
        'tables_count': tables.length,
        'data': backup,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في نسخ قاعدة البيانات المحلية: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// نسخ احتياطية من البيانات السحابية
  static Future<Map<String, dynamic>> _backupCloudData() async {
    try {
      debugPrint('☁️ [DATA_PROTECTION] نسخ البيانات السحابية...');

      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        return {
          'success': false,
          'error': 'المستخدم غير مسجل دخول',
        };
      }

      final backup = <String, dynamic>{
        'user_id': user.id,
        'user_metadata': user.userMetadata,
        'app_metadata': user.appMetadata,
      };

      // نسخ بيانات الحساب
      try {
        final accountData = await Supabase.instance.client
            .from('user_accounts')
            .select('*')
            .eq('user_id', user.id)
            .maybeSingle();
        backup['account_data'] = accountData;
      } catch (e) {
        backup['account_data_error'] = e.toString();
      }

      // نسخ بيانات الأجهزة
      try {
        final devicesData = await Supabase.instance.client
            .from('user_devices')
            .select('*')
            .eq('user_id', user.id);
        backup['devices_data'] = devicesData;
      } catch (e) {
        backup['devices_data_error'] = e.toString();
      }

      // نسخ الجلسات النشطة
      try {
        final sessionsData = await Supabase.instance.client
            .from('active_sessions')
            .select('*')
            .eq('user_id', user.id);
        backup['sessions_data'] = sessionsData;
      } catch (e) {
        backup['sessions_data_error'] = e.toString();
      }

      // نسخ الإحصائيات اليومية
      try {
        final statsData = await Supabase.instance.client
            .from('daily_stats')
            .select('*')
            .eq('user_id', user.id)
            .order('date', ascending: false)
            .limit(30); // آخر 30 يوم
        backup['stats_data'] = statsData;
      } catch (e) {
        backup['stats_data_error'] = e.toString();
      }

      return {
        'success': true,
        'data': backup,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في نسخ البيانات السحابية: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// نسخ احتياطية من الإعدادات المحلية
  static Future<Map<String, dynamic>> _backupLocalSettings() async {
    try {
      debugPrint('⚙️ [DATA_PROTECTION] نسخ الإعدادات المحلية...');

      final prefs = await SharedPreferences.getInstance();
      final settings = <String, dynamic>{};

      // نسخ جميع الإعدادات المهمة
      for (final key in prefs.getKeys()) {
        if (_isImportantSetting(key)) {
          final value = prefs.get(key);
          settings[key] = value;
        }
      }

      return {
        'success': true,
        'settings_count': settings.length,
        'data': settings,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في نسخ الإعدادات المحلية: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// فحص إذا كان الإعداد مهم
  static bool _isImportantSetting(String key) {
    final importantPrefixes = [
      'user_',
      'app_',
      'settings_',
      'account_',
      'sync_',
      'notification_',
      'theme_',
      'language_',
      'backup_',
      'migration_',
    ];

    return importantPrefixes.any((prefix) => key.startsWith(prefix)) ||
           key == 'is_logged_in' ||
           key == 'first_run' ||
           key == 'has_seen_introduction';
  }

  /// نسخ احتياطية من الملفات المهمة
  static Future<Map<String, dynamic>> _backupImportantFiles() async {
    try {
      debugPrint('📁 [DATA_PROTECTION] نسخ الملفات المهمة...');

      final appDir = await getApplicationDocumentsDirectory();
      final files = <String, dynamic>{};

      // البحث عن الملفات المهمة
      final importantFiles = [
        'profile_image.jpg',
        'app_config.json',
        'user_data.json',
        'cache_data.json',
      ];

      for (final fileName in importantFiles) {
        final file = File(path.join(appDir.path, fileName));
        if (await file.exists()) {
          try {
            final content = await file.readAsBytes();
            files[fileName] = {
              'size': content.length,
              'content': base64Encode(content),
              'last_modified': (await file.lastModified()).toIso8601String(),
            };
          } catch (e) {
            files['${fileName}_error'] = e.toString();
          }
        }
      }

      return {
        'success': true,
        'files_count': files.length,
        'data': files,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في نسخ الملفات: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// حفظ النسخة الاحتياطية
  static Future<SaveResult> _saveBackupData(String backupId, Map<String, dynamic> data) async {
    try {
      // حفظ محلي
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(data);
      await prefs.setString('backup_$backupId', jsonString);

      // حفظ في ملف
      final appDir = await getApplicationDocumentsDirectory();
      final backupFile = File(path.join(appDir.path, 'backup_$backupId.json'));
      await backupFile.writeAsString(jsonString);

      final size = jsonString.length;
      debugPrint('💾 [DATA_PROTECTION] تم حفظ النسخة الاحتياطية: ${(size / 1024).toStringAsFixed(2)} KB');

      return SaveResult(success: true, size: size);
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في حفظ النسخة الاحتياطية: $e');
      return SaveResult(success: false, error: e.toString());
    }
  }

  /// تحديث metadata النسخة الاحتياطية
  static Future<void> _updateBackupMetadata(String backupId, String updateVersion) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_backupVersionKey, updateVersion);
      await prefs.setInt(_lastBackupKey, DateTime.now().millisecondsSinceEpoch);
      await prefs.setString('last_backup_id', backupId);
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في تحديث metadata: $e');
    }
  }

  /// استعادة البيانات من النسخة الاحتياطية
  static Future<RestoreResult> restoreFromBackup(String backupId) async {
    try {
      debugPrint('🔄 [DATA_PROTECTION] بدء استعادة البيانات من النسخة الاحتياطية...');

      // جلب النسخة الاحتياطية
      final prefs = await SharedPreferences.getInstance();
      final backupString = prefs.getString('backup_$backupId');
      
      if (backupString == null) {
        throw Exception('النسخة الاحتياطية غير موجودة');
      }

      final backupData = jsonDecode(backupString) as Map<String, dynamic>;

      // استعادة الإعدادات المحلية
      if (backupData['local_settings'] != null) {
        await _restoreLocalSettings(backupData['local_settings']);
      }

      // استعادة قاعدة البيانات المحلية
      if (backupData['local_database'] != null) {
        await _restoreLocalDatabase(backupData['local_database']);
      }

      // استعادة الملفات
      if (backupData['files'] != null) {
        await _restoreFiles(backupData['files']);
      }

      debugPrint('✅ [DATA_PROTECTION] تم استعادة البيانات بنجاح');
      return RestoreResult(success: true, message: 'تم استعادة البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في استعادة البيانات: $e');
      return RestoreResult(success: false, error: e.toString());
    }
  }

  /// استعادة الإعدادات المحلية
  static Future<void> _restoreLocalSettings(Map<String, dynamic> settingsData) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settings = settingsData['data'] as Map<String, dynamic>;

      for (final entry in settings.entries) {
        final key = entry.key;
        final value = entry.value;

        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        }
      }

      debugPrint('⚙️ [DATA_PROTECTION] تم استعادة الإعدادات المحلية');
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في استعادة الإعدادات: $e');
    }
  }

  /// استعادة قاعدة البيانات المحلية
  static Future<void> _restoreLocalDatabase(Map<String, dynamic> dbData) async {
    try {
      if (dbData['success'] != true) return;

      final data = dbData['data'] as Map<String, dynamic>;
      final db = await DBHelper.instance.database;

      // استعادة البيانات لكل جدول
      for (final entry in data.entries) {
        final tableName = entry.key;
        if (tableName.startsWith('_') || tableName.endsWith('_error')) continue;

        final tableData = entry.value as List<dynamic>;
        
        try {
          // مسح الجدول الحالي
          await db.delete(tableName);
          
          // إدراج البيانات المستعادة
          for (final row in tableData) {
            await db.insert(tableName, row as Map<String, dynamic>);
          }
          
          debugPrint('📋 [DATA_PROTECTION] تم استعادة جدول: $tableName');
        } catch (e) {
          debugPrint('⚠️ [DATA_PROTECTION] خطأ في استعادة جدول $tableName: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في استعادة قاعدة البيانات: $e');
    }
  }

  /// استعادة الملفات
  static Future<void> _restoreFiles(Map<String, dynamic> filesData) async {
    try {
      if (filesData['success'] != true) return;

      final data = filesData['data'] as Map<String, dynamic>;
      final appDir = await getApplicationDocumentsDirectory();

      for (final entry in data.entries) {
        final fileName = entry.key;
        if (fileName.endsWith('_error')) continue;

        final fileData = entry.value as Map<String, dynamic>;
        final content = base64Decode(fileData['content']);
        
        final file = File(path.join(appDir.path, fileName));
        await file.writeAsBytes(content);
        
        debugPrint('📁 [DATA_PROTECTION] تم استعادة ملف: $fileName');
      }
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في استعادة الملفات: $e');
    }
  }

  /// الحصول على إصدار التطبيق الحالي
  static Future<String> _getCurrentAppVersion() async {
    try {
      // يمكن استخدام package_info_plus هنا
      return '1.0.0'; // placeholder
    } catch (e) {
      return 'unknown';
    }
  }

  /// فحص توفر نسخة احتياطية
  static Future<bool> hasBackup(String backupId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey('backup_$backupId');
    } catch (e) {
      return false;
    }
  }

  /// حذف النسخ الاحتياطية القديمة
  static Future<void> cleanupOldBackups({int keepCount = 3}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('backup_')).toList();
      
      if (keys.length > keepCount) {
        keys.sort();
        final toDelete = keys.take(keys.length - keepCount);
        
        for (final key in toDelete) {
          await prefs.remove(key);
        }
        
        debugPrint('🧹 [DATA_PROTECTION] تم حذف ${toDelete.length} نسخة احتياطية قديمة');
      }
    } catch (e) {
      debugPrint('❌ [DATA_PROTECTION] خطأ في تنظيف النسخ الاحتياطية: $e');
    }
  }
}

/// نتيجة النسخة الاحتياطية
class DataBackupResult {
  final bool success;
  final String? backupId;
  final int? backupSize;
  final String? error;
  final String message;

  DataBackupResult({
    required this.success,
    this.backupId,
    this.backupSize,
    this.error,
    required this.message,
  });
}

/// نتيجة الحفظ
class SaveResult {
  final bool success;
  final int? size;
  final String? error;

  SaveResult({required this.success, this.size, this.error});
}

/// نتيجة الاستعادة
class RestoreResult {
  final bool success;
  final String? error;
  final String? message;

  RestoreResult({required this.success, this.error, this.message});
}
