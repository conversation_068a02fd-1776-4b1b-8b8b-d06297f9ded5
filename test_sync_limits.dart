import 'package:flutter/material.dart';
import 'lib/services/daily_sync_limits.dart';
import 'lib/services/sync_notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 اختبار نظام حدود المزامنة اليومية');
  print('=' * 50);
  
  // اختبار 1: الحصول على الإحصائيات الأولية
  print('\n📊 اختبار 1: الإحصائيات الأولية');
  final initialStats = await DailySyncLimits.getTodayStats();
  print('المزامنات اليدوية: ${initialStats['manualSyncs']}/${initialStats['maxManualSyncs']}');
  print('المزامنات التلقائية: ${initialStats['autoSyncs']}/${initialStats['maxAutoSyncs']}');
  print('المتبقي يدوي: ${initialStats['manualRemaining']}');
  print('المتبقي تلقائي: ${initialStats['autoRemaining']}');
  
  // اختبار 2: فحص إمكانية المزامنة اليدوية
  print('\n🖱️ اختبار 2: فحص المزامنة اليدوية');
  final manualCheck = await DailySyncLimits.canPerformManualSync();
  print('يمكن المزامنة: ${manualCheck['canSync']}');
  print('الرسالة: ${manualCheck['message']}');
  
  // اختبار 3: فحص إمكانية المزامنة التلقائية
  print('\n🤖 اختبار 3: فحص المزامنة التلقائية');
  final autoCheck = await DailySyncLimits.canPerformAutoSync();
  print('يمكن المزامنة: ${autoCheck['canSync']}');
  print('الرسالة: ${autoCheck['message']}');
  
  // اختبار 4: تسجيل مزامنة يدوية
  print('\n📝 اختبار 4: تسجيل مزامنة يدوية');
  await DailySyncLimits.recordManualSync();
  final afterManualStats = await DailySyncLimits.getTodayStats();
  print('بعد التسجيل - المزامنات اليدوية: ${afterManualStats['manualSyncs']}/${afterManualStats['maxManualSyncs']}');
  
  // اختبار 5: تسجيل مزامنة تلقائية
  print('\n🔄 اختبار 5: تسجيل مزامنة تلقائية');
  await DailySyncLimits.recordAutoSync();
  final afterAutoStats = await DailySyncLimits.getTodayStats();
  print('بعد التسجيل - المزامنات التلقائية: ${afterAutoStats['autoSyncs']}/${afterAutoStats['maxAutoSyncs']}');
  
  // اختبار 6: محاولة تجاوز الحدود
  print('\n⚠️ اختبار 6: محاولة تجاوز الحدود');
  
  // تسجيل مزامنات يدوية حتى الوصول للحد
  for (int i = afterManualStats['manualSyncs']; i < 3; i++) {
    await DailySyncLimits.recordManualSync();
    print('تم تسجيل مزامنة يدوية ${i + 1}');
  }
  
  // فحص الحالة بعد الوصول للحد
  final limitReachedCheck = await DailySyncLimits.canPerformManualSync();
  print('بعد الوصول للحد - يمكن المزامنة: ${limitReachedCheck['canSync']}');
  print('الرسالة: ${limitReachedCheck['message']}');
  
  // اختبار 7: وقت إعادة التعيين
  print('\n⏰ اختبار 7: وقت إعادة التعيين');
  final resetTime = DailySyncLimits.getNextResetTime();
  print('وقت إعادة التعيين: $resetTime');
  
  // اختبار 8: الإحصائيات النهائية
  print('\n📈 اختبار 8: الإحصائيات النهائية');
  final finalStats = await DailySyncLimits.getTodayStats();
  print('المزامنات اليدوية: ${finalStats['manualSyncs']}/${finalStats['maxManualSyncs']}');
  print('المزامنات التلقائية: ${finalStats['autoSyncs']}/${finalStats['maxAutoSyncs']}');
  print('إجمالي المزامنات: ${finalStats['totalSyncs']}/${finalStats['totalMaxSyncs']}');
  print('يمكن مزامنة يدوية: ${finalStats['canManualSync']}');
  print('يمكن مزامنة تلقائية: ${finalStats['canAutoSync']}');
  
  print('\n✅ انتهى الاختبار بنجاح!');
  print('=' * 50);
}
