import 'package:flutter/material.dart';
import '../data/settings_model.dart';

class SettingsScreen extends StatelessWidget {
  final SettingsModel settings;
  const SettingsScreen({super.key, required this.settings});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الإعدادات')),
      body: Center(
        child: Text(
          'اللغة: ${settings.language}\nالوضع الليلي: ${settings.darkMode ? 'مفعل' : 'غير مفعل'}',
        ),
      ),
    );
  }
}
