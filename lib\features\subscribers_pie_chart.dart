import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

class Subscribers<PERSON><PERSON><PERSON>hart extends StatelessWidget {
  final int total;
  final int active;
  final int expired;
  final int debt;
  final int connected;
  const Subscribers<PERSON><PERSON><PERSON><PERSON>({
    required this.total,
    required this.active,
    required this.expired,
    required this.debt,
    required this.connected,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final List<PieChartSectionData> sections = [
      PieChartSectionData(
        color: Colors.green,
        value: active.toDouble(),
        title: 'نشط',
        radius: 38,
        titleStyle: const TextStyle(
          fontSize: 13,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      PieChartSectionData(
        color: Colors.redAccent,
        value: expired.toDouble(),
        title: 'منتهي',
        radius: 38,
        titleStyle: const TextStyle(
          fontSize: 13,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      PieChartSectionData(
        color: Colors.orange,
        value: debt.toDouble(),
        title: 'عليه دين',
        radius: 38,
        titleStyle: const TextStyle(
          fontSize: 13,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      PieChartSectionData(
        color: Colors.blue,
        value: connected.toDouble(),
        title: 'متصل',
        radius: 38,
        titleStyle: const TextStyle(
          fontSize: 13,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    ];
    return PieChart(
      PieChartData(
        sections: sections,
        centerSpaceRadius: 32,
        sectionsSpace: 2,
        borderData: FlBorderData(show: false),
        startDegreeOffset: 180,
        pieTouchData: PieTouchData(enabled: false),
      ),
      swapAnimationDuration: const Duration(milliseconds: 600),
    );
  }
}
