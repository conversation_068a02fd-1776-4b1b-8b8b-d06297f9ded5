import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../data/subscriber_model.dart';
import '../domain/subscribers_repository.dart';
import '../../../db_helper.dart';

class PayDebtScreen extends StatefulWidget {
  final Subscriber subscriber;
  final SubscribersRepository repository;
  const PayDebtScreen({
    super.key,
    required this.subscriber,
    required this.repository,
  });

  @override
  State<PayDebtScreen> createState() => _PayDebtScreenState();
}

class _PayDebtScreenState extends State<PayDebtScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController(
    text: '000',
  );
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();
  DateTime? _payDate;

  @override
  void initState() {
    super.initState();
    _payDate = DateTime.now();
    _updateDateController();
  }

  void _updateDateController() {
    final d = _payDate!;
    final hour = d.hour % 12 == 0 ? 12 : d.hour % 12;
    final ampm = d.hour >= 12 ? 'PM' : 'AM';
    _dateController.text =
        '${d.year}/${d.month.toString().padLeft(2, '0')}/${d.day.toString().padLeft(2, '0')} ($hour:${d.minute.toString().padLeft(2, '0')} $ampm)';
  }

  Future<void> _pickDate() async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: _payDate ?? now,
      firstDate: DateTime(now.year - 2),
      lastDate: DateTime(now.year + 2),
      locale: const Locale('ar'),
    );
    if (picked != null) {
      final pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_payDate ?? now),
        builder: (context, child) =>
            Directionality(textDirection: TextDirection.rtl, child: child!),
      );
      setState(() {
        _payDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          pickedTime?.hour ?? now.hour,
          pickedTime?.minute ?? now.minute,
        );
        _updateDateController();
      });
    }
  }

  void _pay() async {
    if (!_formKey.currentState!.validate() || _payDate == null) return;
    final amount =
        double.tryParse(_amountController.text.replaceAll(',', '')) ?? 0.0;
    final totalDebt = widget.subscriber.totalDebt;
    // إذا لم يكن على المشترك دين
    if (totalDebt == 0) {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (ctx) => AlertDialog(
          title: const Text('تنبيه'),
          content: const Text(
            'المشترك ليس عليه ديون. هل تريد إضافة المبلغ إلى المحفظة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(true),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
      if (confirm == true) {
        final updated = widget.subscriber.copyWith(
          notes: 'wallet:${amount.toStringAsFixed(0)}',
        );
        await widget.repository.updateSubscriber(updated);
        // سجل معاملة الإيداع هنا إذا لزم الأمر
        if (!mounted) return;
        Navigator.of(context).pop(true);
      }
      return;
    }
    // إذا كان المبلغ أكبر من الدين
    if (amount > totalDebt && totalDebt > 0) {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (ctx) => AlertDialog(
          title: const Text('تنبيه'),
          content: const Text(
            'ستقوم بتسديد مبلغ أعلى من دين المشترك. سيتم حفظ باقي المبلغ في المحفظة. هل تريد المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(true),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
      if (confirm != true) return;
      final deposit = amount - totalDebt;
      final updated = widget.subscriber.copyWith(
        totalDebt: 0,
        notes: 'wallet:${deposit.toStringAsFixed(0)}',
      );
      await widget.repository.updateSubscriber(updated);
      // سجل معاملة الإيداع هنا إذا لزم الأمر
      if (!mounted) return;
      Navigator.of(context).pop(true);
      return;
    }
    // تسديد عادي
    final updated = widget.subscriber.copyWith(totalDebt: totalDebt - amount);
    await widget.repository.updateSubscriber(updated);
    // سجل معاملة التسديد هنا إذا لزم الأمر
    // إرسال رسالة واتساب إذا كان الخيار مفعل ورقم الهاتف موجود
    try {
      // جلب حالة خيار التنبيهات من قاعدة البيانات
      final db = await DBHelper.instance.database;
      final notifyRow = await db.query(
        'settings',
        where: 'key = ?',
        whereArgs: ['notifyPayment'],
        limit: 1,
      );
      final notifyPayment =
          notifyRow.isNotEmpty &&
          (notifyRow.first['value'] == '1' ||
              notifyRow.first['value'] == true ||
              notifyRow.first['value'] == 'true');
      final phoneRaw = widget.subscriber.phone.trim();
      String phone = phoneRaw;
      // معالجة رقم الهاتف: إذا يبدأ بـ 0 يتم استبداله برمز الدولة الافتراضي (مثلاً العراق 964)
      if (phone.startsWith('0')) {
        phone = '964${phone.substring(1)}';
      }
      // إزالة أي رموز أو فراغات
      phone = phone.replaceAll(RegExp(r'[^0-9]'), '');
      if (notifyPayment && phone.isNotEmpty) {
        // جلب نص رسالة التسديد
        final payMsg = await DBHelper.instance.getMessage(
          'pay_msg',
          'تم استلام دفعتك بقيمة {المبلغ_المسدد}. المتبقي عليك: {الدين}. شكراً لك {الاسم}.',
        );
        // استبدال المتغيرات
        String msg = payMsg
            .replaceAll('{الاسم}', widget.subscriber.name)
            .replaceAll('{رقم_الهاتف}', phone)
            .replaceAll('{نوع_الاشتراك}', widget.subscriber.subscriptionType)
            .replaceAll(
              '{سعر_الاشتراك}',
              widget.subscriber.subscriptionPrice.toString(),
            )
            .replaceAll(
              '{تاريخ_البدء}',
              widget.subscriber.startDate.toString().split(' ').first,
            )
            .replaceAll(
              '{تاريخ_الانتهاء}',
              widget.subscriber.endDate.toString().split(' ').first,
            )
            .replaceAll('{الدين}', (updated.totalDebt).toString())
            .replaceAll('{المبلغ_المسدد}', amount.toString());
        // إرسال عبر واتساب
        final whatsappUrl = Uri.parse(
          'https://wa.me/$phone?text=${Uri.encodeComponent(msg)}',
        );
        print('WHATSAPP URL: $whatsappUrl');
        debugPrint('WHATSAPP URL: $whatsappUrl');
        try {
          final launched = await launchUrl(
            whatsappUrl,
            mode: LaunchMode.externalApplication,
          );
          if (!launched) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'تعذر فتح واتساب. تأكد من وجود التطبيق وصحة الرقم.',
                ),
              ),
            );
          }
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تعذر إرسال رسالة واتساب: $e')),
          );
        }
      }
    } catch (e) {
      // تجاهل الخطأ أو أظهر رسالة إذا رغبت
    }
    if (!mounted) return;
    Navigator.of(
      context,
    ).pop(true); // إرجاع true ليتمكن من استدعاء شاشة التفاصيل بعد التسديد
  }

  @override
  Widget build(BuildContext context) {
    final iconColor = Theme.of(context).iconTheme.color;
    return Scaffold(
      appBar: AppBar(
        title: const Text('تسديد مبلغ'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Padding(
          padding: const EdgeInsets.all(18.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                TextFormField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'المبلغ',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    suffixIcon: Icon(Icons.numbers, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                  validator: (v) => v == null || v.isEmpty || v == '000'
                      ? 'المبلغ مطلوب'
                      : null,
                ),
                const SizedBox(height: 14),
                TextFormField(
                  readOnly: true,
                  controller: _dateController,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'تاريخ التسديد',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    suffixIcon: Icon(Icons.calendar_month, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                  onTap: _pickDate,
                ),
                const SizedBox(height: 14),
                TextFormField(
                  controller: _noteController,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'ملاحظة',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    suffixIcon: Icon(Icons.copy, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                ),
                const SizedBox(height: 18),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton.icon(
                    onPressed: () {},
                    icon: const Icon(Icons.print),
                    label: const Text(''),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _pay,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: const Text('تسديد', style: TextStyle(fontSize: 18)),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
