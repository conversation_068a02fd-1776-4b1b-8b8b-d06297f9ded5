import 'package:flutter/material.dart';
import 'features/subscribers/data/subscriber_model.dart';
import 'features/subscribers/domain/subscribers_repository.dart';

class PayDebtScreen extends StatefulWidget {
  final Subscriber subscriber;
  final SubscribersRepository repository;
  const PayDebtScreen({
    super.key,
    required this.subscriber,
    required this.repository,
  });

  @override
  State<PayDebtScreen> createState() => _PayDebtScreenState();
}

class _PayDebtScreenState extends State<PayDebtScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _amountController = TextEditingController(
    text: '000',
  );
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _noteController = TextEditingController();
  DateTime? _payDate;

  @override
  void initState() {
    super.initState();
    _payDate = DateTime.now();
    _updateDateController();
  }

  void _updateDateController() {
    final d = _payDate!;
    final hour = d.hour % 12 == 0 ? 12 : d.hour % 12;
    final ampm = d.hour >= 12 ? 'PM' : 'AM';
    _dateController.text =
        '${d.year}/${d.month.toString().padLeft(2, '0')}/${d.day.toString().padLeft(2, '0')} ($hour:${d.minute.toString().padLeft(2, '0')} $ampm)';
  }

  Future<void> _pickDate() async {
    final now = DateTime.now();
    final picked = await showDatePicker(
      context: context,
      initialDate: _payDate ?? now,
      firstDate: DateTime(now.year - 2),
      lastDate: DateTime(now.year + 2),
      locale: const Locale('ar'),
    );
    if (picked != null) {
      final pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_payDate ?? now),
        builder: (context, child) =>
            Directionality(textDirection: TextDirection.rtl, child: child!),
      );
      setState(() {
        _payDate = DateTime(
          picked.year,
          picked.month,
          picked.day,
          pickedTime?.hour ?? now.hour,
          pickedTime?.minute ?? now.minute,
        );
        _updateDateController();
      });
    }
  }

  void _pay() async {
    if (!_formKey.currentState!.validate() || _payDate == null) return;
    final amount =
        double.tryParse(_amountController.text.replaceAll(',', '')) ?? 0.0;
    final totalDebt = widget.subscriber.totalDebt;

    if (amount > totalDebt && totalDebt > 0) {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (ctx) => AlertDialog(
          title: const Text('تنبيه'),
          content: const Text(
            'سوف تقوم بتسديد مبلغ أعلى من دين المشترك. هل تريد المتابعة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(false),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () => Navigator.of(ctx).pop(true),
              child: const Text('موافق'),
            ),
          ],
        ),
      );
      if (confirm != true) return;
      final deposit = amount - totalDebt;
      final updated = widget.subscriber.copyWith(
        totalDebt: 0,
        notes: 'deposit:${deposit.toStringAsFixed(0)}',
        boardId: widget.subscriber.boardId,
      );
      await widget.repository.updateSubscriber(updated);
      // سجل معاملة الإيداع هنا إذا لزم الأمر
      if (!mounted) return;
      Navigator.of(context).pop(true);
      return;
    }

    if (totalDebt == 0) {
      final updated = widget.subscriber.copyWith(
        notes: 'deposit:${amount.toStringAsFixed(0)}',
        boardId: widget.subscriber.boardId,
      );
      await widget.repository.updateSubscriber(updated);
      // سجل معاملة الإيداع هنا إذا لزم الأمر
      if (!mounted) return;
      Navigator.of(context).pop(true);
      return;
    }

    final updated = widget.subscriber.copyWith(
      totalDebt: totalDebt - amount,
      boardId: widget.subscriber.boardId,
    );
    await widget.repository.updateSubscriber(updated);
    // سجل معاملة التسديد هنا إذا لزم الأمر
    if (!mounted) return;
    Navigator.of(context).pop(true);
  }

  @override
  Widget build(BuildContext context) {
    final iconColor = Theme.of(context).iconTheme.color;
    return Scaffold(
      appBar: AppBar(
        title: const Text('تسديد مبلغ'),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Container(
        color: Theme.of(context).scaffoldBackgroundColor,
        child: Padding(
          padding: const EdgeInsets.all(18.0),
          child: Form(
            key: _formKey,
            child: ListView(
              children: [
                TextFormField(
                  controller: _amountController,
                  keyboardType: TextInputType.number,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'المبلغ',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    suffixIcon: Icon(Icons.numbers, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                  validator: (v) => v == null || v.isEmpty || v == '000'
                      ? 'المبلغ مطلوب'
                      : null,
                ),
                const SizedBox(height: 14),
                TextFormField(
                  readOnly: true,
                  controller: _dateController,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'تاريخ التسديد',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    suffixIcon: Icon(Icons.calendar_month, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                  onTap: _pickDate,
                ),
                const SizedBox(height: 14),
                TextFormField(
                  controller: _noteController,
                  style: TextStyle(
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  decoration: InputDecoration(
                    labelText: 'ملاحظة',
                    labelStyle: TextStyle(color: Theme.of(context).hintColor),
                    suffixIcon: Icon(Icons.copy, color: iconColor),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).cardColor,
                  ),
                ),
                const SizedBox(height: 18),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton.icon(
                    onPressed: () {},
                    icon: const Icon(Icons.print),
                    label: const Text(''),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[200],
                      foregroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: _pay,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24),
                      ),
                    ),
                    child: const Text('تسديد', style: TextStyle(fontSize: 18)),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
