# 📱 دليل ميزة "حساب واحد لكل هاتف"

## 🎯 **الهدف من الميزة:**

منع إنشاء أكثر من حساب واحد في نفس الجهاز لأسباب:
- **الأمان**: منع إساءة الاستخدام
- **التحكم**: ضبط عدد المستخدمين
- **الجودة**: تجربة مستخدم أفضل
- **المراقبة**: تتبع أفضل للاستخدام

## 🔧 **آلية العمل:**

### **1️⃣ فحص الجهاز قبل إنشاء الحساب:**
```dart
// في supabase_login_screen.dart
final deviceId = await getDeviceId();
final existingUserId = await AccountService.getExistingAccountForDevice(deviceId);

if (existingUserId != null && existingUserId != response.user!.id) {
  throw Exception('يوجد حساب آخر مرتبط بهذا الجهاز...');
}
```

### **2️⃣ فحص قاعدة البيانات:**
```dart
// في AccountService.getExistingAccountForDevice()
final response = await Supabase.instance.client
    .from('user_devices')
    .select('user_id, created_at')
    .eq('device_id', deviceId)
    .maybeSingle();

if (response != null) {
  return response['user_id'] as String; // حساب موجود
}
return null; // لا يوجد حساب
```

### **3️⃣ ربط الجهاز بالحساب:**
```dart
// في AccountService.linkDevice()
// التحقق من وجود حساب آخر أولاً
final existingUserId = await getExistingAccountForDevice(deviceId);
if (existingUserId != null && existingUserId != userId) {
  throw Exception('يوجد حساب آخر مرتبط بهذا الجهاز...');
}

// ربط الجهاز إذا لم يكن مرتبط بحساب آخر
await Supabase.instance.client.from('user_devices').insert(deviceData);
```

## 🎨 **واجهة المستخدم:**

### **🚨 حوار التحذير:**
عند محاولة إنشاء حساب ثاني، يظهر حوار يوضح:
- **المشكلة**: وجود حساب آخر مرتبط بالجهاز
- **السياسة**: حساب واحد فقط لكل جهاز
- **الخيارات المتاحة**:
  - تسجيل الدخول للحساب الموجود
  - استخدام جهاز آخر
  - التواصل مع الدعم

### **📱 تصميم الحوار:**
```dart
// في DeviceLimitDialog
AlertDialog(
  title: Row([
    Icon(Icons.warning_amber_rounded, color: Colors.orange),
    Text('حساب موجود مسبقاً'),
  ]),
  content: Column([
    Text('يوجد حساب آخر مرتبط بهذا الجهاز.'),
    Container(/* سياسة الجهاز الواحد */),
    Text('الخيارات المتاحة:'),
    _buildOption(Icons.login, 'تسجيل الدخول للحساب الموجود'),
    _buildOption(Icons.phone_android, 'استخدام جهاز آخر'),
    _buildOption(Icons.support_agent, 'التواصل مع الدعم'),
  ]),
)
```

## 🛡️ **الأمان والحماية:**

### **1️⃣ فحص متعدد المستويات:**
```dart
// المستوى الأول: فحص قبل إنشاء الحساب
final existingUserId = await AccountService.getExistingAccountForDevice(deviceId);

// المستوى الثاني: فحص عند ربط الجهاز
await AccountService.linkDevice(userId, deviceId);

// المستوى الثالث: قيود قاعدة البيانات
UNIQUE(user_id, device_id) // في جدول user_devices
```

### **2️⃣ معالجة الأخطاء:**
```dart
try {
  await AccountService.linkDevice(userId, deviceId);
} catch (e) {
  if (e.toString().contains('يوجد حساب آخر مرتبط بهذا الجهاز')) {
    // إظهار حوار التحذير
    showDialog(context: context, builder: (context) => DeviceLimitDialog());
    return; // لا نرمي استثناء
  }
  rethrow; // للأخطاء الأخرى
}
```

### **3️⃣ حماية قاعدة البيانات:**
```sql
-- في supabase_database_final.sql
CREATE TABLE user_devices (
  id SERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_id TEXT NOT NULL,
  device_name TEXT DEFAULT '',
  device_type TEXT DEFAULT '',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, device_id)  -- منع التكرار
);
```

## 🧪 **اختبار الميزة:**

### **1️⃣ اختبار الحالة العادية:**
```bash
flutter run
# 1. أنشئ حساب جديد
# 2. تحقق من ربط الجهاز في قاعدة البيانات
# 3. تأكد من نجاح العملية
```

### **2️⃣ اختبار منع الحساب الثاني:**
```bash
# 1. أنشئ حساب أول (ينجح)
# 2. سجل خروج
# 3. حاول إنشاء حساب ثاني (يفشل)
# 4. تحقق من ظهور حوار التحذير
```

### **3️⃣ اختبار تسجيل الدخول:**
```bash
# 1. أنشئ حساب
# 2. سجل خروج
# 3. سجل دخول بنفس البيانات (ينجح)
# 4. تحقق من عدم ظهور أخطاء
```

## 📊 **الحالات المختلفة:**

### **✅ حالات النجاح:**
- **إنشاء حساب أول**: ينجح ويربط الجهاز
- **تسجيل دخول**: ينجح للحساب المرتبط
- **ربط الجهاز**: ينجح للحساب الصحيح

### **❌ حالات الفشل:**
- **إنشاء حساب ثاني**: يفشل ويظهر حوار
- **ربط جهاز مرتبط**: يفشل مع رسالة واضحة
- **مشاكل قاعدة البيانات**: معالجة مناسبة

### **⚠️ حالات خاصة:**
- **جهاز جديد**: يسمح بإنشاء حساب
- **حذف الحساب**: يحرر الجهاز للاستخدام
- **مشاكل الشبكة**: معالجة مناسبة

## 🔧 **الملفات المضافة/المحدثة:**

### **الملفات الجديدة:**
1. **`lib/device_limit_dialog.dart`** - حوار التحذير

### **الملفات المحدثة:**
1. **`lib/services/account_service.dart`** - إضافة دوال فحص الجهاز
2. **`lib/supabase_login_screen.dart`** - إضافة فحص الجهاز وتفعيل الربط
3. **قاعدة البيانات** - جدول `user_devices` مع قيود مناسبة

## 🎯 **الدوال المضافة:**

### **1️⃣ فحص وجود حساب للجهاز:**
```dart
static Future<String?> getExistingAccountForDevice(String deviceId) async {
  final response = await Supabase.instance.client
      .from('user_devices')
      .select('user_id, created_at')
      .eq('device_id', deviceId)
      .maybeSingle();
  
  return response?['user_id'] as String?;
}
```

### **2️⃣ ربط الجهاز مع فحص:**
```dart
static Future<void> linkDevice(String userId, String deviceId, {String? deviceName}) async {
  // فحص وجود حساب آخر
  final existingUserId = await getExistingAccountForDevice(deviceId);
  if (existingUserId != null && existingUserId != userId) {
    throw Exception('يوجد حساب آخر مرتبط بهذا الجهاز...');
  }
  
  // ربط الجهاز
  await Supabase.instance.client.from('user_devices').insert(deviceData);
}
```

## 📈 **الفوائد المحققة:**

### **🛡️ الأمان:**
- **منع إساءة الاستخدام** - حساب واحد فقط
- **تتبع أفضل** - ربط واضح بين المستخدم والجهاز
- **حماية البيانات** - كل مستخدم له جهازه

### **📱 تجربة المستخدم:**
- **رسائل واضحة** - المستخدم يفهم السبب
- **خيارات متعددة** - حلول للمشكلة
- **تصميم جميل** - حوار احترافي

### **🔧 التطوير:**
- **كود منظم** - دوال واضحة ومفصولة
- **معالجة شاملة** - جميع الحالات مغطاة
- **سهولة الصيانة** - كود قابل للتطوير

## 🎉 **النتيجة النهائية:**

### **✅ ميزة مكتملة:**
- 🔒 **منع إنشاء حسابات متعددة** في نفس الجهاز
- 🛡️ **حماية قوية** على مستوى قاعدة البيانات والتطبيق
- 🎨 **واجهة مستخدم واضحة** مع حوار تحذير احترافي
- 📱 **تجربة مستخدم ممتازة** مع خيارات واضحة

### **🚀 جاهز للاستخدام:**
- **للمطورين**: دوال جاهزة وموثقة
- **للمستخدمين**: رسائل واضحة وخيارات مفيدة
- **للأمان**: حماية شاملة ومتعددة المستويات

**🎯 نظام "حساب واحد لكل هاتف" مكتمل واحترافي!**

---

## 📞 **للدعم:**

إذا واجهت مشاكل:
1. **تحقق من قاعدة البيانات** - جدول `user_devices`
2. **تحقق من الـ logs** - رسائل `[DEVICE_CHECK]` و `[LINK_DEVICE]`
3. **تحقق من الصلاحيات** - Row Level Security
4. **اختبر الحالات المختلفة** - حساب أول وثاني

**🚀 استمتع بالميزة الجديدة!**
