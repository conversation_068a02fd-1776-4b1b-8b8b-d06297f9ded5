import 'package:flutter/material.dart';

class AddBoardScreen extends StatefulWidget {
  const AddBoardScreen({
    super.key,
    this.onBoardAdded,
    this.initialBoard,
    this.isEdit = false,
  });
  final void Function(Map<String, dynamic>)? onBoardAdded;
  final Map<String, dynamic>? initialBoard;
  final bool isEdit;
  @override
  State<AddBoardScreen> createState() => _AddBoardScreenState();
}

class _AddBoardScreenState extends State<AddBoardScreen> {
  final TextEditingController sasServerController = TextEditingController();
  final TextEditingController sasUrlController = TextEditingController();
  final TextEditingController sasUserController = TextEditingController();
  final TextEditingController sasPassController = TextEditingController();
  bool passVisible = false;

  // متغيرات للتحقق من صحة الحقول
  String? urlError;
  String? userError;
  String? passError;

  @override
  void initState() {
    super.initState();
    if (widget.initialBoard != null) {
      final b = widget.initialBoard!;
      sasServerController.text = b['name'] ?? '';
      sasUrlController.text = b['url'] ?? '';
      sasUserController.text = b['user'] ?? '';
      sasPassController.text = b['pass'] ?? '';
    }
  }

  // دالة التحقق من صحة البيانات
  bool _validateFields() {
    setState(() {
      urlError = null;
      userError = null;
      passError = null;
    });

    bool isValid = true;

    // التحقق من حقل العنوان
    if (sasUrlController.text.trim().isEmpty) {
      setState(() {
        urlError = 'العنوان مطلوب';
      });
      isValid = false;
    }

    // التحقق من حقل اسم المستخدم
    if (sasUserController.text.trim().isEmpty) {
      setState(() {
        userError = 'اسم المستخدم مطلوب';
      });
      isValid = false;
    }

    // التحقق من حقل كلمة المرور
    if (sasPassController.text.trim().isEmpty) {
      setState(() {
        passError = 'كلمة المرور مطلوبة';
      });
      isValid = false;
    }

    return isValid;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // نموذج إدخال البيانات
                  _buildForm(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.18),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withValues(
              alpha: isDark ? 0.08 : 0.18,
            ),
            child: Icon(
              widget.isEdit ? Icons.edit : Icons.add,
              color: colorScheme.primary,
              size: 54,
            ),
          ),
        ),

        // عنوان الشاشة
        Text(
          widget.isEdit ? 'تعديل لوحة' : 'إضافة لوحة جديدة',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.isEdit
              ? 'تعديل بيانات اللوحة الحالية'
              : 'أدخل بيانات اللوحة الجديدة للاتصال',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء نموذج إدخال البيانات
  Widget _buildForm(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // حقل اسم السيرفر
            _buildInputField(
              controller: sasServerController,
              icon: Icons.dns,
              label: 'اسم السيرفر',
              hint: 'أدخل اسم مميز للسيرفر',
              colorScheme: colorScheme,
            ),

            const SizedBox(height: 20),

            // حقل العنوان
            _buildInputField(
              controller: sasUrlController,
              icon: Icons.link,
              label: 'العنوان',
              hint: 'example.com أو https://example.com',
              colorScheme: colorScheme,
              errorText: urlError,
              isRequired: true,
            ),

            const SizedBox(height: 20),

            // حقل اسم المستخدم
            _buildInputField(
              controller: sasUserController,
              icon: Icons.person,
              label: 'اسم المستخدم',
              hint: 'أدخل اسم المستخدم',
              colorScheme: colorScheme,
              errorText: userError,
              isRequired: true,
            ),

            const SizedBox(height: 20),

            // حقل كلمة المرور
            _buildPasswordField(colorScheme),

            const SizedBox(height: 32),

            // زر الحفظ/الإضافة
            _buildSubmitButton(colorScheme),
          ],
        ),
      ),
    );
  }

  // بناء حقل إدخال عادي
  Widget _buildInputField({
    required TextEditingController controller,
    required IconData icon,
    required String label,
    required String hint,
    required ColorScheme colorScheme,
    String? errorText,
    bool isRequired = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
            if (isRequired)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.red,
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          textDirection: TextDirection.ltr,
          style: TextStyle(color: colorScheme.onSurface, fontSize: 16),
          decoration: InputDecoration(
            prefixIcon: Icon(
              icon,
              color: errorText != null ? Colors.red : colorScheme.primary,
            ),
            hintText: hint,
            hintStyle: TextStyle(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            errorText: errorText,
            filled: true,
            fillColor: errorText != null
                ? Colors.red.withValues(alpha: 0.05)
                : colorScheme.primary.withValues(alpha: 0.05),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: errorText != null
                    ? Colors.red
                    : colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: errorText != null
                    ? Colors.red
                    : colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: errorText != null ? Colors.red : colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }

  // بناء حقل كلمة المرور
  Widget _buildPasswordField(ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'كلمة المرور',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: sasPassController,
          obscureText: !passVisible,
          textDirection: TextDirection.ltr,
          style: TextStyle(color: colorScheme.onSurface, fontSize: 16),
          decoration: InputDecoration(
            prefixIcon: Icon(
              Icons.fingerprint,
              color: passError != null ? Colors.red : colorScheme.primary,
            ),
            suffixIcon: IconButton(
              icon: Icon(
                passVisible ? Icons.visibility : Icons.visibility_off,
                color: passError != null ? Colors.red : colorScheme.primary,
              ),
              onPressed: () => setState(() => passVisible = !passVisible),
            ),
            hintText: 'أدخل كلمة المرور',
            hintStyle: TextStyle(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            errorText: passError,
            filled: true,
            fillColor: passError != null
                ? Colors.red.withValues(alpha: 0.05)
                : colorScheme.primary.withValues(alpha: 0.05),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: passError != null
                    ? Colors.red
                    : colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: passError != null
                    ? Colors.red
                    : colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: BorderSide(
                color: passError != null ? Colors.red : colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
          ),
        ),
      ],
    );
  }

  // بناء زر الحفظ/الإضافة
  Widget _buildSubmitButton(ColorScheme colorScheme) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: () async {
          // التحقق من صحة البيانات
          if (!_validateFields()) {
            return;
          }

          final board = {
            'name': sasServerController.text.trim().isEmpty
                ? sasUrlController.text.trim()
                : sasServerController.text.trim(),
            'url': sasUrlController.text.trim(),
            'user': sasUserController.text.trim(),
            'pass': sasPassController.text.trim(),
            'type': 'SAS',
            'connected': true,
            'active': true,
          };
          if (widget.onBoardAdded != null) {
            widget.onBoardAdded!(board);
          }
          Navigator.of(context).pop(board);
        },
        icon: Icon(widget.isEdit ? Icons.save : Icons.add, size: 24),
        label: Text(
          widget.isEdit ? 'حفظ التعديلات' : 'إضافة اللوحة',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          shadowColor: Colors.transparent,
        ),
      ),
    );
  }
}
