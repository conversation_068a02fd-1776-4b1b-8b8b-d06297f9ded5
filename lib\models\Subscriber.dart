enum SubscriberStatus { active, inactive }

class Subscriber {
  int? id;
  String name;
  SubscriberStatus status;
  double totalDebt;
  String user;
  double subscriptionPrice;
  String subscriptionType;
  DateTime startDate;
  DateTime endDate;
  int remainingDays;
  String phone;
  String? notes;

  Subscriber({
    this.id,
    required this.name,
    required this.status,
    required this.totalDebt,
    required this.user,
    required this.subscriptionPrice,
    required this.subscriptionType,
    required this.startDate,
    required this.endDate,
    required this.remainingDays,
    required this.phone,
    this.notes,
  });

  factory Subscriber.fromMap(Map<String, dynamic> map) {
    return Subscriber(
      id: map['id'],
      name: map['name'],
      status: SubscriberStatus.values[map['status'] ?? 0],
      totalDebt: map['totalDebt']?.toDouble() ?? 0.0,
      user: map['user'] ?? '',
      subscriptionPrice: map['subscriptionPrice']?.toDouble() ?? 0.0,
      subscriptionType: map['subscriptionType'] ?? '',
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      remainingDays: map['remainingDays'] ?? 0,
      phone: map['phone'],
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'status': status.index,
      'totalDebt': totalDebt,
      'user': user,
      'subscriptionPrice': subscriptionPrice,
      'subscriptionType': subscriptionType,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'remainingDays': remainingDays,
      'phone': phone,
      'notes': notes,
    };
  }

  Subscriber copyWith({
    int? id,
    String? name,
    SubscriberStatus? status,
    double? totalDebt,
    String? user,
    double? subscriptionPrice,
    String? subscriptionType,
    DateTime? startDate,
    DateTime? endDate,
    int? remainingDays,
    String? phone,
    String? notes,
  }) {
    return Subscriber(
      id: id ?? this.id,
      name: name ?? this.name,
      status: status ?? this.status,
      totalDebt: totalDebt ?? this.totalDebt,
      user: user ?? this.user,
      subscriptionPrice: subscriptionPrice ?? this.subscriptionPrice,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      remainingDays: remainingDays ?? this.remainingDays,
      phone: phone ?? this.phone,
      notes: notes ?? this.notes,
    );
  }
}
