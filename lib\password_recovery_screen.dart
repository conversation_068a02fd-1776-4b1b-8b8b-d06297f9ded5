import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'services/supabase_auth_service.dart';
import 'supabase_login_screen.dart';

/// شاشة تحديث كلمة المرور المخصصة لعملية الاستعادة
/// هذه الشاشة مقيدة ولا يمكن الخروج منها إلا بتحديث كلمة المرور أو الإلغاء
class PasswordRecoveryScreen extends StatefulWidget {
  const PasswordRecoveryScreen({super.key});

  @override
  State<PasswordRecoveryScreen> createState() => _PasswordRecoveryScreenState();
}

class _PasswordRecoveryScreenState extends State<PasswordRecoveryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _authService = SupabaseAuthService();

  bool _isLoading = false;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;
  String? _message;
  bool _isSuccess = false;

  @override
  void dispose() {
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  /// تحديث كلمة المرور
  Future<void> _updatePassword() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _message = null;
      _isSuccess = false;
    });

    try {
      debugPrint('🔒 [PASSWORD_RECOVERY] بدء تحديث كلمة المرور');

      await _authService.updatePassword(_newPasswordController.text);

      debugPrint('✅ [PASSWORD_RECOVERY] تم تحديث كلمة المرور بنجاح');

      setState(() {
        _isSuccess = true;
        _message = 'تم تحديث كلمة المرور بنجاح!';
      });

      // إظهار رسالة النجاح لمدة 3 ثوان ثم الانتقال لشاشة تسجيل الدخول
      Future.delayed(const Duration(seconds: 3), () {
        _navigateToLogin();
      });
    } catch (e) {
      debugPrint('❌ [PASSWORD_RECOVERY] خطأ في تحديث كلمة المرور: $e');

      setState(() {
        _isSuccess = false;
        _message = _getErrorMessage(e.toString());
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// الانتقال لشاشة تسجيل الدخول
  void _navigateToLogin() {
    debugPrint('🔄 [PASSWORD_RECOVERY] الانتقال لشاشة تسجيل الدخول');

    // تسجيل خروج المستخدم الحالي
    Supabase.instance.client.auth.signOut();

    // الانتقال لشاشة تسجيل الدخول مع إزالة جميع الشاشات السابقة
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const SupabaseLoginScreen()),
      (route) => false,
    );
  }

  /// إلغاء عملية التحديث
  void _cancelUpdate() {
    debugPrint('❌ [PASSWORD_RECOVERY] إلغاء عملية تحديث كلمة المرور');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إلغاء التحديث'),
        content: const Text('هل أنت متأكد من إلغاء عملية تحديث كلمة المرور؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('لا'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin();
            },
            child: const Text('نعم'),
          ),
        ],
      ),
    );
  }

  /// الحصول على رسالة الخطأ
  String _getErrorMessage(String error) {
    if (error.contains('Password should be at least')) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else if (error.contains('weak')) {
      return 'كلمة المرور ضعيفة. استخدم مزيج من الأحرف والأرقام';
    } else if (error.contains('network')) {
      return 'مشكلة في الاتصال بالإنترنت';
    } else {
      return 'حدث خطأ في تحديث كلمة المرور. يرجى المحاولة مرة أخرى';
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return PopScope(
      canPop: false, // منع الخروج من الشاشة بزر الرجوع
      child: Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: colorScheme.surface,
        body: Stack(
          children: [
            // خلفية متدرجة عصرية
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: isDark
                      ? [
                          colorScheme.primary.withValues(alpha: 0.9),
                          colorScheme.surface.withValues(alpha: 0.85),
                        ]
                      : [
                          colorScheme.primary.withValues(alpha: 0.1),
                          colorScheme.surface,
                        ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
            ),
            SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 24,
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 32),

                      // رأس الشاشة العصري
                      _buildHeader(colorScheme, isDark),
                      const SizedBox(height: 48),

                      // قسم إدخال كلمات المرور
                      _buildPasswordSection(colorScheme, isDark),
                      const SizedBox(height: 32),

                      // قسم الرسائل والحالة
                      if (_message != null) ...[
                        _buildMessageSection(colorScheme),
                        const SizedBox(height: 24),
                      ],

                      // زر الإلغاء
                      if (!_isSuccess) _buildCancelButton(colorScheme),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء رأس الشاشة العصري
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 24),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [
                colorScheme.primary,
                colorScheme.primary.withValues(alpha: 0.7),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withValues(alpha: 0.1),
            ),
            child: Icon(
              Icons.lock_reset_rounded,
              size: 50,
              color: Colors.white,
            ),
          ),
        ),

        // العنوان الرئيسي
        Text(
          'استعادة كلمة المرور',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 12),

        // الوصف
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'أدخل كلمة المرور الجديدة وتأكد من تأكيدها',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  // بناء قسم إدخال كلمات المرور
  Widget _buildPasswordSection(ColorScheme colorScheme, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDark
            ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.3)
            : Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // حقل كلمة المرور الجديدة
          TextFormField(
            controller: _newPasswordController,
            obscureText: _obscureNewPassword,
            decoration: InputDecoration(
              labelText: 'كلمة المرور الجديدة',
              prefixIcon: Icon(Icons.lock_rounded, color: colorScheme.primary),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureNewPassword
                      ? Icons.visibility_rounded
                      : Icons.visibility_off_rounded,
                  color: colorScheme.primary,
                ),
                onPressed: () {
                  setState(() {
                    _obscureNewPassword = !_obscureNewPassword;
                  });
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.3,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى إدخال كلمة المرور الجديدة';
              }
              if (value.length < 6) {
                return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          // حقل تأكيد كلمة المرور
          TextFormField(
            controller: _confirmPasswordController,
            obscureText: _obscureConfirmPassword,
            decoration: InputDecoration(
              labelText: 'تأكيد كلمة المرور',
              prefixIcon: Icon(
                Icons.lock_outline_rounded,
                color: colorScheme.secondary,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureConfirmPassword
                      ? Icons.visibility_rounded
                      : Icons.visibility_off_rounded,
                  color: colorScheme.secondary,
                ),
                onPressed: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              filled: true,
              fillColor: colorScheme.surfaceContainerHighest.withValues(
                alpha: 0.3,
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى تأكيد كلمة المرور';
              }
              if (value != _newPasswordController.text) {
                return 'كلمة المرور غير متطابقة';
              }
              return null;
            },
          ),

          const SizedBox(height: 24),

          // زر التحديث
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _updatePassword,
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.security_update_rounded, size: 20),
              label: Text(
                _isLoading ? 'جاري التحديث...' : 'تحديث كلمة المرور',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء قسم الرسائل والحالة
  Widget _buildMessageSection(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _isSuccess
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isSuccess
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _isSuccess ? Icons.check_circle_rounded : Icons.error_rounded,
            color: _isSuccess ? Colors.green : Colors.red,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              _message!,
              style: TextStyle(
                color: _isSuccess ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء زر الإلغاء
  Widget _buildCancelButton(ColorScheme colorScheme) {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      child: TextButton.icon(
        onPressed: _cancelUpdate,
        icon: Icon(Icons.close_rounded, size: 20, color: colorScheme.error),
        label: Text(
          'إلغاء وتسجيل الدخول',
          style: TextStyle(
            color: colorScheme.error,
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}
