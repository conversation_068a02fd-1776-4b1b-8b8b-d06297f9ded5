import 'dart:convert';
import 'dart:io';
import 'package:archive/archive.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import '../db_helper.dart';
import '../features/subscribers/data/subscriber_model.dart';
import '../features/subscribers/data/transaction_model.dart' as app_transaction;
import '../core/managers/internet_status_manager.dart';
import 'account_service.dart';
import 'daily_sync_limits.dart';

/// خدمة المزامنة السحابية المحسنة مع الضغط والتشفير
class EnhancedSyncService {
  static final EnhancedSyncService _instance = EnhancedSyncService._internal();
  factory EnhancedSyncService() => _instance;
  EnhancedSyncService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  final DBHelper _dbHelper = DBHelper.instance;
  // استخدام AccountService الثابت

  bool _isSyncing = false;

  /// فحص إمكانية المزامنة
  Future<Map<String, dynamic>> checkSyncEligibility({
    bool isManual = true,
  }) async {
    try {
      // فحص الاتصال بالإنترنت
      if (!InternetStatusManager.isConnected) {
        return {
          'canSync': false,
          'reason': 'لا يوجد اتصال بالإنترنت',
          'code': 'NO_INTERNET',
          'showNotification': true,
        };
      }

      // فحص حالة الحساب
      final user = _supabase.auth.currentUser;
      if (user == null) {
        return {
          'canSync': false,
          'reason': 'المستخدم غير مسجل الدخول',
          'code': 'NOT_AUTHENTICATED',
          'showNotification': false,
        };
      }

      // فحص حدود المزامنة المحلية
      if (isManual) {
        final manualLimits = await DailySyncLimits.canPerformManualSync();
        if (!manualLimits['canSync']) {
          return {
            'canSync': false,
            'reason': manualLimits['message'],
            'code': 'MANUAL_LIMIT_REACHED',
            'currentCount': manualLimits['currentCount'],
            'maxCount': manualLimits['maxCount'],
            'remaining': manualLimits['remaining'],
            'resetTime': manualLimits['resetTime'],
            'showNotification': true,
            'isLimitReached': true,
          };
        }
      } else {
        final autoLimits = await DailySyncLimits.canPerformAutoSync();
        if (!autoLimits['canSync']) {
          return {
            'canSync': false,
            'reason': autoLimits['message'],
            'code': 'AUTO_LIMIT_REACHED',
            'currentCount': autoLimits['currentCount'],
            'maxCount': autoLimits['maxCount'],
            'showNotification': false, // المزامنة التلقائية صامتة
            'isAutoLimitReached': true,
          };
        }
      }

      // فحص حالة الحساب
      final accountData = await AccountService.getAccountDataV2(user.id);
      final accountStatus = accountData?['account_status'] as String?;
      if (accountStatus == 'expired' || accountStatus == 'banned') {
        return {
          'canSync': false,
          'reason': 'انتهت صلاحية الحساب',
          'code': 'ACCOUNT_EXPIRED',
          'showNotification': true,
        };
      }

      // تقدير حجم البيانات
      final dataSize = await _estimateDataSize();
      final dataSizeMB = (dataSize / 1024 / 1024).ceil();

      // فحص حدود المستخدم في السحابة
      final limitsCheck = await _checkUserLimits(user.id, dataSizeMB);

      return {
        'canSync': limitsCheck['can_sync'],
        'reason': limitsCheck['can_sync']
            ? 'يمكن المزامنة'
            : limitsCheck['reasons'].join(', '),
        'code': limitsCheck['can_sync'] ? 'OK' : 'LIMITS_EXCEEDED',
        'dataSize': dataSize,
        'dataSizeMB': dataSizeMB,
        'limits': limitsCheck,
        'isManual': isManual,
        'showNotification': false,
      };
    } catch (e) {
      return {
        'canSync': false,
        'reason': 'خطأ في فحص إمكانية المزامنة: $e',
        'code': 'CHECK_ERROR',
        'showNotification': true,
      };
    }
  }

  /// تنفيذ المزامنة الكاملة
  Future<Map<String, dynamic>> performFullSync({bool isManual = true}) async {
    if (_isSyncing) {
      return {
        'success': false,
        'message': 'المزامنة قيد التنفيذ بالفعل',
        'code': 'ALREADY_SYNCING',
      };
    }

    _isSyncing = true;
    final startTime = DateTime.now();
    String? logId;

    try {
      final syncType = isManual ? 'يدوية' : 'تلقائية';
      debugPrint('🔄 بدء المزامنة السحابية المحسنة ($syncType)...');

      // فحص إمكانية المزامنة
      final eligibilityCheck = await checkSyncEligibility(isManual: isManual);
      if (!eligibilityCheck['canSync']) {
        return {
          'success': false,
          'message': eligibilityCheck['reason'],
          'code': eligibilityCheck['code'],
          'isManual': isManual,
          'limits': eligibilityCheck['limits'],
        };
      }

      // إظهار رسالة التنظيف التلقائي إذا حدث
      final limits = eligibilityCheck['limits'];
      if (limits != null && limits['cleanup_performed'] == true) {
        final deletedCount = limits['deleted_old_backups'] ?? 0;
        debugPrint(
          '🧹 تم حذف $deletedCount نسخة قديمة تلقائياً لإفساح المجال للنسخة الجديدة',
        );
      }

      final user = _supabase.auth.currentUser!;
      final deviceId = await _getDeviceId();

      // تسجيل بداية العملية
      logId = await _logSyncOperation(
        user.id,
        deviceId,
        'upload',
        status: 'started',
        details: {'estimated_size_mb': eligibilityCheck['dataSizeMB']},
      );

      // المرحلة 1: جمع البيانات
      await _updateSyncLog(logId, 'compressing', 10);
      debugPrint('📊 جاري جمع البيانات من قاعدة البيانات المحلية...');

      final backupData = await _exportDatabaseData();

      // المرحلة 2: ضغط البيانات
      await _updateSyncLog(logId, 'compressing', 30);
      debugPrint('🗜️ جاري ضغط البيانات...');

      final compressionResult = await _compressData(backupData);
      final compressedData = compressionResult['data'] as Uint8List;
      final originalSize = compressionResult['originalSize'] as int;
      final compressedSize = compressedData.length;
      final compressionRatio = ((1.0 - compressedSize / originalSize) * 100);

      // المرحلة 3: إنشاء checksum
      await _updateSyncLog(logId, 'uploading', 50);
      final checksum = _generateChecksum(compressedData);

      // المرحلة 4: رفع الملف
      debugPrint('☁️ جاري رفع النسخة المضغوطة إلى Supabase...');

      final fileName = _generateBackupFileName(user.id);
      final filePath = '${user.id}/$fileName';

      await _supabase.storage
          .from('userbackups')
          .uploadBinary(filePath, compressedData);

      await _updateSyncLog(logId, 'verifying', 80);

      // المرحلة 5: حفظ معلومات النسخة في قاعدة البيانات
      final backupId = await _saveBackupMetadata(
        user.id,
        deviceId,
        fileName,
        filePath,
        originalSize,
        compressedSize,
        checksum,
        backupData,
      );

      // المرحلة 6: تنظيف النسخ القديمة
      await _updateSyncLog(logId, 'verifying', 90);
      debugPrint('🧹 جاري تنظيف النسخ القديمة...');

      final cleanupResult = await _cleanupOldBackups(user.id);

      // المرحلة 7: تحديث الإحصائيات
      final duration = DateTime.now().difference(startTime);
      await _updateSyncStatistics(
        user.id,
        deviceId,
        true,
        compressedSize,
        duration,
        compressionRatio,
      );

      await _updateSyncLog(logId, 'completed', 100);
      debugPrint('✅ تمت المزامنة بنجاح!');

      // تسجيل المزامنة في العدادات المحلية
      if (isManual) {
        await DailySyncLimits.recordManualSync();
        debugPrint('📊 تم تسجيل مزامنة يدوية في العدادات المحلية');
      } else {
        await DailySyncLimits.recordAutoSync();
        debugPrint('🤖 تم تسجيل مزامنة تلقائية في العدادات المحلية');
      }

      // إنشاء رسالة النجاح مع معلومات التنظيف
      String successMessage = 'تمت المزامنة الشاملة بنجاح';
      if (limits != null && limits['cleanup_performed'] == true) {
        final deletedCount = limits['deleted_old_backups'] ?? 0;
        successMessage += ' (تم حذف $deletedCount نسخة قديمة تلقائياً)';
      }

      return {
        'success': true,
        'message': successMessage,
        'backupId': backupId,
        'fileName': fileName,
        'originalSize': originalSize,
        'compressedSize': compressedSize,
        'compressionRatio': compressionRatio.toStringAsFixed(2),
        'duration': duration.inSeconds,
        'cleanupResult': cleanupResult,
        'dynamicCleanup': limits?['cleanup_performed'] ?? false,
        'deletedOldBackups': limits?['deleted_old_backups'] ?? 0,
        'code': 'SUCCESS',
        'isManual': isManual,
      };
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة: $e');

      if (logId != null) {
        await _updateSyncLog(logId, 'failed', null, e.toString(), 'SYNC_ERROR');
      }

      // تحديث إحصائيات الفشل
      final user = _supabase.auth.currentUser;
      if (user != null) {
        final deviceId = await _getDeviceId();
        final duration = DateTime.now().difference(startTime);
        await _updateSyncStatistics(
          user.id,
          deviceId,
          false,
          0,
          duration,
          0,
          errorMessage: e.toString(),
        );
      }

      return {
        'success': false,
        'message': 'فشلت المزامنة: $e',
        'code': 'SYNC_ERROR',
      };
    } finally {
      _isSyncing = false;
    }
  }

  /// تقدير حجم البيانات الشاملة
  Future<int> _estimateDataSize() async {
    final subscribers = await _dbHelper.getAllSubscribers();
    final transactions = await _dbHelper.getAllTransactions();
    final subscriptions = await _getAllSubscriptions();
    final devices = await _getAllDevices();
    final servers = await _getAllServers();
    final accountStatus = await _getAllAccountStatus();
    final settings = await _getAllSettings();
    final boards = await _getAllBoards();
    final sharedPrefs = await _getSharedPreferences();
    final fileInfo = await _getFileInformation();

    final data = {
      'subscribers': subscribers.map((s) => s.toMap()).toList(),
      'transactions': transactions.map((t) => t.toMap()).toList(),
      'subscriptions': subscriptions,
      'devices': devices,
      'servers': servers,
      'account_status': accountStatus,
      'settings': settings,
      'boards': boards,
      'shared_preferences': sharedPrefs,
      'file_info': fileInfo,
    };

    final jsonString = jsonEncode(data);
    final estimatedSize = utf8.encode(jsonString).length;

    debugPrint(
      '[ENHANCED_SYNC] تقدير حجم البيانات الشاملة: ${(estimatedSize / 1024).toStringAsFixed(2)} KB',
    );
    return estimatedSize;
  }

  /// فحص حدود المستخدم مع المزامنة الديناميكية
  Future<Map<String, dynamic>> _checkUserLimits(
    String userId,
    int backupSizeMB,
  ) async {
    final response = await _supabase.rpc(
      'check_sync_limits_dynamic',
      params: {'target_user_id': userId, 'backup_size_mb': backupSizeMB},
    );

    return Map<String, dynamic>.from(response);
  }

  /// تصدير بيانات قاعدة البيانات الشاملة
  Future<Map<String, dynamic>> _exportDatabaseData() async {
    debugPrint('[ENHANCED_SYNC] بدء تصدير البيانات الشاملة...');

    // جمع بيانات المشتركين والمعاملات
    final subscribers = await _dbHelper.getAllSubscribers();
    final transactions = await _dbHelper.getAllTransactions();
    debugPrint(
      '[ENHANCED_SYNC] تم جمع ${subscribers.length} مشترك و ${transactions.length} معاملة',
    );

    // جمع بيانات الباقات/الاشتراكات
    final subscriptions = await _getAllSubscriptions();
    debugPrint('[ENHANCED_SYNC] تم جمع ${subscriptions.length} باقة');

    // جمع بيانات الأجهزة
    final devices = await _getAllDevices();
    debugPrint('[ENHANCED_SYNC] تم جمع ${devices.length} جهاز');

    // جمع بيانات السيرفرات
    final servers = await _getAllServers();
    debugPrint('[ENHANCED_SYNC] تم جمع ${servers.length} سيرفر');

    // جمع بيانات حالة الحساب
    final accountStatus = await _getAllAccountStatus();
    debugPrint('[ENHANCED_SYNC] تم جمع ${accountStatus.length} حالة حساب');

    // جمع إعدادات التطبيق
    final settings = await _getAllSettings();
    debugPrint('[ENHANCED_SYNC] تم جمع ${settings.length} إعداد');

    // جمع معلومات اللوحات/السيرفرات
    final boards = await _getAllBoards();
    debugPrint('[ENHANCED_SYNC] تم جمع ${boards.length} لوحة');

    // جمع إعدادات SharedPreferences
    final sharedPrefs = await _getSharedPreferences();
    debugPrint('[ENHANCED_SYNC] تم جمع ${sharedPrefs.length} إعداد مشترك');

    // جمع معلومات الملفات
    final fileInfo = await _getFileInformation();
    debugPrint('[ENHANCED_SYNC] تم جمع معلومات ${fileInfo.length} ملف');

    return {
      'version': '4.0.0',
      'timestamp': DateTime.now().toIso8601String(),
      'app_name': 'iTower',
      'backup_type': 'comprehensive_database_compressed',
      'compression': 'gzip',
      'data': {
        'subscribers': subscribers.map((s) => s.toMap()).toList(),
        'transactions': transactions.map((t) => t.toMap()).toList(),
        'subscriptions': subscriptions,
        'devices': devices,
        'servers': servers,
        'account_status': accountStatus,
        'settings': settings,
        'boards': boards,
        'shared_preferences': sharedPrefs,
        'file_info': fileInfo,
      },
      'metadata': {
        'total_subscribers': subscribers.length,
        'total_transactions': transactions.length,
        'total_subscriptions': subscriptions.length,
        'total_devices': devices.length,
        'total_servers': servers.length,
        'total_account_status': accountStatus.length,
        'total_settings': settings.length,
        'total_boards': boards.length,
        'total_shared_prefs': sharedPrefs.length,
        'total_files': fileInfo.length,
        'export_date': DateTime.now().toIso8601String(),
        'backup_scope': 'comprehensive',
      },
    };
  }

  /// ضغط البيانات باستخدام GZip
  Future<Map<String, dynamic>> _compressData(Map<String, dynamic> data) async {
    final jsonString = jsonEncode(data);
    final originalBytes = utf8.encode(jsonString);
    final originalSize = originalBytes.length;

    // ضغط البيانات
    final encoder = GZipEncoder();
    final compressedBytes = encoder.encode(originalBytes);

    return {
      'data': Uint8List.fromList(compressedBytes),
      'originalSize': originalSize,
      'compressedSize': compressedBytes.length,
    };
  }

  /// فك ضغط البيانات
  Future<Map<String, dynamic>> _decompressData(Uint8List compressedData) async {
    final decoder = GZipDecoder();
    final decompressedBytes = decoder.decodeBytes(compressedData);
    final jsonString = utf8.decode(decompressedBytes);
    return jsonDecode(jsonString);
  }

  /// إنشاء checksum للتحقق من سلامة البيانات
  String _generateChecksum(Uint8List data) {
    final digest = sha256.convert(data);
    return digest.toString();
  }

  /// إنشاء اسم ملف النسخة الاحتياطية
  String _generateBackupFileName(String userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'itower_backup_${userId}_$timestamp.gz';
  }

  /// الحصول على معرف الجهاز
  Future<String> _getDeviceId() async {
    // يمكن استخدام device_info_plus للحصول على معرف فريد
    return 'device_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// تسجيل عملية مزامنة
  Future<String> _logSyncOperation(
    String userId,
    String deviceId,
    String operationType, {
    String? backupId,
    String status = 'started',
    Map<String, dynamic>? details,
    int dataSize = 0,
    String? errorMessage,
    String? errorCode,
  }) async {
    final response = await _supabase.rpc(
      'log_sync_operation',
      params: {
        'p_user_id': userId,
        'p_device_id': deviceId,
        'p_operation_type': operationType,
        'p_backup_id': backupId,
        'p_status': status,
        'p_details': details ?? {},
        'p_data_size': dataSize,
        'p_error_message': errorMessage,
        'p_error_code': errorCode,
      },
    );

    return response.toString();
  }

  /// تحديث حالة عملية المزامنة
  Future<void> _updateSyncLog(
    String logId,
    String status, [
    int? progress,
    String? errorMessage,
    String? errorCode,
    Map<String, dynamic>? metadata,
  ]) async {
    await _supabase.rpc(
      'update_sync_operation',
      params: {
        'p_log_id': logId,
        'p_status': status,
        'p_progress': progress,
        'p_error_message': errorMessage,
        'p_error_code': errorCode,
        'p_metadata': metadata,
      },
    );
  }

  /// حفظ معلومات النسخة الاحتياطية
  Future<String> _saveBackupMetadata(
    String userId,
    String deviceId,
    String fileName,
    String filePath,
    int originalSize,
    int compressedSize,
    String checksum,
    Map<String, dynamic> backupData,
  ) async {
    final metadata = backupData['metadata'] as Map<String, dynamic>;

    final response = await _supabase
        .from('compressed_backups')
        .insert({
          'user_id': userId,
          'device_id': deviceId,
          'backup_name': fileName,
          'file_path': filePath,
          'original_size': originalSize,
          'compressed_size': compressedSize,
          'checksum': checksum,
          'backup_version': backupData['version'],
          'backup_type': backupData['backup_type'],
          'total_subscribers': metadata['total_subscribers'],
          'total_transactions': metadata['total_transactions'],
          'app_version': '1.0.0', // يمكن الحصول عليها من package_info
          'platform': Platform.operatingSystem,
          'device_info': {
            'platform': Platform.operatingSystem,
            'version': Platform.operatingSystemVersion,
          },
          'compression_algorithm': 'gzip',
        })
        .select('id')
        .single();

    return response['id'].toString();
  }

  /// تنظيف النسخ القديمة
  Future<Map<String, dynamic>> _cleanupOldBackups(String userId) async {
    final response = await _supabase.rpc(
      'cleanup_old_backups',
      params: {'target_user_id': userId},
    );

    // حذف الملفات من Storage
    final deletedFiles = response[0]['deleted_files'] as List<dynamic>;
    for (final filePath in deletedFiles) {
      try {
        await _supabase.storage.from('userbackups').remove([filePath]);
      } catch (e) {
        debugPrint('تحذير: فشل حذف الملف $filePath: $e');
      }
    }

    return {
      'deletedCount': response[0]['deleted_count'],
      'freedSpaceMB': response[0]['freed_space_mb'],
      'deletedFiles': deletedFiles,
    };
  }

  /// تحديث إحصائيات المزامنة
  Future<void> _updateSyncStatistics(
    String userId,
    String deviceId,
    bool success,
    int backupSize,
    Duration duration,
    double compressionRatio, {
    String? errorMessage,
  }) async {
    await _supabase.rpc(
      'update_sync_statistics',
      params: {
        'target_user_id': userId,
        'target_device_id': deviceId,
        'sync_success': success,
        'backup_size': backupSize,
        'sync_duration': '${duration.inSeconds} seconds',
        'compression_ratio': compressionRatio,
        'error_msg': errorMessage,
      },
    );
  }

  /// الحصول على لوحة معلومات المزامنة
  Future<Map<String, dynamic>> getUserSyncDashboard() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    final response = await _supabase.rpc(
      'get_user_sync_dashboard',
      params: {'target_user_id': user.id},
    );

    return Map<String, dynamic>.from(response);
  }

  /// الحصول على قائمة النسخ الاحتياطية
  Future<List<Map<String, dynamic>>> getUserBackups() async {
    final user = _supabase.auth.currentUser;
    if (user == null) {
      throw Exception('المستخدم غير مسجل الدخول');
    }

    final response = await _supabase
        .from('compressed_backups')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', ascending: false);

    return List<Map<String, dynamic>>.from(response);
  }

  /// استعادة نسخة احتياطية
  Future<Map<String, dynamic>> restoreBackup(String backupId) async {
    try {
      debugPrint('🔄 بدء استعادة النسخة الاحتياطية...');

      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // الحصول على معلومات النسخة
      final backupInfo = await _supabase
          .from('compressed_backups')
          .select('*')
          .eq('id', backupId)
          .eq('user_id', user.id)
          .single();

      // تحميل الملف من Storage
      final fileData = await _supabase.storage
          .from('userbackups')
          .download(backupInfo['file_path']);

      // فك الضغط
      final decompressedData = await _decompressData(fileData);

      // التحقق من checksum
      final calculatedChecksum = _generateChecksum(fileData);
      if (calculatedChecksum != backupInfo['checksum']) {
        throw Exception('فشل التحقق من سلامة البيانات');
      }

      // استعادة البيانات
      await _restoreDataToDatabase(decompressedData);

      debugPrint('✅ تمت استعادة النسخة الاحتياطية بنجاح!');

      return {
        'success': true,
        'message': 'تمت الاستعادة بنجاح',
        'backupInfo': backupInfo,
      };
    } catch (e) {
      debugPrint('❌ خطأ في استعادة النسخة: $e');
      return {'success': false, 'message': 'فشلت الاستعادة: $e'};
    }
  }

  /// استعادة البيانات الشاملة إلى قاعدة البيانات المحلية
  Future<void> _restoreDataToDatabase(Map<String, dynamic> backupData) async {
    final data = backupData['data'] as Map<String, dynamic>;
    debugPrint('[ENHANCED_SYNC] بدء استعادة البيانات الشاملة...');

    // مسح البيانات الحالية
    await _clearAllData();

    // استعادة المشتركين
    if (data.containsKey('subscribers')) {
      final subscribersData = data['subscribers'] as List<dynamic>;
      for (final subscriberMap in subscribersData) {
        final subscriber = Subscriber.fromMap(
          Map<String, dynamic>.from(subscriberMap),
        );
        await _dbHelper.insertSubscriber(subscriber);
      }
      debugPrint('[ENHANCED_SYNC] تم استعادة ${subscribersData.length} مشترك');
    }

    // استعادة المعاملات
    if (data.containsKey('transactions')) {
      final transactionsData = data['transactions'] as List<dynamic>;
      for (final transactionMap in transactionsData) {
        final transaction = app_transaction.Transaction.fromMap(
          Map<String, dynamic>.from(transactionMap),
        );
        await _dbHelper.insertTransaction(transaction);
      }
      debugPrint(
        '[ENHANCED_SYNC] تم استعادة ${transactionsData.length} معاملة',
      );
    }

    // استعادة بيانات الباقات/الاشتراكات
    if (data.containsKey('subscriptions')) {
      await _restoreSubscriptions(data['subscriptions'] as List<dynamic>);
    }

    // استعادة بيانات الأجهزة
    if (data.containsKey('devices')) {
      await _restoreDevices(data['devices'] as List<dynamic>);
    }

    // استعادة بيانات السيرفرات
    if (data.containsKey('servers')) {
      await _restoreServers(data['servers'] as List<dynamic>);
    }

    // استعادة بيانات حالة الحساب
    if (data.containsKey('account_status')) {
      await _restoreAccountStatus(data['account_status'] as List<dynamic>);
    }

    // استعادة إعدادات التطبيق
    if (data.containsKey('settings')) {
      await _restoreSettings(data['settings'] as List<dynamic>);
    }

    // استعادة معلومات اللوحات (مع حماية كلمات المرور)
    if (data.containsKey('boards')) {
      await _restoreBoards(data['boards'] as List<dynamic>);
    }

    // استعادة إعدادات SharedPreferences
    if (data.containsKey('shared_preferences')) {
      await _restoreSharedPreferences(
        data['shared_preferences'] as Map<String, dynamic>,
      );
    }

    // معلومات الملفات (للمرجع فقط - لا يتم استعادة الملفات الفعلية)
    if (data.containsKey('file_info')) {
      final fileInfo = data['file_info'] as List<dynamic>;
      debugPrint(
        '[ENHANCED_SYNC] معلومات ${fileInfo.length} ملف متوفرة في النسخة الاحتياطية',
      );
    }

    debugPrint('[ENHANCED_SYNC] تمت استعادة جميع البيانات الشاملة بنجاح');
  }

  /// مسح جميع البيانات من قاعدة البيانات الشاملة
  Future<void> _clearAllData() async {
    final db = await _dbHelper.database;

    try {
      // حذف جميع المشتركين
      await db.delete('subscribers');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول المشتركين');

      // حذف جميع المعاملات
      await db.delete('transactions');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول المعاملات');

      // حذف جميع الباقات/الاشتراكات
      await db.delete('subscriptions');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول الباقات');

      // حذف جميع الأجهزة
      await db.delete('devices');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول الأجهزة');

      // حذف جميع السيرفرات
      await db.delete('servers');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول السيرفرات');

      // حذف جميع بيانات حالة الحساب
      await db.delete('account_status');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول حالة الحساب');

      // حذف جميع الإعدادات
      await db.delete('settings');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول الإعدادات');

      // حذف جميع اللوحات
      await db.delete('boards');
      debugPrint('[ENHANCED_SYNC] تم مسح جدول اللوحات');

      debugPrint('✅ تم مسح جميع البيانات من قاعدة البيانات المحلية');
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في مسح البيانات: $e');
      // المتابعة حتى لو فشل مسح بعض الجداول
    }
  }

  /// حذف نسخة احتياطية
  Future<Map<String, dynamic>> deleteBackup(String backupId) async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // الحصول على معلومات النسخة
      final backupInfo = await _supabase
          .from('compressed_backups')
          .select('*')
          .eq('id', backupId)
          .eq('user_id', user.id)
          .single();

      // حذف الملف من Storage
      await _supabase.storage.from('userbackups').remove([
        backupInfo['file_path'],
      ]);

      // حذف السجل من قاعدة البيانات
      await _supabase.from('compressed_backups').delete().eq('id', backupId);

      return {'success': true, 'message': 'تم حذف النسخة الاحتياطية بنجاح'};
    } catch (e) {
      return {'success': false, 'message': 'فشل حذف النسخة: $e'};
    }
  }

  /// جمع بيانات الباقات/الاشتراكات من قاعدة البيانات
  Future<List<Map<String, dynamic>>> _getAllSubscriptions() async {
    try {
      final db = await _dbHelper.database;
      final subscriptions = await db.query('subscriptions');
      debugPrint(
        '[ENHANCED_SYNC] تم جمع ${subscriptions.length} باقة من قاعدة البيانات',
      );
      return subscriptions;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع بيانات الباقات: $e');
      return [];
    }
  }

  /// جمع بيانات الأجهزة من قاعدة البيانات
  Future<List<Map<String, dynamic>>> _getAllDevices() async {
    try {
      final db = await _dbHelper.database;
      final devices = await db.query('devices');
      debugPrint(
        '[ENHANCED_SYNC] تم جمع ${devices.length} جهاز من قاعدة البيانات',
      );
      return devices;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع بيانات الأجهزة: $e');
      return [];
    }
  }

  /// جمع بيانات السيرفرات من قاعدة البيانات (مع حماية كلمات المرور)
  Future<List<Map<String, dynamic>>> _getAllServers() async {
    try {
      final db = await _dbHelper.database;
      final servers = await db.query('servers');

      // إزالة كلمات المرور الحساسة من النسخة الاحتياطية لأسباب أمنية
      final safeServersData = servers.map((server) {
        final safeServer = Map<String, dynamic>.from(server);
        // الاحتفاظ بالمعلومات المهمة مع إخفاء كلمات المرور
        if (safeServer.containsKey('pass')) {
          safeServer['pass'] = '***ENCRYPTED***';
        }
        return safeServer;
      }).toList();

      debugPrint(
        '[ENHANCED_SYNC] تم جمع ${safeServersData.length} سيرفر (مع حماية كلمات المرور)',
      );
      return safeServersData;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع بيانات السيرفرات: $e');
      return [];
    }
  }

  /// جمع بيانات حالة الحساب من قاعدة البيانات (مع تشفير البيانات الحساسة)
  Future<List<Map<String, dynamic>>> _getAllAccountStatus() async {
    try {
      final db = await _dbHelper.database;
      final accountStatus = await db.query('account_status');

      // تشفير البيانات الحساسة من النسخة الاحتياطية لأسباب أمنية
      final safeAccountStatusData = accountStatus.map((status) {
        final safeStatus = Map<String, dynamic>.from(status);
        // تشفير البيانات الحساسة
        if (safeStatus.containsKey('expiry_millis')) {
          safeStatus['expiry_millis'] = '***ENCRYPTED***';
        }
        if (safeStatus.containsKey('subscription_end')) {
          safeStatus['subscription_end'] = '***ENCRYPTED***';
        }
        return safeStatus;
      }).toList();

      debugPrint(
        '[ENHANCED_SYNC] تم جمع ${safeAccountStatusData.length} حالة حساب (مع تشفير البيانات الحساسة)',
      );
      return safeAccountStatusData;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع بيانات حالة الحساب: $e');
      return [];
    }
  }

  /// جمع إعدادات التطبيق من قاعدة البيانات
  Future<List<Map<String, dynamic>>> _getAllSettings() async {
    try {
      final db = await _dbHelper.database;
      final settings = await db.query('settings');
      debugPrint(
        '[ENHANCED_SYNC] تم جمع ${settings.length} إعداد من قاعدة البيانات',
      );
      return settings;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع إعدادات التطبيق: $e');
      return [];
    }
  }

  /// جمع معلومات اللوحات/السيرفرات من قاعدة البيانات
  Future<List<Map<String, dynamic>>> _getAllBoards() async {
    try {
      final db = await _dbHelper.database;
      final boards = await db.query('boards');

      // إزالة كلمات المرور الحساسة من النسخة الاحتياطية لأسباب أمنية
      final safeBoardsData = boards.map((board) {
        final safeBoard = Map<String, dynamic>.from(board);
        // الاحتفاظ بالمعلومات المهمة مع إخفاء كلمات المرور
        if (safeBoard.containsKey('pass')) {
          safeBoard['pass'] = '***ENCRYPTED***';
        }
        if (safeBoard.containsKey('token')) {
          safeBoard['token'] = '***ENCRYPTED***';
        }
        return safeBoard;
      }).toList();

      debugPrint(
        '[ENHANCED_SYNC] تم جمع ${safeBoardsData.length} لوحة (مع حماية كلمات المرور)',
      );
      return safeBoardsData;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع معلومات اللوحات: $e');
      return [];
    }
  }

  /// جمع إعدادات SharedPreferences
  Future<Map<String, dynamic>> _getSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final prefsData = <String, dynamic>{};

      for (final key in keys) {
        // تجنب حفظ البيانات الحساسة
        if (key.toLowerCase().contains('password') ||
            key.toLowerCase().contains('token') ||
            key.toLowerCase().contains('secret')) {
          prefsData[key] = '***ENCRYPTED***';
          continue;
        }

        // استخدام reflection للحصول على القيمة الفعلية
        try {
          // الحصول على القيمة الخام من SharedPreferences
          final rawValue = prefs.get(key);
          if (rawValue != null) {
            prefsData[key] = rawValue;
          } else {
            debugPrint('[ENHANCED_SYNC] قيمة فارغة للمفتاح: $key');
            prefsData[key] = null;
          }
        } catch (e) {
          debugPrint('[ENHANCED_SYNC] خطأ في قراءة المفتاح $key: $e');
          prefsData[key] = 'ERROR_READING_VALUE';
        }
      }

      debugPrint(
        '[ENHANCED_SYNC] تم جمع ${prefsData.length} إعداد من SharedPreferences',
      );
      return prefsData;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع إعدادات SharedPreferences: $e');
      return {};
    }
  }

  /// جمع معلومات الملفات (بدون المحتوى الفعلي)
  Future<List<Map<String, dynamic>>> _getFileInformation() async {
    try {
      final fileInfoList = <Map<String, dynamic>>[];

      // الحصول على مجلد المستندات
      final documentsDir = await getApplicationDocumentsDirectory();

      // فحص الملفات المهمة
      final importantFiles = [
        'profile_image.jpg',
        'app_config.json',
        'user_data.json',
        'cache_data.json',
      ];

      for (final fileName in importantFiles) {
        final file = File('${documentsDir.path}/$fileName');
        if (await file.exists()) {
          final stat = await file.stat();
          fileInfoList.add({
            'name': fileName,
            'path': file.path,
            'size': stat.size,
            'modified': stat.modified.toIso8601String(),
            'type': fileName.split('.').last,
            'exists': true,
          });
        } else {
          fileInfoList.add({
            'name': fileName,
            'path': file.path,
            'size': 0,
            'modified': null,
            'type': fileName.split('.').last,
            'exists': false,
          });
        }
      }

      // فحص مجلد النسخ الاحتياطية المحلية
      final backupDir = Directory('${documentsDir.path}/local_backups');
      if (await backupDir.exists()) {
        final backupFiles = await backupDir.list().toList();
        for (final entity in backupFiles) {
          if (entity is File) {
            final stat = await entity.stat();
            fileInfoList.add({
              'name': entity.path.split('/').last,
              'path': entity.path,
              'size': stat.size,
              'modified': stat.modified.toIso8601String(),
              'type': 'backup',
              'exists': true,
            });
          }
        }
      }

      debugPrint('[ENHANCED_SYNC] تم جمع معلومات ${fileInfoList.length} ملف');
      return fileInfoList;
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في جمع معلومات الملفات: $e');
      return [];
    }
  }

  /// استعادة بيانات الباقات/الاشتراكات
  Future<void> _restoreSubscriptions(List<dynamic> subscriptionsData) async {
    try {
      final db = await _dbHelper.database;

      for (final subscriptionMap in subscriptionsData) {
        final subscription = Map<String, dynamic>.from(subscriptionMap);
        await db.insert(
          'subscriptions',
          subscription,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      debugPrint('[ENHANCED_SYNC] تم استعادة ${subscriptionsData.length} باقة');
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في استعادة بيانات الباقات: $e');
    }
  }

  /// استعادة بيانات الأجهزة
  Future<void> _restoreDevices(List<dynamic> devicesData) async {
    try {
      final db = await _dbHelper.database;

      for (final deviceMap in devicesData) {
        final device = Map<String, dynamic>.from(deviceMap);
        await db.insert(
          'devices',
          device,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      debugPrint('[ENHANCED_SYNC] تم استعادة ${devicesData.length} جهاز');
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في استعادة بيانات الأجهزة: $e');
    }
  }

  /// استعادة بيانات السيرفرات (مع تجاهل كلمات المرور المشفرة)
  Future<void> _restoreServers(List<dynamic> serversData) async {
    try {
      final db = await _dbHelper.database;

      for (final serverMap in serversData) {
        final server = Map<String, dynamic>.from(serverMap);

        // تجاهل كلمات المرور المشفرة وعدم استعادتها
        if (server['pass'] == '***ENCRYPTED***') {
          server.remove('pass');
        }

        await db.insert(
          'servers',
          server,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      debugPrint('[ENHANCED_SYNC] تم استعادة ${serversData.length} سيرفر');
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في استعادة بيانات السيرفرات: $e');
    }
  }

  /// استعادة بيانات حالة الحساب (مع تجاهل البيانات المشفرة)
  Future<void> _restoreAccountStatus(List<dynamic> accountStatusData) async {
    try {
      final db = await _dbHelper.database;

      for (final statusMap in accountStatusData) {
        final status = Map<String, dynamic>.from(statusMap);

        // تجاهل البيانات المشفرة وعدم استعادتها
        if (status['expiry_millis'] == '***ENCRYPTED***') {
          status.remove('expiry_millis');
        }
        if (status['subscription_end'] == '***ENCRYPTED***') {
          status.remove('subscription_end');
        }

        await db.insert(
          'account_status',
          status,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      debugPrint(
        '[ENHANCED_SYNC] تم استعادة ${accountStatusData.length} حالة حساب',
      );
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في استعادة بيانات حالة الحساب: $e');
    }
  }

  /// استعادة إعدادات التطبيق
  Future<void> _restoreSettings(List<dynamic> settingsData) async {
    try {
      final db = await _dbHelper.database;

      for (final settingMap in settingsData) {
        final setting = Map<String, dynamic>.from(settingMap);
        await db.insert(
          'settings',
          setting,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      debugPrint('[ENHANCED_SYNC] تم استعادة ${settingsData.length} إعداد');
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في استعادة إعدادات التطبيق: $e');
    }
  }

  /// استعادة معلومات اللوحات (مع تجاهل كلمات المرور المشفرة)
  Future<void> _restoreBoards(List<dynamic> boardsData) async {
    try {
      final db = await _dbHelper.database;

      for (final boardMap in boardsData) {
        final board = Map<String, dynamic>.from(boardMap);

        // تجاهل كلمات المرور المشفرة وعدم استعادتها
        if (board['pass'] == '***ENCRYPTED***') {
          board.remove('pass');
        }
        if (board['token'] == '***ENCRYPTED***') {
          board.remove('token');
        }

        await db.insert(
          'boards',
          board,
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }

      debugPrint(
        '[ENHANCED_SYNC] تم استعادة ${boardsData.length} لوحة (مع حماية كلمات المرور)',
      );
    } catch (e) {
      debugPrint('[ENHANCED_SYNC] خطأ في استعادة معلومات اللوحات: $e');
    }
  }

  /// استعادة إعدادات SharedPreferences
  Future<void> _restoreSharedPreferences(Map<String, dynamic> prefsData) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      for (final entry in prefsData.entries) {
        final key = entry.key;
        final value = entry.value;

        // تجاهل البيانات المشفرة
        if (value == '***ENCRYPTED***' || value == 'ERROR_READING_VALUE') {
          continue;
        }

        // استعادة القيم حسب نوعها
        if (value is String) {
          await prefs.setString(key, value);
        } else if (value is int) {
          await prefs.setInt(key, value);
        } else if (value is double) {
          await prefs.setDouble(key, value);
        } else if (value is bool) {
          await prefs.setBool(key, value);
        } else if (value is List<String>) {
          await prefs.setStringList(key, value);
        } else if (value is List) {
          // محاولة تحويل القائمة إلى قائمة نصوص
          final stringList = value.map((e) => e.toString()).toList();
          await prefs.setStringList(key, stringList);
        }
      }

      debugPrint(
        '[ENHANCED_SYNC] تم استعادة ${prefsData.length} إعداد من SharedPreferences',
      );
    } catch (e) {
      debugPrint(
        '[ENHANCED_SYNC] خطأ في استعادة إعدادات SharedPreferences: $e',
      );
    }
  }
}
