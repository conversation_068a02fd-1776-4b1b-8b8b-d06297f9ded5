import 'package:flutter/material.dart';
import '../../boards_list_screen.dart';

class SourcesScreen extends StatefulWidget {
  const SourcesScreen({super.key});

  @override
  State<SourcesScreen> createState() => _SourcesScreenState();
}

class _SourcesScreenState extends State<SourcesScreen> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          Safe<PERSON>rea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // قسم المصادر
                  _buildSourcesSection(colorScheme, isDark),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.18),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withValues(
              alpha: isDark ? 0.08 : 0.18,
            ),
            child: Icon(Icons.source, color: colorScheme.primary, size: 54),
          ),
        ),
        // عنوان الشاشة
        Text(
          'المصادر',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'اختر مصدر البيانات لجلب معلومات المشتركين',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء قسم المصادر
  Widget _buildSourcesSection(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // بطاقة SAS
        _buildSourceCard(
          colorScheme: colorScheme,
          isDark: isDark,
          icon: Icons.dns,
          title: 'SAS4',
          subtitle: 'نظام إدارة المشتركين',
          isEnabled: true,
          onTap: () {
            Navigator.of(
              context,
            ).push(MaterialPageRoute(builder: (_) => BoardsListScreen()));
          },
        ),

        const SizedBox(height: 16),

        // بطاقة السيرفر
        _buildSourceCard(
          colorScheme: colorScheme,
          isDark: isDark,
          icon: Icons.storage,
          title: 'سيرفر',
          subtitle: 'قريباً - قيد التطوير',
          isEnabled: false,
          onTap: () => _showComingSoonMessage(),
        ),

        const SizedBox(height: 16),

        // بطاقة Earthlink
        _buildSourceCard(
          colorScheme: colorScheme,
          isDark: isDark,
          icon: Icons.language,
          title: 'Earthlink',
          subtitle: 'قريباً - قيد التطوير',
          isEnabled: false,
          onTap: () => _showComingSoonMessage(),
        ),
      ],
    );
  }

  // بناء بطاقة مصدر واحد
  Widget _buildSourceCard({
    required ColorScheme colorScheme,
    required bool isDark,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isEnabled,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: InkWell(
        borderRadius: BorderRadius.circular(22),
        onTap: isEnabled ? onTap : null,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Row(
            children: [
              // الأيقونة
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: isEnabled
                      ? colorScheme.primary.withValues(alpha: 0.1)
                      : colorScheme.outline.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: isEnabled ? colorScheme.primary : colorScheme.outline,
                ),
              ),

              const SizedBox(width: 20),

              // النص
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isEnabled
                            ? colorScheme.primary
                            : colorScheme.outline,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: isEnabled
                            ? colorScheme.onSurface.withValues(alpha: 0.7)
                            : colorScheme.outline.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),

              // مؤشر التفعيل
              if (!isEnabled)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.orange.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    'قريباً',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              else
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: colorScheme.primary,
                ),
            ],
          ),
        ),
      ),
    );
  }

  // إظهار رسالة "قريباً"
  void _showComingSoonMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('هذه الميزة قيد التطوير'),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
