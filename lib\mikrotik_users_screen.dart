import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:dartssh2/dartssh2.dart';
import 'features/servers/data/server_model.dart';
import 'features/servers/data/server_storage.dart';
import 'dart:typed_data';
import 'dart:async';

class MikroTikUsersScreen extends StatefulWidget {
  const MikroTikUsersScreen({super.key});

  @override
  State<MikroTikUsersScreen> createState() => _MikroTikUsersScreenState();
}

class _MikroTikUsersScreenState extends State<MikroTikUsersScreen> {
  List<Map<String, String>> users = [];
  bool loading = false;

  Future<void> fetchUsers() async {
    setState(() => loading = true);
    final server = await getLastServer();
    if (server == null) {
      setState(() => loading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لم يتم العثور على أي سيرفر!')),
      );
      return;
    }
    final storage = ServerStorage();
    try {
      final socket = await SSHSocket.connect(server.ip, 22);
      final client = SSHClient(
        socket,
        username: server.user,
        onPasswordRequest: () => server.pass,
      );
      final session = await client.execute(
        'export terse /ip/hotspot/user print',
      );
      final output = await session.stdout
          .transform(
            StreamTransformer<Uint8List, String>.fromHandlers(
              handleData: (data, sink) => sink.add(utf8.decode(data)),
            ),
          )
          .join();
      client.close();
      // Parse output: each user in a line, fields separated by spaces
      final lines = output
          .split('\n')
          .where((l) => l.trim().isNotEmpty)
          .toList();
      final parsedUsers = <Map<String, String>>[];
      for (final line in lines) {
        final fields = line.split(' ');
        final user = <String, String>{};
        for (final field in fields) {
          final kv = field.split('=');
          if (kv.length == 2) {
            user[kv[0]] = kv[1];
          }
        }
        if (user.isNotEmpty) parsedUsers.add(user);
      }
      setState(() => users = parsedUsers);
      // تحديث حالة الاتصال
      final servers = await storage.loadServers();
      final idx = servers.indexWhere(
        (s) => s.ip == server.ip && s.user == server.user,
      );
      if (idx != -1) {
        servers[idx].connected = true;
        await storage.saveServers(servers);
      }
    } catch (e) {
      setState(() => users = []);
      // تحديث حالة الاتصال
      final servers = await storage.loadServers();
      final idx = servers.indexWhere(
        (s) => s.ip == server.ip && s.user == server.user,
      );
      if (idx != -1) {
        servers[idx].connected = false;
        await storage.saveServers(servers);
      }
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('فشل الاتصال بالسيرفر: $e')));
    }
    setState(() => loading = false);
  }

  Future<ServerModel?> getLastServer() async {
    final storage = ServerStorage();
    final servers = await storage.loadServers();
    if (servers.isNotEmpty) {
      return servers.last;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('مستخدمو MikroTik')),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : users.isEmpty
          ? const Center(child: Text('لا يوجد بيانات'))
          : ListView.builder(
              itemCount: users.length,
              itemBuilder: (context, i) {
                final user = users[i];
                return ListTile(
                  title: Text(user['name'] ?? ''),
                  subtitle: Text('Profile:  ${user['profile'] ?? ''}'),
                );
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: fetchUsers,
        child: const Icon(Icons.refresh),
      ),
    );
  }
}
