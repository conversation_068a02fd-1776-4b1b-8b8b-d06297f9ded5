import 'package:flutter/material.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // خلفية متدرجة عصرية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [
                        colorScheme.primary.withValues(alpha: 0.9),
                        colorScheme.surface.withValues(alpha: 0.85),
                      ]
                    : [colorScheme.primary, colorScheme.surface],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
          ),
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              child: <PERSON>umn(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس الشاشة
                  _buildHeader(colorScheme, isDark),
                  const SizedBox(height: 32),

                  // محتوى السياسة
                  _buildPolicyContent(colorScheme, isDark),

                  const SizedBox(height: 32),

                  // تذييل الشاشة
                  _buildFooter(colorScheme, isDark),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // بناء رأس الشاشة
  Widget _buildHeader(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        // شعار دائري عصري
        Container(
          margin: const EdgeInsets.only(bottom: 18),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: colorScheme.primary.withValues(alpha: 0.18),
                blurRadius: 24,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: CircleAvatar(
            radius: 48,
            backgroundColor: Colors.white.withValues(
              alpha: isDark ? 0.08 : 0.18,
            ),
            child: Icon(
              Icons.privacy_tip,
              color: colorScheme.primary,
              size: 54,
            ),
          ),
        ),
        // عنوان الشاشة
        Text(
          'سياسة الخصوصية',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
            letterSpacing: 1,
            shadows: [
              Shadow(
                color: colorScheme.shadow.withValues(alpha: 0.13),
                blurRadius: 4,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'آخر تحديث في: 22 تموز 2025',
          style: TextStyle(
            fontSize: 16,
            color: colorScheme.onPrimary.withValues(alpha: 0.92),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // بناء محتوى السياسة
  Widget _buildPolicyContent(ColorScheme colorScheme, bool isDark) {
    return Column(
      children: [
        _buildModernSection(
          'المقدمة',
          'مرحباً بك في تطبيق "iTower"! نحن نهتم بخصوصيتك ونريد أن تعرف كيف نتعامل مع بياناتك. في هذه الصفحة ستجد شرحاً بسيطاً لكيفية جمع واستخدام وحماية معلوماتك أثناء استخدام التطبيق، وما هي حقوقك وكيف تتواصل معنا إذا احتجت أي مساعدة.',
          Icons.info_outline,
          colorScheme,
          isDark,
        ),
        const SizedBox(height: 16),

        _buildModernSection(
          'ما هي المعلومات التي نجمعها؟',
          'نجمع بعض المعلومات الأساسية فقط لكي يعمل التطبيق بشكل جيد، مثل:\n• اسمك ورقم هاتفك وبيانات حسابك.\n• معلومات عن المشتركين مثل الاسم ورقم الهاتف والعنوان والديون.\n• تفاصيل العمليات المالية مثل الديون والمدفوعات والمصروفات.\n• كيف تستخدم التطبيق (لتحسين الخدمة).',
          Icons.list_alt,
          colorScheme,
          isDark,
        ),
        const SizedBox(height: 16),

        _buildModernSection(
          'كيف نستخدم معلوماتك؟',
          'نستخدم المعلومات التي نجمعها فقط لتشغيل التطبيق وتقديم الخدمة لك، مثل:\n• إدارة حسابك ومعاملاتك.\n• تحسين التطبيق وتطويره.\n• التواصل معك إذا كان هناك تحديثات أو تغييرات مهمة.\n• تحليل الاستخدام لنقدم لك تجربة أفضل.',
          Icons.settings,
          colorScheme,
          isDark,
        ),
        const SizedBox(height: 16),

        _buildModernSection(
          'أين نخزن بياناتك؟',
          'نخزن بياناتك على خوادم آمنة تابعة لخدمة Google Firebase. هذه الخدمة تلتزم بسياسات حماية البيانات العالمية. نحن نبذل جهدنا لحماية معلوماتك من أي وصول غير مصرح به أو استخدام خاطئ.',
          Icons.cloud,
          colorScheme,
          isDark,
        ),
        const SizedBox(height: 16),

        _buildModernSection(
          'هل نشارك معلوماتك مع أحد؟',
          'لا نبيع أو نشارك بياناتك مع أي جهة خارجية إلا في حالات نادرة جداً مثل:\n• إذا وافقت أنت بشكل واضح.\n• إذا طلب القانون ذلك.\n• إذا كان ذلك ضرورياً لحماية حقوقنا أو حقوق الآخرين أو سلامتك.',
          Icons.share,
          colorScheme,
          isDark,
        ),
        const SizedBox(height: 16),

        _buildModernSection(
          'حقوقك',
          'يحق لك دائماً أن تعرف ما هي المعلومات التي نحتفظ بها عنك، ويمكنك طلب تعديلها أو حذفها في أي وقت. إذا كان لديك أي اعتراض على طريقة استخدامنا لمعلوماتك، يمكنك التواصل معنا بسهولة.',
          Icons.verified_user,
          colorScheme,
          isDark,
        ),
        const SizedBox(height: 16),

        _buildModernSection(
          'هل تتغير هذه السياسة؟',
          'قد نقوم بتحديث سياسة الخصوصية من وقت لآخر. إذا حدث أي تغيير مهم سنخبرك بذلك داخل التطبيق، وستجد تاريخ آخر تحديث في أعلى هذه الصفحة.',
          Icons.update,
          colorScheme,
          isDark,
        ),
        const SizedBox(height: 16),

        _buildModernSection(
          'كيف تتواصل معنا؟',
          'إذا كان لديك أي سؤال أو استفسار بخصوص خصوصيتك أو بياناتك، يمكنك التواصل معنا في أي وقت عبر البريد الإلكتروني التالي:\<EMAIL>',
          Icons.email,
          colorScheme,
          isDark,
        ),
      ],
    );
  }

  // بناء قسم عصري
  Widget _buildModernSection(
    String title,
    String content,
    IconData icon,
    ColorScheme colorScheme,
    bool isDark,
  ) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, color: colorScheme.primary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              content,
              style: TextStyle(
                fontSize: 16,
                height: 1.6,
                color: colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء تذييل الشاشة
  Widget _buildFooter(ColorScheme colorScheme, bool isDark) {
    return Card(
      elevation: 0,
      color: colorScheme.surface.withValues(alpha: isDark ? 0.7 : 0.93),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(22)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Container(
              width: 64,
              height: 4,
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'iTower © 2025',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'جميع الحقوق محفوظة',
              style: TextStyle(
                fontSize: 14,
                color: colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
