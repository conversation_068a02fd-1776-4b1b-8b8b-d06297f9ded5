// مكون حماية الصلاحيات

import 'package:flutter/material.dart';
import '../services/employee_service.dart';
import '../models/employee_models.dart';

/// مكون لحماية المحتوى بناءً على الصلاحيات
class PermissionGuard extends StatelessWidget {
  final PermissionType permission;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackMessage;

  const PermissionGuard({
    super.key,
    required this.permission,
    required this.child,
    this.fallback,
    this.showFallbackMessage = true,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: EmployeeService.hasPermission(permission),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }

        final hasPermission = snapshot.data ?? false;

        if (hasPermission) {
          return child;
        }

        if (fallback != null) {
          return fallback!;
        }

        if (showFallbackMessage) {
          return _buildNoPermissionWidget(context);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildNoPermissionWidget(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.error.withValues(alpha: 0.5)),
      ),
      child: Row(
        children: [
          Icon(Icons.lock, color: colorScheme.error, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'ليس لديك صلاحية للوصول إلى هذا المحتوى',
              style: TextStyle(color: colorScheme.error, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}

/// مكون لحماية الأزرار بناءً على الصلاحيات
class PermissionButton extends StatelessWidget {
  final PermissionType permission;
  final VoidCallback? onPressed;
  final Widget? child;
  final IconData? icon;
  final String? label;
  final ButtonStyle? style;
  final bool isElevated;

  const PermissionButton({
    super.key,
    required this.permission,
    required this.onPressed,
    this.child,
    this.icon,
    this.label,
    this.style,
    this.isElevated = true,
  }) : assert(
         child != null || (icon != null || label != null),
         'Either child or icon/label must be provided',
       );

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: EmployeeService.hasPermission(permission),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return const SizedBox.shrink();
        }

        final buttonChild = child ?? _buildDefaultChild();

        if (isElevated) {
          return ElevatedButton(
            onPressed: onPressed,
            style: style,
            child: buttonChild,
          );
        } else {
          return TextButton(
            onPressed: onPressed,
            style: style,
            child: buttonChild,
          );
        }
      },
    );
  }

  Widget _buildDefaultChild() {
    if (icon != null && label != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [Icon(icon), const SizedBox(width: 8), Text(label!)],
      );
    } else if (icon != null) {
      return Icon(icon);
    } else if (label != null) {
      return Text(label!);
    } else {
      return const SizedBox.shrink();
    }
  }
}

/// مكون لحماية عناصر القائمة بناءً على الصلاحيات
class PermissionListTile extends StatelessWidget {
  final PermissionType permission;
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;

  const PermissionListTile({
    super.key,
    required this.permission,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: EmployeeService.hasPermission(permission),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return const SizedBox.shrink();
        }

        return ListTile(
          leading: leading,
          title: title,
          subtitle: subtitle,
          trailing: trailing,
          onTap: onTap,
        );
      },
    );
  }
}

/// مكون لحماية عناصر القائمة المنبثقة بناءً على الصلاحيات
class PermissionPopupMenuItem<T> extends PopupMenuEntry<T> {
  final PermissionType permission;
  final T value;
  final Widget child;

  const PermissionPopupMenuItem({
    super.key,
    required this.permission,
    required this.value,
    required this.child,
  });

  @override
  State<PermissionPopupMenuItem<T>> createState() =>
      _PermissionPopupMenuItemState<T>();

  @override
  double get height => kMinInteractiveDimension;

  @override
  bool represents(T? value) => value == this.value;
}

class _PermissionPopupMenuItemState<T>
    extends State<PermissionPopupMenuItem<T>> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: EmployeeService.hasPermission(widget.permission),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }

        final hasPermission = snapshot.data ?? false;

        if (!hasPermission) {
          return const SizedBox.shrink();
        }

        return PopupMenuItem<T>(value: widget.value, child: widget.child);
      },
    );
  }
}

/// مكون لعرض معلومات الموظف الحالي
class CurrentEmployeeInfo extends StatelessWidget {
  const CurrentEmployeeInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: EmployeeService.isManagerMode(),
      builder: (context, managerSnapshot) {
        if (managerSnapshot.connectionState == ConnectionState.waiting) {
          return const SizedBox.shrink();
        }

        final isManagerMode = managerSnapshot.data ?? true;

        if (isManagerMode) {
          return _buildManagerInfo(context);
        }

        return FutureBuilder<EmployeeSession?>(
          future: EmployeeService.getCurrentSession(),
          builder: (context, sessionSnapshot) {
            if (sessionSnapshot.connectionState == ConnectionState.waiting) {
              return const SizedBox.shrink();
            }

            final session = sessionSnapshot.data;
            if (session == null) {
              return _buildManagerInfo(context);
            }

            return _buildEmployeeInfo(context, session);
          },
        );
      },
    );
  }

  Widget _buildManagerInfo(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.admin_panel_settings,
            size: 16,
            color: colorScheme.onPrimaryContainer,
          ),
          const SizedBox(width: 4),
          Text(
            'المدير',
            style: TextStyle(
              color: colorScheme.onPrimaryContainer,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmployeeInfo(BuildContext context, EmployeeSession session) {
    final colorScheme = Theme.of(context).colorScheme;

    String roleText;
    IconData roleIcon;

    switch (session.role) {
      case UserRole.manager:
        roleText = 'مدير';
        roleIcon = Icons.admin_panel_settings;
        break;
      case UserRole.employee:
        roleText = 'موظف';
        roleIcon = Icons.person;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(roleIcon, size: 16, color: colorScheme.onSecondaryContainer),
          const SizedBox(width: 4),
          Text(
            '${session.employeeName} ($roleText)',
            style: TextStyle(
              color: colorScheme.onSecondaryContainer,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// دالة مساعدة للتحقق من الصلاحية مع عرض رسالة خطأ
Future<bool> checkPermissionWithMessage(
  BuildContext context,
  PermissionType permission,
) async {
  final hasPermission = await EmployeeService.hasPermission(permission);

  if (!hasPermission && context.mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ليس لديك صلاحية لتنفيذ هذا الإجراء'),
        backgroundColor: Colors.red,
      ),
    );
  }

  return hasPermission;
}
