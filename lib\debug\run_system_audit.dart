import 'package:flutter/foundation.dart';
import '../tools/system_audit_tool.dart';
import '../services/app_migration_service.dart';

/// ملف تشغيل المراجعة الشاملة للنظام
/// يمكن استدعاؤه من أي مكان في التطبيق للتحقق من حالة النظام

/// تشغيل المراجعة الشاملة
Future<void> runFullSystemAudit() async {
  if (!kDebugMode) {
    debugPrint('⚠️ المراجعة الشاملة متاحة فقط في وضع التطوير');
    return;
  }

  debugPrint('🚀 بدء المراجعة الشاملة للنظام...');
  debugPrint('=' * 60);

  try {
    // تشغيل المراجعة الشاملة
    await SystemAuditTool.runFullAudit();

    debugPrint('=' * 60);
    debugPrint('✅ تم الانتهاء من المراجعة الشاملة بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في المراجعة الشاملة: $e');
  }
}

/// تشغيل تنظيف البقايا القديمة فقط
Future<void> runLegacyCleanupOnly() async {
  if (!kDebugMode) {
    debugPrint('⚠️ التنظيف متاح فقط في وضع التطوير');
    return;
  }

  debugPrint('🧹 بدء تنظيف البقايا القديمة...');

  try {
    await SystemAuditTool.runLegacyCleanup();
    debugPrint('✅ تم الانتهاء من التنظيف بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في التنظيف: $e');
  }
}

/// فحص سريع للنظام
Future<void> runQuickSystemCheck() async {
  debugPrint('⚡ فحص سريع للنظام...');

  try {
    await SystemAuditTool.runQuickCheck();
    debugPrint('✅ انتهى الفحص السريع');
  } catch (e) {
    debugPrint('❌ خطأ في الفحص السريع: $e');
  }
}

/// إنشاء تقرير مفصل
Future<String> generateSystemReport() async {
  debugPrint('📋 إنشاء تقرير مفصل للنظام...');

  try {
    final report = await SystemAuditTool.generateReport();
    debugPrint('✅ تم إنشاء التقرير بنجاح');
    return report;
  } catch (e) {
    debugPrint('❌ خطأ في إنشاء التقرير: $e');
    return 'خطأ في إنشاء التقرير: $e';
  }
}

/// تشغيل تهيئة النظام الجديد
Future<void> runSystemMigration() async {
  debugPrint('🔄 بدء تهيئة النظام الجديد...');

  try {
    final success = await AppMigrationService.initializeNewSystem();

    if (success) {
      debugPrint('✅ تم تهيئة النظام الجديد بنجاح');
    } else {
      debugPrint('❌ فشل في تهيئة النظام الجديد');
    }
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة النظام الجديد: $e');
  }
}

/// عرض معلومات النظام
Future<void> showSystemInfo() async {
  debugPrint('ℹ️ معلومات النظام:');
  debugPrint('-' * 30);

  try {
    final systemInfo = await AppMigrationService.getSystemInfo();

    debugPrint(
      '📱 إصدار النظام: ${systemInfo['system_version'] ?? 'غير معروف'}',
    );
    debugPrint(
      '🔄 حالة الترحيل: ${systemInfo['migration_status'] ? 'مكتمل' : 'غير مكتمل'}',
    );
    debugPrint(
      '👤 بيانات الحساب: ${systemInfo['account_data'] != null ? 'موجودة' : 'غير موجودة'}',
    );
    debugPrint('📱 عدد الأجهزة: ${systemInfo['devices_count'] ?? 0}');
    debugPrint(
      '🔄 الجلسة نشطة: ${systemInfo['session_active'] ? 'نعم' : 'لا'}',
    );

    if (systemInfo['live_stats'] != null) {
      final stats = systemInfo['live_stats'];
      debugPrint('📊 إحصائيات فورية:');
      debugPrint('  - إجمالي المستخدمين: ${stats['total_users'] ?? 0}');
      debugPrint('  - المستخدمين النشطين: ${stats['active_users'] ?? 0}');
      debugPrint('  - المستخدمين المتصلين: ${stats['online_users'] ?? 0}');
    }

    if (systemInfo['error'] != null) {
      debugPrint('❌ خطأ: ${systemInfo['error']}');
    }
  } catch (e) {
    debugPrint('❌ خطأ في جلب معلومات النظام: $e');
  }

  debugPrint('-' * 30);
}

/// تشغيل جميع الفحوصات والإجراءات
Future<void> runCompleteSystemCheck() async {
  if (!kDebugMode) {
    debugPrint('⚠️ الفحص الشامل متاح فقط في وضع التطوير');
    return;
  }

  debugPrint('🎯 بدء الفحص الشامل للنظام...');
  debugPrint('=' * 60);

  try {
    // 1. عرض معلومات النظام
    await showSystemInfo();
    debugPrint('');

    // 2. فحص سريع
    await runQuickSystemCheck();
    debugPrint('');

    // 3. تهيئة النظام الجديد
    await runSystemMigration();
    debugPrint('');

    // 4. المراجعة الشاملة
    await runFullSystemAudit();
    debugPrint('');

    // 5. إنشاء تقرير نهائي
    final report = await generateSystemReport();
    debugPrint('📋 التقرير النهائي:');
    debugPrint(report);

    debugPrint('=' * 60);
    debugPrint('🎉 تم الانتهاء من الفحص الشامل بنجاح!');
  } catch (e) {
    debugPrint('❌ خطأ في الفحص الشامل: $e');
  }
}

/// دوال مساعدة للاستدعاء السريع

/// فحص وتنظيف سريع
Future<void> quickCleanup() async {
  await runQuickSystemCheck();
  await runLegacyCleanupOnly();
}

/// تهيئة ومراجعة
Future<void> initAndAudit() async {
  await runSystemMigration();
  await runFullSystemAudit();
}

/// معلومات وتقرير
Future<void> infoAndReport() async {
  await showSystemInfo();
  final report = await generateSystemReport();
  debugPrint('📋 التقرير:\n$report');
}

// مثال على الاستخدام:
// 
// في أي مكان في التطبيق، يمكنك استدعاء:
// 
// import 'debug/run_system_audit.dart';
// 
// // للفحص الشامل
// await runCompleteSystemCheck();
// 
// // للفحص السريع
// await runQuickSystemCheck();
// 
// // للتنظيف فقط
// await runLegacyCleanupOnly();
// 
// // لعرض المعلومات
// await showSystemInfo();
