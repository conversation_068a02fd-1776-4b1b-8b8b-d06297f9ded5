import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:convert';
import 'data_protection_service.dart';

/// خدمة فحص وإدارة تحديثات التطبيق
class AppUpdateService {
  static const String _lastUpdateCheckKey = 'last_update_check';
  static const String _skippedVersionKey = 'skipped_version';
  static const String _currentVersionKey = 'current_app_version';

  /// فحص التحديثات المتاحة
  static Future<UpdateInfo?> checkForUpdates() async {
    try {
      debugPrint('🔍 [UPDATE] فحص التحديثات المتاحة...');

      // جلب معلومات التطبيق الحالي
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final currentBuildNumber = int.parse(packageInfo.buildNumber);

      debugPrint(
        '📱 [UPDATE] الإصدار الحالي: $currentVersion ($currentBuildNumber)',
      );

      // جلب معلومات التحديث من الخادم
      final response = await Supabase.instance.client
          .from('app_updates')
          .select('*')
          .eq('platform', _getPlatform())
          .eq('is_active', true)
          .order('build_number', ascending: false)
          .limit(1)
          .maybeSingle();

      if (response == null) {
        debugPrint('ℹ️ [UPDATE] لا توجد تحديثات متاحة');
        await _saveLastUpdateCheck();
        return null;
      }

      final latestVersion = response['version'] as String;
      final latestBuildNumber = response['build_number'] as int;
      final isForced = response['is_forced'] as bool? ?? false;
      final minCompatibleVersion =
          response['min_compatible_version'] as String?;

      debugPrint('🆕 [UPDATE] أحدث إصدار: $latestVersion ($latestBuildNumber)');

      // فحص إذا كان هناك تحديث متاح
      if (latestBuildNumber <= currentBuildNumber) {
        debugPrint('✅ [UPDATE] التطبيق محدث');
        await _saveLastUpdateCheck();
        return null;
      }

      // فحص إذا كان المستخدم تخطى هذا الإصدار
      if (!isForced && await _isVersionSkipped(latestVersion)) {
        debugPrint('⏭️ [UPDATE] تم تخطي هذا الإصدار مسبقاً');
        return null;
      }

      // فحص التوافق
      final isCompatible = await _checkCompatibility(
        currentVersion,
        minCompatibleVersion,
        response['database_migration_required'] as bool? ?? false,
      );

      final updateInfo = UpdateInfo(
        currentVersion: currentVersion,
        currentBuildNumber: currentBuildNumber,
        latestVersion: latestVersion,
        latestBuildNumber: latestBuildNumber,
        isForced: isForced,
        isCompatible: isCompatible,
        releaseNotes: response['release_notes'] as String? ?? '',
        downloadUrl: response['download_url'] as String? ?? '',
        minCompatibleVersion: minCompatibleVersion,
        databaseMigrationRequired:
            response['database_migration_required'] as bool? ?? false,
        features: List<String>.from(response['features'] ?? []),
        bugFixes: List<String>.from(response['bug_fixes'] ?? []),
        securityUpdates: List<String>.from(response['security_updates'] ?? []),
      );

      await _saveLastUpdateCheck();
      debugPrint('📋 [UPDATE] تحديث متاح: ${updateInfo.toString()}');

      return updateInfo;
    } catch (e) {
      debugPrint('❌ [UPDATE] خطأ في فحص التحديثات: $e');
      return null;
    }
  }

  /// فحص التوافق مع الإصدار الجديد
  static Future<bool> _checkCompatibility(
    String currentVersion,
    String? minCompatibleVersion,
    bool databaseMigrationRequired,
  ) async {
    try {
      // فحص الحد الأدنى للتوافق
      if (minCompatibleVersion != null) {
        final isVersionCompatible =
            _compareVersions(currentVersion, minCompatibleVersion) >= 0;
        if (!isVersionCompatible) {
          debugPrint(
            '⚠️ [UPDATE] الإصدار الحالي غير متوافق - مطلوب $minCompatibleVersion أو أحدث',
          );
          return false;
        }
      }

      // فحص توافق قاعدة البيانات
      if (databaseMigrationRequired) {
        final canMigrate = await _canMigrateDatabase();
        if (!canMigrate) {
          debugPrint('⚠️ [UPDATE] لا يمكن ترحيل قاعدة البيانات');
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('❌ [UPDATE] خطأ في فحص التوافق: $e');
      return false;
    }
  }

  /// فحص إمكانية ترحيل قاعدة البيانات
  static Future<bool> _canMigrateDatabase() async {
    try {
      // فحص وجود بيانات مهمة
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return true;

      // فحص إمكانية الوصول لقاعدة البيانات
      await Supabase.instance.client
          .from('user_accounts')
          .select('user_id')
          .eq('user_id', user.id)
          .limit(1);

      return true;
    } catch (e) {
      debugPrint('❌ [UPDATE] خطأ في فحص قاعدة البيانات: $e');
      return false;
    }
  }

  /// مقارنة الإصدارات (semantic versioning)
  static int _compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();

    for (int i = 0; i < 3; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;

      if (v1Part < v2Part) return -1;
      if (v1Part > v2Part) return 1;
    }

    return 0;
  }

  /// تخطي إصدار معين
  static Future<void> skipVersion(String version) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_skippedVersionKey, version);
      debugPrint('⏭️ [UPDATE] تم تخطي الإصدار: $version');
    } catch (e) {
      debugPrint('❌ [UPDATE] خطأ في تخطي الإصدار: $e');
    }
  }

  /// فحص إذا كان الإصدار مُتخطى
  static Future<bool> _isVersionSkipped(String version) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final skippedVersion = prefs.getString(_skippedVersionKey);
      return skippedVersion == version;
    } catch (e) {
      return false;
    }
  }

  /// حفظ وقت آخر فحص
  static Future<void> _saveLastUpdateCheck() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(
        _lastUpdateCheckKey,
        DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      debugPrint('❌ [UPDATE] خطأ في حفظ وقت الفحص: $e');
    }
  }

  /// فحص إذا كان يحتاج فحص تحديثات
  static Future<bool> shouldCheckForUpdates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastCheck = prefs.getInt(_lastUpdateCheckKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;

      // فحص كل 24 ساعة
      return (now - lastCheck) > (24 * 60 * 60 * 1000);
    } catch (e) {
      return true;
    }
  }

  /// الحصول على منصة التشغيل
  static String _getPlatform() {
    if (defaultTargetPlatform == TargetPlatform.android) {
      return 'android';
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return 'ios';
    } else {
      return 'web';
    }
  }

  /// إنشاء نسخة احتياطية من البيانات قبل التحديث (محدث للاستخدام الخدمة الشاملة)
  static Future<Map<String, dynamic>?> createDataBackup() async {
    try {
      debugPrint('💾 [UPDATE] إنشاء نسخة احتياطية شاملة من البيانات...');

      // استخدام خدمة حماية البيانات الشاملة
      final result = await DataProtectionService.createFullBackup(
        updateVersion: 'unknown', // سيتم تحديثه لاحقاً
        reason: 'app_update_backup',
      );

      if (result.success) {
        debugPrint('✅ [UPDATE] تم إنشاء النسخة الاحتياطية بنجاح');
        return {
          'success': true,
          'backup_id': result.backupId,
          'backup_size': result.backupSize,
          'message': result.message,
        };
      } else {
        debugPrint(
          '❌ [UPDATE] فشل في إنشاء النسخة الاحتياطية: ${result.error}',
        );
        return {
          'success': false,
          'error': result.error,
          'message': result.message,
        };
      }
    } catch (e) {
      debugPrint('❌ [UPDATE] خطأ في إنشاء النسخة الاحتياطية: $e');
      return {
        'success': false,
        'error': e.toString(),
        'message': 'خطأ غير متوقع في إنشاء النسخة الاحتياطية',
      };
    }
  }

  /// استعادة البيانات من النسخة الاحتياطية
  static Future<bool> restoreDataBackup() async {
    try {
      debugPrint('🔄 [UPDATE] استعادة البيانات من النسخة الاحتياطية...');

      final prefs = await SharedPreferences.getInstance();
      final backupString = prefs.getString('data_backup');

      if (backupString == null) {
        debugPrint('⚠️ [UPDATE] لا توجد نسخة احتياطية');
        return false;
      }

      final backup = jsonDecode(backupString) as Map<String, dynamic>;

      // استعادة الإعدادات المحلية
      if (backup['local_settings'] != null) {
        final localSettings = backup['local_settings'] as Map<String, dynamic>;
        for (final entry in localSettings.entries) {
          final key = entry.key;
          final value = entry.value;

          if (value is String) {
            await prefs.setString(key, value);
          } else if (value is int) {
            await prefs.setInt(key, value);
          } else if (value is double) {
            await prefs.setDouble(key, value);
          } else if (value is bool) {
            await prefs.setBool(key, value);
          }
        }
      }

      debugPrint('✅ [UPDATE] تم استعادة البيانات بنجاح');
      return true;
    } catch (e) {
      debugPrint('❌ [UPDATE] خطأ في استعادة البيانات: $e');
      return false;
    }
  }
}

/// معلومات التحديث
class UpdateInfo {
  final String currentVersion;
  final int currentBuildNumber;
  final String latestVersion;
  final int latestBuildNumber;
  final bool isForced;
  final bool isCompatible;
  final String releaseNotes;
  final String downloadUrl;
  final String? minCompatibleVersion;
  final bool databaseMigrationRequired;
  final List<String> features;
  final List<String> bugFixes;
  final List<String> securityUpdates;

  UpdateInfo({
    required this.currentVersion,
    required this.currentBuildNumber,
    required this.latestVersion,
    required this.latestBuildNumber,
    required this.isForced,
    required this.isCompatible,
    required this.releaseNotes,
    required this.downloadUrl,
    this.minCompatibleVersion,
    required this.databaseMigrationRequired,
    required this.features,
    required this.bugFixes,
    required this.securityUpdates,
  });

  @override
  String toString() {
    return 'UpdateInfo(current: $currentVersion, latest: $latestVersion, forced: $isForced, compatible: $isCompatible)';
  }
}
