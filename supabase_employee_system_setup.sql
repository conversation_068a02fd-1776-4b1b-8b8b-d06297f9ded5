-- =====================================================
-- إعداد نظام الموظفين في Supabase
-- تاريخ الإنشاء: 2025-07-29
-- =====================================================

-- 1. جدول الموظفين
CREATE TABLE IF NOT EXISTS employees (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    manager_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    employee_name VARCHAR(255) NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    permissions JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. جدول جلسات الموظفين
CREATE TABLE IF NOT EXISTS employee_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    device_info TEXT,
    login_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. جدول سجل أنشطة الموظفين
CREATE TABLE IF NOT EXISTS employee_activity_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    manager_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    action VARCHAR(255) NOT NULL,
    details TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- إنشاء الفهارس لتحسين الأداء
-- =====================================================

-- فهارس جدول الموظفين
CREATE INDEX IF NOT EXISTS idx_employees_manager_id ON employees(manager_id);
CREATE INDEX IF NOT EXISTS idx_employees_username ON employees(username);
CREATE INDEX IF NOT EXISTS idx_employees_is_active ON employees(is_active);

-- فهارس جدول جلسات الموظفين
CREATE INDEX IF NOT EXISTS idx_employee_sessions_employee_id ON employee_sessions(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_sessions_is_active ON employee_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_employee_sessions_last_activity ON employee_sessions(last_activity);

-- فهارس جدول سجل الأنشطة
CREATE INDEX IF NOT EXISTS idx_employee_activity_logs_employee_id ON employee_activity_logs(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_activity_logs_manager_id ON employee_activity_logs(manager_id);
CREATE INDEX IF NOT EXISTS idx_employee_activity_logs_timestamp ON employee_activity_logs(timestamp);

-- =====================================================
-- إعداد Row Level Security (RLS)
-- =====================================================

-- تفعيل RLS على جميع الجداول
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_activity_logs ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان لجدول الموظفين
-- المدير يمكنه رؤية وإدارة موظفيه فقط
CREATE POLICY "managers_can_manage_their_employees" ON employees
    FOR ALL USING (manager_id = auth.uid());

-- سياسات الأمان لجدول جلسات الموظفين
-- المدير يمكنه رؤية جلسات موظفيه
CREATE POLICY "managers_can_view_employee_sessions" ON employee_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM employees 
            WHERE employees.id = employee_sessions.employee_id 
            AND employees.manager_id = auth.uid()
        )
    );

-- الموظف يمكنه إدارة جلساته الخاصة (للتطبيق)
CREATE POLICY "employees_can_manage_own_sessions" ON employee_sessions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM employees 
            WHERE employees.id = employee_sessions.employee_id 
            AND employees.username = current_setting('app.current_employee_username', true)
        )
    );

-- سياسات الأمان لجدول سجل الأنشطة
-- المدير يمكنه رؤية أنشطة موظفيه
CREATE POLICY "managers_can_view_employee_activities" ON employee_activity_logs
    FOR SELECT USING (manager_id = auth.uid());

-- إدراج الأنشطة (للتطبيق)
CREATE POLICY "allow_activity_logging" ON employee_activity_logs
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM employees 
            WHERE employees.id = employee_activity_logs.employee_id 
            AND employees.manager_id = auth.uid()
        )
    );

-- =====================================================
-- دوال مساعدة
-- =====================================================

-- دالة للتحقق من صحة كلمة المرور
CREATE OR REPLACE FUNCTION verify_employee_password(
    p_username VARCHAR,
    p_password VARCHAR
) RETURNS TABLE(
    employee_id UUID,
    employee_name VARCHAR,
    permissions JSONB,
    manager_id UUID
) 
LANGUAGE plpgsql SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.id,
        e.employee_name,
        e.permissions,
        e.manager_id
    FROM employees e
    WHERE e.username = p_username 
    AND e.password_hash = crypt(p_password, e.password_hash)
    AND e.is_active = true;
END;
$$;

-- دالة لإنشاء موظف جديد
CREATE OR REPLACE FUNCTION create_employee(
    p_manager_id UUID,
    p_employee_name VARCHAR,
    p_username VARCHAR,
    p_password VARCHAR,
    p_permissions JSONB
) RETURNS UUID
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    new_employee_id UUID;
BEGIN
    -- التحقق من أن المدير مسجل دخول
    IF auth.uid() != p_manager_id THEN
        RAISE EXCEPTION 'غير مصرح لك بإنشاء موظفين';
    END IF;
    
    -- إنشاء الموظف
    INSERT INTO employees (
        manager_id,
        employee_name,
        username,
        password_hash,
        permissions
    ) VALUES (
        p_manager_id,
        p_employee_name,
        p_username,
        crypt(p_password, gen_salt('bf')),
        p_permissions
    ) RETURNING id INTO new_employee_id;
    
    RETURN new_employee_id;
END;
$$;

-- دالة لتسجيل نشاط الموظف
CREATE OR REPLACE FUNCTION log_employee_activity(
    p_employee_id UUID,
    p_action VARCHAR,
    p_details TEXT
) RETURNS VOID
LANGUAGE plpgsql SECURITY DEFINER
AS $$
DECLARE
    v_manager_id UUID;
BEGIN
    -- الحصول على معرف المدير
    SELECT manager_id INTO v_manager_id
    FROM employees
    WHERE id = p_employee_id;
    
    -- إدراج النشاط
    INSERT INTO employee_activity_logs (
        employee_id,
        manager_id,
        action,
        details
    ) VALUES (
        p_employee_id,
        v_manager_id,
        p_action,
        p_details
    );
END;
$$;

-- =====================================================
-- تحديث الطوابع الزمنية تلقائياً
-- =====================================================

-- دالة لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إضافة trigger لجدول الموظفين
CREATE TRIGGER update_employees_updated_at 
    BEFORE UPDATE ON employees 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- بيانات تجريبية (اختيارية)
-- =====================================================

-- يمكنك إضافة بيانات تجريبية هنا إذا أردت
-- مثال:
-- INSERT INTO employees (manager_id, employee_name, username, password_hash, permissions)
-- VALUES (
--     'your-manager-uuid-here',
--     'موظف تجريبي',
--     'test_employee',
--     crypt('password123', gen_salt('bf')),
--     '{"viewSubscribers": true, "addSubscribers": true}'::jsonb
-- );

-- =====================================================
-- انتهاء الإعداد
-- =====================================================

-- رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE 'تم إعداد نظام الموظفين بنجاح في Supabase! 🎉';
    RAISE NOTICE 'الجداول المُنشأة:';
    RAISE NOTICE '- employees (الموظفين)';
    RAISE NOTICE '- employee_sessions (جلسات الموظفين)';
    RAISE NOTICE '- employee_activity_logs (سجل أنشطة الموظفين)';
    RAISE NOTICE 'الدوال المُنشأة:';
    RAISE NOTICE '- verify_employee_password()';
    RAISE NOTICE '- create_employee()';
    RAISE NOTICE '- log_employee_activity()';
END $$;
